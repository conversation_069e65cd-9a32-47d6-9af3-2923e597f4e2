'use client';

import { useState, useEffect, useCallback, useRef } from 'react';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle
} from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Badge } from '@/components/ui/badge';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow
} from '@/components/ui/table';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger
} from '@/components/ui/dropdown-menu';
import {
  Building2,
  Plus,
  Search,
  MoreHorizontal,
  Edit,
  Trash2,
  Users,
  Loader2
} from 'lucide-react';
import Link from 'next/link';
import { useToast } from '@/hooks/use-toast';

interface Institution {
  id: number;
  name: string;
  type: string;
  subscription_plan: string;
  billing_cycle: string;
  payment_status: string;
  payment_due_date: string;
  student_count: number;
  teacher_count: number;
  created_at: string;
  updated_at: string;
}

export default function InstitutionsPage() {
  const [searchTerm, setSearchTerm] = useState('');
  const [institutions, setInstitutions] = useState<Institution[]>([]);
  const [loading, setLoading] = useState(true);
  const [deleting, setDeleting] = useState<number | null>(null);
  const { toast } = useToast();
  const isInitialMount = useRef(true);

  // Fetch institutions from API - hapus toast dari dependency
  const fetchInstitutions = useCallback(
    async (search: string = '') => {
      try {
        setLoading(true);
        const response = await fetch(
          `/api/institutions?search=${encodeURIComponent(search)}`
        );
        const data = await response.json();

        if (data.success) {
          setInstitutions(data.data.institutions);
        } else {
          toast({
            title: 'Error',
            description: data.error || 'Failed to fetch institutions',
            variant: 'destructive'
          });
        }
      } catch (error) {
        console.error('Error fetching institutions:', error);
        toast({
          title: 'Error',
          description: 'Failed to fetch institutions',
          variant: 'destructive'
        });
      } finally {
        setLoading(false);
      }
    },
    [] // Hapus toast dari dependency untuk mencegah re-creation
  );

  // Delete institution
  const handleDelete = async (id: number) => {
    if (
      !confirm(
        'Are you sure you want to delete this institution? This action cannot be undone.'
      )
    ) {
      return;
    }

    try {
      setDeleting(id);
      const response = await fetch(`/api/institutions/${id}`, {
        method: 'DELETE'
      });
      const data = await response.json();

      if (data.success) {
        toast({
          title: 'Success',
          description: 'Institution deleted successfully'
        });
        // Refresh the list with current search term
        fetchInstitutions(searchTerm);
      } else {
        toast({
          title: 'Error',
          description: data.error || 'Failed to delete institution',
          variant: 'destructive'
        });
      }
    } catch (error) {
      console.error('Error deleting institution:', error);
      toast({
        title: 'Error',
        description: 'Failed to delete institution',
        variant: 'destructive'
      });
    } finally {
      setDeleting(null);
    }
  };

  // Initial fetch on component mount - hapus fetchInstitutions dari dependency
  useEffect(() => {
    fetchInstitutions();
  }, []); // Empty dependency array untuk hanya run sekali saat mount

  // Debounced search effect
  useEffect(() => {
    if (isInitialMount.current) {
      isInitialMount.current = false;
      return;
    }

    const timeoutId = setTimeout(() => {
      fetchInstitutions(searchTerm);
    }, 500);

    return () => clearTimeout(timeoutId);
  }, [searchTerm, fetchInstitutions]);

  const getStatusBadge = (status: string) => {
    return (
      <Badge variant={status === 'paid' ? 'default' : 'destructive'}>
        {status}
      </Badge>
    );
  };

  const getPlanBadge = (plan: string) => {
    const variant =
      plan === 'enterprise'
        ? 'default'
        : plan === 'pro'
          ? 'secondary'
          : 'outline';
    return <Badge variant={variant}>{plan}</Badge>;
  };

  return (
    <div className='space-y-6'>
      <div className='flex items-center justify-between'>
        <div>
          <h1 className='text-3xl font-bold tracking-tight'>Institutions</h1>
          <p className='text-muted-foreground'>
            Manage all educational institutions on the platform
          </p>
        </div>
        <Link href='/dashboard/admin/institutions/new'>
          <Button>
            <Plus className='mr-2 h-4 w-4' />
            Add Institution
          </Button>
        </Link>
      </div>

      <Card>
        <CardHeader>
          <CardTitle>All Institutions</CardTitle>
          <CardDescription>
            View and manage all registered institutions
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className='mb-4 flex items-center space-x-2'>
            <div className='relative flex-1'>
              <Search className='text-muted-foreground absolute top-2.5 left-2 h-4 w-4' />
              <Input
                placeholder='Search institutions...'
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className='pl-8'
              />
            </div>
          </div>

          <div className='rounded-md border'>
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>Institution</TableHead>
                  <TableHead>Type</TableHead>
                  <TableHead>Plan</TableHead>
                  <TableHead>Students/Teachers</TableHead>
                  <TableHead>Payment Status</TableHead>
                  <TableHead>Due Date</TableHead>
                  <TableHead className='w-[70px]'>Actions</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {loading ? (
                  <TableRow>
                    <TableCell colSpan={7} className='py-8 text-center'>
                      <Loader2 className='mx-auto h-6 w-6 animate-spin' />
                      <p className='text-muted-foreground mt-2 text-sm'>
                        Loading institutions...
                      </p>
                    </TableCell>
                  </TableRow>
                ) : institutions.length === 0 ? (
                  <TableRow>
                    <TableCell colSpan={7} className='py-8 text-center'>
                      <Building2 className='text-muted-foreground mx-auto mb-4 h-12 w-12' />
                      <h3 className='mb-2 text-sm font-semibold'>
                        No institutions found
                      </h3>
                      <p className='text-muted-foreground mb-4 text-sm'>
                        {searchTerm
                          ? 'Try adjusting your search terms.'
                          : 'Get started by adding a new institution.'}
                      </p>
                      {!searchTerm && (
                        <Link href='/dashboard/admin/institutions/new'>
                          <Button>
                            <Plus className='mr-2 h-4 w-4' />
                            Add Institution
                          </Button>
                        </Link>
                      )}
                    </TableCell>
                  </TableRow>
                ) : (
                  institutions.map((institution) => (
                    <TableRow key={institution.id}>
                      <TableCell>
                        <div className='flex items-center space-x-2'>
                          <Building2 className='text-muted-foreground h-4 w-4' />
                          <div>
                            <p className='font-medium'>{institution.name}</p>
                            <p className='text-muted-foreground text-sm'>
                              ID: {institution.id}
                            </p>
                          </div>
                        </div>
                      </TableCell>
                      <TableCell>
                        <Badge variant='outline'>{institution.type}</Badge>
                      </TableCell>
                      <TableCell>
                        <div className='space-y-1'>
                          {getPlanBadge(institution.subscription_plan)}
                          <p className='text-muted-foreground text-xs'>
                            {institution.billing_cycle}
                          </p>
                        </div>
                      </TableCell>
                      <TableCell>
                        <div className='flex items-center space-x-1'>
                          <Users className='text-muted-foreground h-3 w-3' />
                          <span className='text-sm'>
                            {institution.student_count}/
                            {institution.teacher_count}
                          </span>
                        </div>
                      </TableCell>
                      <TableCell>
                        {getStatusBadge(institution.payment_status)}
                      </TableCell>
                      <TableCell>
                        <span className='text-sm'>
                          {institution.payment_due_date
                            ? new Date(
                                institution.payment_due_date
                              ).toLocaleDateString()
                            : 'N/A'}
                        </span>
                      </TableCell>
                      <TableCell>
                        <DropdownMenu>
                          <DropdownMenuTrigger asChild>
                            <Button
                              variant='ghost'
                              className='h-8 w-8 p-0'
                              disabled={deleting === institution.id}
                            >
                              {deleting === institution.id ? (
                                <Loader2 className='h-4 w-4 animate-spin' />
                              ) : (
                                <MoreHorizontal className='h-4 w-4' />
                              )}
                            </Button>
                          </DropdownMenuTrigger>
                          <DropdownMenuContent align='end'>
                            <DropdownMenuItem asChild>
                              <Link
                                href={`/dashboard/admin/institutions/${institution.id}`}
                              >
                                <Edit className='mr-2 h-4 w-4' />
                                Edit
                              </Link>
                            </DropdownMenuItem>
                            <DropdownMenuItem
                              className='text-red-600'
                              onClick={() => handleDelete(institution.id)}
                              disabled={deleting === institution.id}
                            >
                              <Trash2 className='mr-2 h-4 w-4' />
                              Delete
                            </DropdownMenuItem>
                          </DropdownMenuContent>
                        </DropdownMenu>
                      </TableCell>
                    </TableRow>
                  ))
                )}
              </TableBody>
            </Table>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
