{"sorted_middleware": [], "middleware": {"/": {"files": ["server/edge/chunks/_0d3699dc._.js", "server/edge/chunks/[root-of-the-server]__dc15d093._.js", "server/edge/chunks/edge-wrapper_594fdf6a.js"], "name": "middleware", "page": "/", "matchers": [{"regexp": "/:nextData(_next/data/[^/]{1,})?/((?!_next|static|.*\\..*|_static|_vercel).*){(\\\\.json)}?", "originalSource": "/((?!_next|static|.*\\..*|_static|_vercel).*)"}], "wasm": [], "assets": [], "env": {"__NEXT_BUILD_ID": "development", "NEXT_SERVER_ACTIONS_ENCRYPTION_KEY": "oQU80BEb9Ysp0y5Cc6KvygFOTlbLolBab1Eeg0Ic8No=", "__NEXT_PREVIEW_MODE_ID": "beeb3986f959111273dd9431ecdca2f7", "__NEXT_PREVIEW_MODE_ENCRYPTION_KEY": "9db3b5f29ab6febd93a3b1a105666163074f21ee75e0b60540e9848ab99f8083", "__NEXT_PREVIEW_MODE_SIGNING_KEY": "8029bcca8ac3d80eda2b21dba96a10242928aec96f4cd8b1f2732a2db2d75796"}}}, "instrumentation": null, "functions": {}}