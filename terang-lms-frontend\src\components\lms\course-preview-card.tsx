'use client';

import React from 'react';
import { Card } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import {
  BookOpen01Icon as BookOpenIcon,
  UserIcon as UsersIcon,
  ArrowRight01Icon as ArrowRightIcon,
  ShoppingCart01Icon as ShoppingCartIcon,
  DoorLockIcon as LockIcon
} from 'hugeicons-react';
import Image from 'next/image'; // Import the Image component
import { CoursePreviewProps } from '@/types/lms';
import { useFeatureFlags } from '@/lib/feature-flags';

const CoursePreviewCard: React.FC<CoursePreviewProps> = ({
  course,
  onEnroll,
  onPurchase,
  onClick,
  isEnrolled = false
}) => {
  const { canPurchase, canEnrollWithCode } = useFeatureFlags();

  const formatPrice = (price: number, currency: string = 'IDR') => {
    if (currency === 'IDR') {
      return 'Rp' + new Intl.NumberFormat('id-ID').format(price);
    }
    return new Intl.NumberFormat('id-ID', {
      style: 'currency',
      currency: currency
    }).format(price);
  };

  const renderActionButton = () => {
    // If already enrolled, show enrolled status
    if (isEnrolled) {
      return (
        <Button
          onClick={(e) => {
            e.stopPropagation();
            onClick?.();
          }}
          variant="outline"
          className="w-full bg-green-50 border-green-200 text-green-700 hover:bg-green-100"
          disabled
        >
          <BookOpenIcon className="mr-2 h-4 w-4" />
          Sudah Terdaftar
        </Button>
      );
    }

    if (course.previewMode) {
      return (
        <Button
          onClick={(e) => {
            e.stopPropagation();
            onClick?.();
          }}
          variant="outline"
          className="w-full"
        >
          <BookOpenIcon className="mr-2 h-4 w-4" />
          Lihat Detail
          <ArrowRightIcon className="ml-2 h-4 w-4" />
        </Button>
      );
    }

    if (course.isPurchasable && canPurchase) {
      return (
        <div className="flex flex-col gap-2">
          <div className="flex items-center justify-between">
            <span className="text-2xl font-bold text-[var(--iai-primary)]">
              {course.price ? formatPrice(course.price, course.currency) : 'Gratis'}
            </span>
          </div>
          <Button
            onClick={(e) => {
              e.stopPropagation();
              onPurchase?.();
            }}
            variant="iai"
            className="w-full"
          >
            <ShoppingCartIcon className="mr-2 h-4 w-4" />
            Beli Kursus
          </Button>
          {canEnrollWithCode && course.enrollmentCode && (
            <Button
              onClick={(e) => {
                e.stopPropagation();
                onEnroll?.();
              }}
              variant="outline"
              className="w-full"
            >
              <LockIcon className="mr-2 h-4 w-4" />
              Gunakan Kode Pendaftaran
            </Button>
          )}
        </div>
      );
    }

    if (canEnrollWithCode && course.enrollmentCode) {
      return (
        <Button
          onClick={(e) => {
            e.stopPropagation();
            onEnroll?.();
          }}
          className="w-full"
        >
          <LockIcon className="mr-2 h-4 w-4" />
          Daftar Sekarang
        </Button>
      );
    }

    return (
      <Button
        onClick={(e) => {
          e.stopPropagation();
          onClick?.();
        }}
        variant="outline"
        className="w-full"
      >
        <BookOpenIcon className="mr-2 h-4 w-4" />
        Lihat Kursus
      </Button>
    );
  };

  return (
    <Card className="group cursor-pointer transition-all hover:shadow-lg hover:scale-[1.02] overflow-hidden p-0 h-full" onClick={onClick}>
      <div className="flex flex-col h-full">
        {/* Course Image/Thumbnail */}
        <div className="aspect-[4/3] relative overflow-hidden bg-gray-100 flex-shrink-0">
          {course.thumbnail ? (
            <Image 
              src={course.thumbnail} 
              alt={course.name}
              fill
              className="object-cover w-full h-full"
              sizes="(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 33vw"
              priority={false}
            />
          ) : (
            <>
              <div className="w-full h-full bg-gradient-to-br from-blue-500 to-purple-600 flex items-center justify-center">
                <div className="absolute inset-0 bg-black/20" />
                <div className="relative z-10 text-center text-white">
                  <BookOpenIcon className="h-12 w-12 mx-auto mb-1 opacity-80" />
                  <p className="text-xs font-medium">{course.code}</p>
                </div>
              </div>
            </>
          )}
          {course.isPurchasable && (
            <Badge 
              className="absolute top-3 right-3 bg-[var(--iai-primary)] hover:bg-[var(--iai-primary)]"
              variant="default"
            >
              {course.price ? formatPrice(course.price, course.currency) : 'Gratis'}
            </Badge>
          )}
        </div>

        {/* Content area that grows to fill available space */}
        <div className="flex flex-col flex-grow p-4">
          {/* Course Header - Fixed height area */}
          <div className="space-y-1 mb-3 min-h-[60px]">
            <h3 className="text-lg font-bold group-hover:text-blue-600 transition-colors line-clamp-2">
              {course.name}
            </h3>
            <p className="text-gray-600 text-xs line-clamp-2">
              {course.description}
            </p>
          </div>

          {/* Course Meta Information - Fixed height area */}
          <div className="space-y-1 mb-4 text-xs text-gray-600 min-h-[32px]">
            <div className="flex items-center">
              <UsersIcon className="mr-1 h-3 w-3 flex-shrink-0" />
              <span className="line-clamp-1">{course.instructor}</span>
            </div>
            <div className="flex items-center">
              <BookOpenIcon className="mr-1 h-3 w-3 flex-shrink-0" />
              <span>{course.modules.length} modul</span>
            </div>
          </div>

          {/* Spacer to push button to bottom */}
          <div className="flex-grow" />

          {/* Action Button - Always at bottom */}
          <div className="mt-auto">
            {renderActionButton()}
          </div>
        </div>
      </div>
    </Card>
  );
};

export default CoursePreviewCard;