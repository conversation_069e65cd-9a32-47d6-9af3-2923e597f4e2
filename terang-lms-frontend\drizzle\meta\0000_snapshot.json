{"id": "41f2101f-dd2a-40a0-90b9-ec3009c05379", "prevId": "00000000-0000-0000-0000-000000000000", "version": "7", "dialect": "postgresql", "tables": {"public.chapters": {"name": "chapters", "schema": "", "columns": {"id": {"name": "id", "type": "serial", "primaryKey": true, "notNull": true}, "name": {"name": "name", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "content": {"name": "content", "type": "text", "primaryKey": false, "notNull": false}, "module_id": {"name": "module_id", "type": "integer", "primaryKey": false, "notNull": true}, "order_index": {"name": "order_index", "type": "integer", "primaryKey": false, "notNull": true}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {}, "foreignKeys": {"chapters_module_id_modules_id_fk": {"name": "chapters_module_id_modules_id_fk", "tableFrom": "chapters", "tableTo": "modules", "columnsFrom": ["module_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.classes": {"name": "classes", "schema": "", "columns": {"id": {"name": "id", "type": "serial", "primaryKey": true, "notNull": true}, "name": {"name": "name", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "description": {"name": "description", "type": "text", "primaryKey": false, "notNull": false}, "institution_id": {"name": "institution_id", "type": "integer", "primaryKey": false, "notNull": true}, "teacher_id": {"name": "teacher_id", "type": "integer", "primaryKey": false, "notNull": true}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {}, "foreignKeys": {"classes_institution_id_institutions_id_fk": {"name": "classes_institution_id_institutions_id_fk", "tableFrom": "classes", "tableTo": "institutions", "columnsFrom": ["institution_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}, "classes_teacher_id_users_id_fk": {"name": "classes_teacher_id_users_id_fk", "tableFrom": "classes", "tableTo": "users", "columnsFrom": ["teacher_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.course_enrollments": {"name": "course_enrollments", "schema": "", "columns": {"id": {"name": "id", "type": "serial", "primaryKey": true, "notNull": true}, "course_id": {"name": "course_id", "type": "integer", "primaryKey": false, "notNull": true}, "class_id": {"name": "class_id", "type": "integer", "primaryKey": false, "notNull": true}, "enrolled_at": {"name": "enrolled_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {}, "foreignKeys": {"course_enrollments_course_id_courses_id_fk": {"name": "course_enrollments_course_id_courses_id_fk", "tableFrom": "course_enrollments", "tableTo": "courses", "columnsFrom": ["course_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}, "course_enrollments_class_id_classes_id_fk": {"name": "course_enrollments_class_id_classes_id_fk", "tableFrom": "course_enrollments", "tableTo": "classes", "columnsFrom": ["class_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.courses": {"name": "courses", "schema": "", "columns": {"id": {"name": "id", "type": "serial", "primaryKey": true, "notNull": true}, "name": {"name": "name", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "description": {"name": "description", "type": "text", "primaryKey": false, "notNull": false}, "type": {"name": "type", "type": "course_type", "typeSchema": "public", "primaryKey": false, "notNull": true, "default": "'self_paced'"}, "start_date": {"name": "start_date", "type": "timestamp", "primaryKey": false, "notNull": false}, "end_date": {"name": "end_date", "type": "timestamp", "primaryKey": false, "notNull": false}, "teacher_id": {"name": "teacher_id", "type": "integer", "primaryKey": false, "notNull": true}, "institution_id": {"name": "institution_id", "type": "integer", "primaryKey": false, "notNull": true}, "course_code": {"name": "course_code", "type": "<PERSON><PERSON><PERSON>(50)", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {}, "foreignKeys": {"courses_teacher_id_users_id_fk": {"name": "courses_teacher_id_users_id_fk", "tableFrom": "courses", "tableTo": "users", "columnsFrom": ["teacher_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}, "courses_institution_id_institutions_id_fk": {"name": "courses_institution_id_institutions_id_fk", "tableFrom": "courses", "tableTo": "institutions", "columnsFrom": ["institution_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {"courses_course_code_unique": {"name": "courses_course_code_unique", "nullsNotDistinct": false, "columns": ["course_code"]}}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.institutions": {"name": "institutions", "schema": "", "columns": {"id": {"name": "id", "type": "serial", "primaryKey": true, "notNull": true}, "name": {"name": "name", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "type": {"name": "type", "type": "institution_type", "typeSchema": "public", "primaryKey": false, "notNull": true}, "subscription_plan": {"name": "subscription_plan", "type": "subscription_plan", "typeSchema": "public", "primaryKey": false, "notNull": true, "default": "'basic'"}, "billing_cycle": {"name": "billing_cycle", "type": "billing_cycle", "typeSchema": "public", "primaryKey": false, "notNull": true, "default": "'monthly'"}, "payment_status": {"name": "payment_status", "type": "payment_status", "typeSchema": "public", "primaryKey": false, "notNull": true, "default": "'unpaid'"}, "payment_due_date": {"name": "payment_due_date", "type": "timestamp", "primaryKey": false, "notNull": false}, "student_count": {"name": "student_count", "type": "integer", "primaryKey": false, "notNull": false, "default": 0}, "teacher_count": {"name": "teacher_count", "type": "integer", "primaryKey": false, "notNull": false, "default": 0}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.modules": {"name": "modules", "schema": "", "columns": {"id": {"name": "id", "type": "serial", "primaryKey": true, "notNull": true}, "name": {"name": "name", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "description": {"name": "description", "type": "text", "primaryKey": false, "notNull": false}, "course_id": {"name": "course_id", "type": "integer", "primaryKey": false, "notNull": true}, "order_index": {"name": "order_index", "type": "integer", "primaryKey": false, "notNull": true}, "start_date": {"name": "start_date", "type": "timestamp", "primaryKey": false, "notNull": false}, "end_date": {"name": "end_date", "type": "timestamp", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {}, "foreignKeys": {"modules_course_id_courses_id_fk": {"name": "modules_course_id_courses_id_fk", "tableFrom": "modules", "tableTo": "courses", "columnsFrom": ["course_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.questions": {"name": "questions", "schema": "", "columns": {"id": {"name": "id", "type": "serial", "primaryKey": true, "notNull": true}, "quiz_id": {"name": "quiz_id", "type": "integer", "primaryKey": false, "notNull": true}, "type": {"name": "type", "type": "question_type", "typeSchema": "public", "primaryKey": false, "notNull": true}, "question": {"name": "question", "type": "text", "primaryKey": false, "notNull": true}, "options": {"name": "options", "type": "json", "primaryKey": false, "notNull": false}, "correct_answer": {"name": "correct_answer", "type": "text", "primaryKey": false, "notNull": true}, "points": {"name": "points", "type": "numeric(5, 2)", "primaryKey": false, "notNull": true, "default": "'1'"}, "order_index": {"name": "order_index", "type": "integer", "primaryKey": false, "notNull": true}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {}, "foreignKeys": {"questions_quiz_id_quizzes_id_fk": {"name": "questions_quiz_id_quizzes_id_fk", "tableFrom": "questions", "tableTo": "quizzes", "columnsFrom": ["quiz_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.quiz_attempts": {"name": "quiz_attempts", "schema": "", "columns": {"id": {"name": "id", "type": "serial", "primaryKey": true, "notNull": true}, "student_id": {"name": "student_id", "type": "integer", "primaryKey": false, "notNull": true}, "quiz_id": {"name": "quiz_id", "type": "integer", "primaryKey": false, "notNull": true}, "score": {"name": "score", "type": "numeric(5, 2)", "primaryKey": false, "notNull": false}, "total_points": {"name": "total_points", "type": "numeric(5, 2)", "primaryKey": false, "notNull": false}, "passed": {"name": "passed", "type": "boolean", "primaryKey": false, "notNull": false, "default": false}, "started_at": {"name": "started_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "completed_at": {"name": "completed_at", "type": "timestamp", "primaryKey": false, "notNull": false}, "answers": {"name": "answers", "type": "json", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {}, "foreignKeys": {"quiz_attempts_student_id_users_id_fk": {"name": "quiz_attempts_student_id_users_id_fk", "tableFrom": "quiz_attempts", "tableTo": "users", "columnsFrom": ["student_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}, "quiz_attempts_quiz_id_quizzes_id_fk": {"name": "quiz_attempts_quiz_id_quizzes_id_fk", "tableFrom": "quiz_attempts", "tableTo": "quizzes", "columnsFrom": ["quiz_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.quizzes": {"name": "quizzes", "schema": "", "columns": {"id": {"name": "id", "type": "serial", "primaryKey": true, "notNull": true}, "name": {"name": "name", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "description": {"name": "description", "type": "text", "primaryKey": false, "notNull": false}, "chapter_id": {"name": "chapter_id", "type": "integer", "primaryKey": false, "notNull": false}, "module_id": {"name": "module_id", "type": "integer", "primaryKey": false, "notNull": false}, "course_id": {"name": "course_id", "type": "integer", "primaryKey": false, "notNull": false}, "quiz_type": {"name": "quiz_type", "type": "<PERSON><PERSON><PERSON>(50)", "primaryKey": false, "notNull": true, "default": "'chapter'"}, "minimum_score": {"name": "minimum_score", "type": "numeric(5, 2)", "primaryKey": false, "notNull": true, "default": "'70'"}, "time_limit": {"name": "time_limit", "type": "integer", "primaryKey": false, "notNull": false}, "start_date": {"name": "start_date", "type": "timestamp", "primaryKey": false, "notNull": false}, "end_date": {"name": "end_date", "type": "timestamp", "primaryKey": false, "notNull": false}, "is_active": {"name": "is_active", "type": "boolean", "primaryKey": false, "notNull": false, "default": true}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {}, "foreignKeys": {"quizzes_chapter_id_chapters_id_fk": {"name": "quizzes_chapter_id_chapters_id_fk", "tableFrom": "quizzes", "tableTo": "chapters", "columnsFrom": ["chapter_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}, "quizzes_module_id_modules_id_fk": {"name": "quizzes_module_id_modules_id_fk", "tableFrom": "quizzes", "tableTo": "modules", "columnsFrom": ["module_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}, "quizzes_course_id_courses_id_fk": {"name": "quizzes_course_id_courses_id_fk", "tableFrom": "quizzes", "tableTo": "courses", "columnsFrom": ["course_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.student_enrollments": {"name": "student_enrollments", "schema": "", "columns": {"id": {"name": "id", "type": "serial", "primaryKey": true, "notNull": true}, "student_id": {"name": "student_id", "type": "integer", "primaryKey": false, "notNull": true}, "course_id": {"name": "course_id", "type": "integer", "primaryKey": false, "notNull": true}, "enrolled_at": {"name": "enrolled_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "completed_at": {"name": "completed_at", "type": "timestamp", "primaryKey": false, "notNull": false}, "final_score": {"name": "final_score", "type": "numeric(5, 2)", "primaryKey": false, "notNull": false}, "certificate_generated": {"name": "certificate_generated", "type": "boolean", "primaryKey": false, "notNull": false, "default": false}}, "indexes": {}, "foreignKeys": {"student_enrollments_student_id_users_id_fk": {"name": "student_enrollments_student_id_users_id_fk", "tableFrom": "student_enrollments", "tableTo": "users", "columnsFrom": ["student_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}, "student_enrollments_course_id_courses_id_fk": {"name": "student_enrollments_course_id_courses_id_fk", "tableFrom": "student_enrollments", "tableTo": "courses", "columnsFrom": ["course_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.student_progress": {"name": "student_progress", "schema": "", "columns": {"id": {"name": "id", "type": "serial", "primaryKey": true, "notNull": true}, "student_id": {"name": "student_id", "type": "integer", "primaryKey": false, "notNull": true}, "course_id": {"name": "course_id", "type": "integer", "primaryKey": false, "notNull": true}, "module_id": {"name": "module_id", "type": "integer", "primaryKey": false, "notNull": false}, "chapter_id": {"name": "chapter_id", "type": "integer", "primaryKey": false, "notNull": false}, "completed": {"name": "completed", "type": "boolean", "primaryKey": false, "notNull": false, "default": false}, "completed_at": {"name": "completed_at", "type": "timestamp", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {}, "foreignKeys": {"student_progress_student_id_users_id_fk": {"name": "student_progress_student_id_users_id_fk", "tableFrom": "student_progress", "tableTo": "users", "columnsFrom": ["student_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}, "student_progress_course_id_courses_id_fk": {"name": "student_progress_course_id_courses_id_fk", "tableFrom": "student_progress", "tableTo": "courses", "columnsFrom": ["course_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}, "student_progress_module_id_modules_id_fk": {"name": "student_progress_module_id_modules_id_fk", "tableFrom": "student_progress", "tableTo": "modules", "columnsFrom": ["module_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}, "student_progress_chapter_id_chapters_id_fk": {"name": "student_progress_chapter_id_chapters_id_fk", "tableFrom": "student_progress", "tableTo": "chapters", "columnsFrom": ["chapter_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.users": {"name": "users", "schema": "", "columns": {"id": {"name": "id", "type": "serial", "primaryKey": true, "notNull": true}, "name": {"name": "name", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "email": {"name": "email", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "password": {"name": "password", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "role": {"name": "role", "type": "user_role", "typeSchema": "public", "primaryKey": false, "notNull": true, "default": "'student'"}, "institution_id": {"name": "institution_id", "type": "integer", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {}, "foreignKeys": {"users_institution_id_institutions_id_fk": {"name": "users_institution_id_institutions_id_fk", "tableFrom": "users", "tableTo": "institutions", "columnsFrom": ["institution_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {"users_email_unique": {"name": "users_email_unique", "nullsNotDistinct": false, "columns": ["email"]}}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}}, "enums": {"public.billing_cycle": {"name": "billing_cycle", "schema": "public", "values": ["monthly", "yearly"]}, "public.course_type": {"name": "course_type", "schema": "public", "values": ["self_paced", "verified"]}, "public.institution_type": {"name": "institution_type", "schema": "public", "values": ["sd-negeri", "sd-swasta", "smp-negeri", "smp-swasta", "sma-negeri", "sma-swasta", "university-negeri", "university-swasta", "institution-training", "institution-course", "institution-other"]}, "public.payment_status": {"name": "payment_status", "schema": "public", "values": ["paid", "unpaid"]}, "public.question_type": {"name": "question_type", "schema": "public", "values": ["multiple_choice", "true_false", "essay"]}, "public.subscription_plan": {"name": "subscription_plan", "schema": "public", "values": ["basic", "pro", "enterprise"]}, "public.user_role": {"name": "user_role", "schema": "public", "values": ["student", "teacher", "super_admin"]}}, "schemas": {}, "sequences": {}, "roles": {}, "policies": {}, "views": {}, "_meta": {"columns": {}, "schemas": {}, "tables": {}}}