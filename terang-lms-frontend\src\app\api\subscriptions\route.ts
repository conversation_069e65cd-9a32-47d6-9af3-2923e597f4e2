import { NextRequest, NextResponse } from 'next/server';
import { neon } from '@neondatabase/serverless';

if (!process.env.DATABASE_URL) {
  throw new Error('DATABASE_URL environment variable is required');
}

const sql = neon(process.env.DATABASE_URL);

interface ApiResponse<T = any> {
  success: boolean;
  data?: T;
  error?: string;
}

// GET /api/subscriptions - Get all institutions with billing information
export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const search = searchParams.get('search') || '';
    const status = searchParams.get('status') || 'all';
    const plan = searchParams.get('plan') || 'all';
    const limit = parseInt(searchParams.get('limit') || '50');
    const offset = parseInt(searchParams.get('offset') || '0');

    // Build parameterized query conditions
    const conditions = [];
    const params = [];
    let paramIndex = 1;

    if (search) {
      conditions.push(`(i.name ILIKE $${paramIndex} OR i.type ILIKE $${paramIndex + 1})`);
      params.push(`%${search}%`, `%${search}%`);
      paramIndex += 2;
    }
    
    if (status !== 'all') {
      conditions.push(`i.payment_status = $${paramIndex}`);
      params.push(status);
      paramIndex++;
    }
    
    if (plan !== 'all') {
      conditions.push(`i.subscription_plan = $${paramIndex}`);
      params.push(plan);
      paramIndex++;
    }

    // Execute queries based on filters
    let institutionsResult, countResult;
    
    if (search && status !== 'all' && plan !== 'all') {
      // All three filters
      [institutionsResult, countResult] = await Promise.all([
        sql`
          SELECT 
            i.id, i.name, i.type, i.subscription_plan, i.billing_cycle,
            i.payment_status, i.payment_due_date, i.student_count, i.teacher_count,
            i.created_at, i.updated_at,
            COUNT(CASE WHEN u.role = 'student' THEN 1 END) as actual_student_count,
            COUNT(CASE WHEN u.role = 'teacher' THEN 1 END) as actual_teacher_count
          FROM institutions i
          LEFT JOIN users u ON u.institution_id = i.id
          WHERE (i.name ILIKE ${`%${search}%`} OR i.type ILIKE ${`%${search}%`})
            AND i.payment_status = ${status}
            AND i.subscription_plan = ${plan}
          GROUP BY i.id, i.name, i.type, i.subscription_plan, i.billing_cycle, 
                   i.payment_status, i.payment_due_date, i.student_count, 
                   i.teacher_count, i.created_at, i.updated_at
          ORDER BY CASE WHEN i.payment_status = 'unpaid' THEN 0 ELSE 1 END,
                   i.payment_due_date ASC NULLS LAST, i.name ASC
          LIMIT ${limit} OFFSET ${offset}
        `,
        sql`
          SELECT COUNT(DISTINCT i.id) as total
          FROM institutions i
          LEFT JOIN users u ON u.institution_id = i.id
          WHERE (i.name ILIKE ${`%${search}%`} OR i.type ILIKE ${`%${search}%`})
            AND i.payment_status = ${status}
            AND i.subscription_plan = ${plan}
        `
      ]);
    } else if (search && status !== 'all') {
      // Search and status filters
      [institutionsResult, countResult] = await Promise.all([
        sql`
          SELECT 
            i.id, i.name, i.type, i.subscription_plan, i.billing_cycle,
            i.payment_status, i.payment_due_date, i.student_count, i.teacher_count,
            i.created_at, i.updated_at,
            COUNT(CASE WHEN u.role = 'student' THEN 1 END) as actual_student_count,
            COUNT(CASE WHEN u.role = 'teacher' THEN 1 END) as actual_teacher_count
          FROM institutions i
          LEFT JOIN users u ON u.institution_id = i.id
          WHERE (i.name ILIKE ${`%${search}%`} OR i.type ILIKE ${`%${search}%`})
            AND i.payment_status = ${status}
          GROUP BY i.id, i.name, i.type, i.subscription_plan, i.billing_cycle, 
                   i.payment_status, i.payment_due_date, i.student_count, 
                   i.teacher_count, i.created_at, i.updated_at
          ORDER BY CASE WHEN i.payment_status = 'unpaid' THEN 0 ELSE 1 END,
                   i.payment_due_date ASC NULLS LAST, i.name ASC
          LIMIT ${limit} OFFSET ${offset}
        `,
        sql`
          SELECT COUNT(DISTINCT i.id) as total
          FROM institutions i
          LEFT JOIN users u ON u.institution_id = i.id
          WHERE (i.name ILIKE ${`%${search}%`} OR i.type ILIKE ${`%${search}%`})
            AND i.payment_status = ${status}
        `
      ]);
    } else if (search && plan !== 'all') {
      // Search and plan filters
      [institutionsResult, countResult] = await Promise.all([
        sql`
          SELECT 
            i.id, i.name, i.type, i.subscription_plan, i.billing_cycle,
            i.payment_status, i.payment_due_date, i.student_count, i.teacher_count,
            i.created_at, i.updated_at,
            COUNT(CASE WHEN u.role = 'student' THEN 1 END) as actual_student_count,
            COUNT(CASE WHEN u.role = 'teacher' THEN 1 END) as actual_teacher_count
          FROM institutions i
          LEFT JOIN users u ON u.institution_id = i.id
          WHERE (i.name ILIKE ${`%${search}%`} OR i.type ILIKE ${`%${search}%`})
            AND i.subscription_plan = ${plan}
          GROUP BY i.id, i.name, i.type, i.subscription_plan, i.billing_cycle, 
                   i.payment_status, i.payment_due_date, i.student_count, 
                   i.teacher_count, i.created_at, i.updated_at
          ORDER BY CASE WHEN i.payment_status = 'unpaid' THEN 0 ELSE 1 END,
                   i.payment_due_date ASC NULLS LAST, i.name ASC
          LIMIT ${limit} OFFSET ${offset}
        `,
        sql`
          SELECT COUNT(DISTINCT i.id) as total
          FROM institutions i
          LEFT JOIN users u ON u.institution_id = i.id
          WHERE (i.name ILIKE ${`%${search}%`} OR i.type ILIKE ${`%${search}%`})
            AND i.subscription_plan = ${plan}
        `
      ]);
    } else if (status !== 'all' && plan !== 'all') {
      // Status and plan filters
      [institutionsResult, countResult] = await Promise.all([
        sql`
          SELECT 
            i.id, i.name, i.type, i.subscription_plan, i.billing_cycle,
            i.payment_status, i.payment_due_date, i.student_count, i.teacher_count,
            i.created_at, i.updated_at,
            COUNT(CASE WHEN u.role = 'student' THEN 1 END) as actual_student_count,
            COUNT(CASE WHEN u.role = 'teacher' THEN 1 END) as actual_teacher_count
          FROM institutions i
          LEFT JOIN users u ON u.institution_id = i.id
          WHERE i.payment_status = ${status}
            AND i.subscription_plan = ${plan}
          GROUP BY i.id, i.name, i.type, i.subscription_plan, i.billing_cycle, 
                   i.payment_status, i.payment_due_date, i.student_count, 
                   i.teacher_count, i.created_at, i.updated_at
          ORDER BY CASE WHEN i.payment_status = 'unpaid' THEN 0 ELSE 1 END,
                   i.payment_due_date ASC NULLS LAST, i.name ASC
          LIMIT ${limit} OFFSET ${offset}
        `,
        sql`
          SELECT COUNT(DISTINCT i.id) as total
          FROM institutions i
          LEFT JOIN users u ON u.institution_id = i.id
          WHERE i.payment_status = ${status}
            AND i.subscription_plan = ${plan}
        `
      ]);
    } else if (search) {
      // Search filter only
      [institutionsResult, countResult] = await Promise.all([
        sql`
          SELECT 
            i.id, i.name, i.type, i.subscription_plan, i.billing_cycle,
            i.payment_status, i.payment_due_date, i.student_count, i.teacher_count,
            i.created_at, i.updated_at,
            COUNT(CASE WHEN u.role = 'student' THEN 1 END) as actual_student_count,
            COUNT(CASE WHEN u.role = 'teacher' THEN 1 END) as actual_teacher_count
          FROM institutions i
          LEFT JOIN users u ON u.institution_id = i.id
          WHERE (i.name ILIKE ${`%${search}%`} OR i.type ILIKE ${`%${search}%`})
          GROUP BY i.id, i.name, i.type, i.subscription_plan, i.billing_cycle, 
                   i.payment_status, i.payment_due_date, i.student_count, 
                   i.teacher_count, i.created_at, i.updated_at
          ORDER BY CASE WHEN i.payment_status = 'unpaid' THEN 0 ELSE 1 END,
                   i.payment_due_date ASC NULLS LAST, i.name ASC
          LIMIT ${limit} OFFSET ${offset}
        `,
        sql`
          SELECT COUNT(DISTINCT i.id) as total
          FROM institutions i
          LEFT JOIN users u ON u.institution_id = i.id
          WHERE (i.name ILIKE ${`%${search}%`} OR i.type ILIKE ${`%${search}%`})
        `
      ]);
    } else if (status !== 'all') {
      // Status filter only
      [institutionsResult, countResult] = await Promise.all([
        sql`
          SELECT 
            i.id, i.name, i.type, i.subscription_plan, i.billing_cycle,
            i.payment_status, i.payment_due_date, i.student_count, i.teacher_count,
            i.created_at, i.updated_at,
            COUNT(CASE WHEN u.role = 'student' THEN 1 END) as actual_student_count,
            COUNT(CASE WHEN u.role = 'teacher' THEN 1 END) as actual_teacher_count
          FROM institutions i
          LEFT JOIN users u ON u.institution_id = i.id
          WHERE i.payment_status = ${status}
          GROUP BY i.id, i.name, i.type, i.subscription_plan, i.billing_cycle, 
                   i.payment_status, i.payment_due_date, i.student_count, 
                   i.teacher_count, i.created_at, i.updated_at
          ORDER BY CASE WHEN i.payment_status = 'unpaid' THEN 0 ELSE 1 END,
                   i.payment_due_date ASC NULLS LAST, i.name ASC
          LIMIT ${limit} OFFSET ${offset}
        `,
        sql`
          SELECT COUNT(DISTINCT i.id) as total
          FROM institutions i
          LEFT JOIN users u ON u.institution_id = i.id
          WHERE i.payment_status = ${status}
        `
      ]);
    } else if (plan !== 'all') {
      // Plan filter only
      [institutionsResult, countResult] = await Promise.all([
        sql`
          SELECT 
            i.id, i.name, i.type, i.subscription_plan, i.billing_cycle,
            i.payment_status, i.payment_due_date, i.student_count, i.teacher_count,
            i.created_at, i.updated_at,
            COUNT(CASE WHEN u.role = 'student' THEN 1 END) as actual_student_count,
            COUNT(CASE WHEN u.role = 'teacher' THEN 1 END) as actual_teacher_count
          FROM institutions i
          LEFT JOIN users u ON u.institution_id = i.id
          WHERE i.subscription_plan = ${plan}
          GROUP BY i.id, i.name, i.type, i.subscription_plan, i.billing_cycle, 
                   i.payment_status, i.payment_due_date, i.student_count, 
                   i.teacher_count, i.created_at, i.updated_at
          ORDER BY CASE WHEN i.payment_status = 'unpaid' THEN 0 ELSE 1 END,
                   i.payment_due_date ASC NULLS LAST, i.name ASC
          LIMIT ${limit} OFFSET ${offset}
        `,
        sql`
          SELECT COUNT(DISTINCT i.id) as total
          FROM institutions i
          LEFT JOIN users u ON u.institution_id = i.id
          WHERE i.subscription_plan = ${plan}
        `
      ]);
    } else {
      // No filters
      [institutionsResult, countResult] = await Promise.all([
        sql`
          SELECT 
            i.id, i.name, i.type, i.subscription_plan, i.billing_cycle,
            i.payment_status, i.payment_due_date, i.student_count, i.teacher_count,
            i.created_at, i.updated_at,
            COUNT(CASE WHEN u.role = 'student' THEN 1 END) as actual_student_count,
            COUNT(CASE WHEN u.role = 'teacher' THEN 1 END) as actual_teacher_count
          FROM institutions i
          LEFT JOIN users u ON u.institution_id = i.id
          GROUP BY i.id, i.name, i.type, i.subscription_plan, i.billing_cycle, 
                   i.payment_status, i.payment_due_date, i.student_count, 
                   i.teacher_count, i.created_at, i.updated_at
          ORDER BY CASE WHEN i.payment_status = 'unpaid' THEN 0 ELSE 1 END,
                   i.payment_due_date ASC NULLS LAST, i.name ASC
          LIMIT ${limit} OFFSET ${offset}
        `,
        sql`
          SELECT COUNT(DISTINCT i.id) as total
          FROM institutions i
          LEFT JOIN users u ON u.institution_id = i.id
        `
      ]);
    }

    const institutions = institutionsResult;
    const total = parseInt(countResult[0]?.total || '0');

    // Calculate billing summary
    const summaryResult = await sql`
      SELECT 
        COUNT(*) as total_institutions,
        COUNT(CASE WHEN payment_status = 'paid' THEN 1 END) as paid_institutions,
        COUNT(CASE WHEN payment_status = 'unpaid' THEN 1 END) as unpaid_institutions,
        COUNT(CASE WHEN payment_status = 'unpaid' AND payment_due_date < NOW() THEN 1 END) as overdue_institutions,
        SUM(student_count) as total_students,
        SUM(teacher_count) as total_teachers
      FROM institutions
    `;
    const summary = summaryResult[0];

    const response: ApiResponse = {
      success: true,
      data: {
        institutions,
        total,
        summary: {
          totalInstitutions: parseInt(summary.total_institutions || '0'),
          paidInstitutions: parseInt(summary.paid_institutions || '0'),
          unpaidInstitutions: parseInt(summary.unpaid_institutions || '0'),
          overdueInstitutions: parseInt(summary.overdue_institutions || '0'),
          totalStudents: parseInt(summary.total_students || '0'),
          totalTeachers: parseInt(summary.total_teachers || '0')
        }
      }
    };

    return NextResponse.json(response);
  } catch (error) {
    console.error('Error fetching subscriptions:', error);
    const response: ApiResponse = {
      success: false,
      error: 'Failed to fetch subscription data'
    };
    return NextResponse.json(response, { status: 500 });
  }
}

// PUT /api/subscriptions - Update payment status for multiple institutions
export async function PUT(request: NextRequest) {
  try {
    const body = await request.json();
    const { institutionIds, paymentStatus, paymentDueDate } = body;

    // Validation
    if (!institutionIds || !Array.isArray(institutionIds) || institutionIds.length === 0) {
      const response: ApiResponse = {
        success: false,
        error: 'Institution IDs are required and must be an array'
      };
      return NextResponse.json(response, { status: 400 });
    }

    if (!paymentStatus || !['paid', 'unpaid'].includes(paymentStatus)) {
      const response: ApiResponse = {
        success: false,
        error: 'Valid payment status is required (paid or unpaid)'
      };
      return NextResponse.json(response, { status: 400 });
    }

    // Validate and sanitize institution IDs
    const validIds = institutionIds.map(id => {
      const parsed = parseInt(id);
      if (isNaN(parsed)) {
        throw new Error(`Invalid institution ID: ${id}`);
      }
      return parsed;
    });

    // Update payment status for multiple institutions
    let result;
    
    if (paymentDueDate) {
      result = await sql`
        UPDATE institutions 
        SET 
          payment_status = ${paymentStatus},
          payment_due_date = ${paymentDueDate},
          updated_at = NOW()
        WHERE id = ANY(${validIds})
      `;
    } else if (paymentStatus === 'paid') {
      result = await sql`
        UPDATE institutions 
        SET 
          payment_status = ${paymentStatus},
          payment_due_date = NULL,
          updated_at = NOW()
        WHERE id = ANY(${validIds})
      `;
    } else {
      result = await sql`
        UPDATE institutions 
        SET 
          payment_status = ${paymentStatus},
          updated_at = NOW()
        WHERE id = ANY(${validIds})
      `;
    }

    if (result.length === 0) {
      const response: ApiResponse = {
        success: false,
        error: 'No institutions found with the provided IDs'
      };
      return NextResponse.json(response, { status: 404 });
    }

    const response: ApiResponse = {
      success: true,
      data: {
        message: `Successfully updated ${validIds.length} institution(s)`,
        updatedCount: validIds.length
      }
    };

    return NextResponse.json(response);
  } catch (error) {
    console.error('Error updating subscription status:', error);
    const response: ApiResponse = {
      success: false,
      error: error instanceof Error ? error.message : 'Failed to update subscription status'
    };
    return NextResponse.json(response, { status: 500 });
  }
}