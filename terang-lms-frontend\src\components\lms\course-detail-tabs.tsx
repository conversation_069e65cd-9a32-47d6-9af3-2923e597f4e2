'use client';

import React from 'react';
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON>List, TabsTrigger } from '@/components/ui/tabs';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import {
  BookOpen01Icon as BookOpenIcon,
  UserIcon as UsersIcon,
  Clock01Icon as ClockIcon,
  Calendar03Icon as CalendarIcon,
  CheckmarkCircle01Icon as CheckCircleIcon,
  DollarCircleIcon,
  Briefcase01Icon as BriefcaseIcon,
  GraduateMaleIcon as GraduationCapIcon,
  StarsIcon as StarIcon,
  Award05Icon as AwardIcon,
  Building01Icon as BuildingIcon,
  CustomerSupportIcon as SupportIcon
} from 'hugeicons-react';
import { CourseDetailTabsProps } from '@/types/lms';

const CourseDetailTabs: React.FC<CourseDetailTabsProps> = ({
  course,
  activeTab,
  onTabChange,
  hideTabsList = false
}) => {
  const formatPrice = (price: number, currency: string = 'IDR') => {
    if (currency === 'IDR') {
      return 'Rp' + new Intl.NumberFormat('id-ID').format(price);
    }
    return new Intl.NumberFormat('id-ID', {
      style: 'currency',
      currency: currency
    }).format(price);
  };

  return (
    <Tabs value={activeTab} onValueChange={onTabChange} className="w-full">
      {!hideTabsList && (
        <TabsList className="grid w-full grid-cols-6 mb-6">
          <TabsTrigger value="overview" className="flex items-center gap-2">
            <BookOpenIcon className="h-4 w-4" />
            Ringkasan
          </TabsTrigger>
          <TabsTrigger value="admissions" className="flex items-center gap-2">
            <GraduationCapIcon className="h-4 w-4" />
            Penerimaan
          </TabsTrigger>
          <TabsTrigger value="academics" className="flex items-center gap-2">
            <AwardIcon className="h-4 w-4" />
            Akademik
          </TabsTrigger>
          <TabsTrigger value="tuition" className="flex items-center gap-2">
            <DollarCircleIcon className="h-4 w-4" />
            Biaya
          </TabsTrigger>
          <TabsTrigger value="careers" className="flex items-center gap-2">
            <BriefcaseIcon className="h-4 w-4" />
            Karier
          </TabsTrigger>
          <TabsTrigger value="experience" className="flex items-center gap-2">
            <StarIcon className="h-4 w-4" />
            Pengalaman
          </TabsTrigger>
        </TabsList>
      )}

      <TabsContent value="overview" className="space-y-6">
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <BookOpenIcon className="h-5 w-5" />
              Ringkasan Kursus
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div>
              <h3 className="font-semibold mb-2">Deskripsi</h3>
              <p className="text-gray-700">{course.description}</p>
            </div>
            
            <div className="grid md:grid-cols-2 gap-4">
              <div>
                <h4 className="font-semibold mb-2">Detail Kursus</h4>
                <div className="space-y-2 text-sm">
                  <div className="flex items-center">
                    <UsersIcon className="mr-2 h-4 w-4 text-gray-500" />
                    <span>Instruktur: {course.instructor}</span>
                  </div>
                  <div className="flex items-center">
                    <CalendarIcon className="mr-2 h-4 w-4 text-gray-500" />
                    <span>
                      Durasi: {new Date(course.startDate).toLocaleDateString('id-ID')} - {' '}
                      {new Date(course.endDate).toLocaleDateString('id-ID')}
                    </span>
                  </div>
                  <div className="flex items-center">
                    <BookOpenIcon className="mr-2 h-4 w-4 text-gray-500" />
                    <span>{course.modules.length} modul</span>
                  </div>
                  <div className="flex items-center">
                    <CheckCircleIcon className="mr-2 h-4 w-4 text-gray-500" />
                    <span>Nilai kelulusan: {course.minPassingScore}%</span>
                  </div>
                </div>
              </div>
              
              <div>
                <h4 className="font-semibold mb-2">Apa yang Akan Anda Pelajari</h4>
                <div className="space-y-2">
                  {course.modules.slice(0, 3).map((module, index) => (
                    <div key={module.id} className="flex items-start gap-2">
                      <CheckCircleIcon className="h-4 w-4 text-green-600 mt-0.5 flex-shrink-0" />
                      <span className="text-sm">{module.title}</span>
                    </div>
                  ))}
                  {course.modules.length > 3 && (
                    <div className="text-sm text-gray-500">
                      +{course.modules.length - 3} modul lagi
                    </div>
                  )}
                </div>
              </div>
            </div>

            <div>
              <h4 className="font-semibold mb-2">Sertifikat</h4>
              <div className="flex items-center gap-2">
                {course.certificate.isEligible ? (
                  <Badge variant="default" className="bg-green-100 text-green-800">
                    <AwardIcon className="mr-1 h-3 w-3" />
                    Sertifikat Tersedia
                  </Badge>
                ) : (
                  <Badge variant="secondary">
                    Tidak Ada Sertifikat
                  </Badge>
                )}
              </div>
            </div>
          </CardContent>
        </Card>
      </TabsContent>

      <TabsContent value="admissions" className="space-y-6">
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <GraduationCapIcon className="h-5 w-5" />
              Informasi Penerimaan
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            {course.admissions?.requirements && (
              <div>
                <h4 className="font-semibold mb-2">Persyaratan</h4>
                <ul className="space-y-1">
                  {course.admissions.requirements.map((req, index) => (
                    <li key={index} className="flex items-start gap-2">
                      <CheckCircleIcon className="h-4 w-4 text-blue-600 mt-0.5 flex-shrink-0" />
                      <span className="text-sm">{req}</span>
                    </li>
                  ))}
                </ul>
              </div>
            )}

            {course.admissions?.prerequisites && (
              <div>
                <h4 className="font-semibold mb-2">Prasyarat</h4>
                <ul className="space-y-1">
                  {course.admissions.prerequisites.map((prereq, index) => (
                    <li key={index} className="flex items-start gap-2">
                      <BookOpenIcon className="h-4 w-4 text-orange-600 mt-0.5 flex-shrink-0" />
                      <span className="text-sm">{prereq}</span>
                    </li>
                  ))}
                </ul>
              </div>
            )}

            {course.admissions?.applicationDeadline && (
              <div>
                <h4 className="font-semibold mb-2">Batas Waktu Pendaftaran</h4>
                <div className="flex items-center gap-2">
                  <CalendarIcon className="h-4 w-4 text-red-600" />
                  <span className="text-sm">
                    {new Date(course.admissions.applicationDeadline).toLocaleDateString('id-ID')}
                  </span>
                </div>
              </div>
            )}

            <div>
              <h4 className="font-semibold mb-2">Opsi Pendaftaran</h4>
              <div className="flex flex-wrap gap-2">
                {course.enrollmentType === 'code' && (
                  <Badge variant="outline">Kode Pendaftaran Diperlukan</Badge>
                )}
                {course.enrollmentType === 'invitation' && (
                  <Badge variant="outline">Hanya Undangan</Badge>
                )}
                {course.enrollmentType === 'purchase' && (
                  <Badge variant="outline">Pembelian Langsung</Badge>
                )}
                {course.enrollmentType === 'both' && (
                  <>
                    <Badge variant="outline">Kode Pendaftaran</Badge>
                    <Badge variant="outline">Pembelian Langsung</Badge>
                  </>
                )}
              </div>
            </div>
          </CardContent>
        </Card>
      </TabsContent>

      <TabsContent value="academics" className="space-y-6">
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <AwardIcon className="h-5 w-5" />
              Informasi Akademik
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            {course.academics && (
              <div className="grid md:grid-cols-2 gap-4">
                <div>
                  <h4 className="font-semibold mb-2">Struktur Kursus</h4>
                  <div className="space-y-2 text-sm">
                    <div className="flex items-center justify-between">
                      <span>Kredit:</span>
                      <Badge variant="secondary">{course.academics.credits}</Badge>
                    </div>
                    <div className="flex items-center justify-between">
                      <span>Beban Kerja:</span>
                      <span className="font-medium">{course.academics.workload}</span>
                    </div>
                  </div>
                </div>
                
                <div>
                  <h4 className="font-semibold mb-2">Metode Penilaian</h4>
                  <ul className="space-y-1">
                    {course.academics.assessment.map((method, index) => (
                      <li key={index} className="flex items-start gap-2">
                        <CheckCircleIcon className="h-4 w-4 text-blue-600 mt-0.5 flex-shrink-0" />
                        <span className="text-sm">{method}</span>
                      </li>
                    ))}
                  </ul>
                </div>
              </div>
            )}

            <div>
              <h4 className="font-semibold mb-2">Modul Kursus</h4>
              <div className="space-y-2">
                {course.modules.map((module, index) => (
                  <Card key={module.id} className="p-3">
                    <div className="flex items-start justify-between">
                      <div>
                        <h5 className="font-medium">{module.title}</h5>
                        <p className="text-sm text-gray-600">{module.description}</p>
                        <div className="mt-2 text-sm text-gray-500">
                          {module.chapters.length} bab
                        </div>
                      </div>
                      <Badge variant="outline">Modul {index + 1}</Badge>
                    </div>
                  </Card>
                ))}
              </div>
            </div>
          </CardContent>
        </Card>
      </TabsContent>

      <TabsContent value="tuition" className="space-y-6">
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <DollarCircleIcon className="h-5 w-5" />
              Biaya & Pembiayaan
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            {course.tuitionAndFinancing ? (
              <>
                <div>
                  <h4 className="font-semibold mb-2">Biaya Kursus</h4>
                  <div className="text-3xl font-bold text-green-600">
                    {formatPrice(course.tuitionAndFinancing.totalCost, course.currency)}
                  </div>
                  {course.price && course.price < course.tuitionAndFinancing.totalCost && (
                    <div className="text-sm text-gray-600">
                      Penawaran khusus: {formatPrice(course.price, course.currency)}
                      <Badge variant="destructive" className="ml-2">
                        {Math.round(((course.tuitionAndFinancing.totalCost - course.price) / course.tuitionAndFinancing.totalCost) * 100)}% OFF
                      </Badge>
                    </div>
                  )}
                </div>

                <div>
                  <h4 className="font-semibold mb-2">Opsi Pembayaran</h4>
                  <ul className="space-y-1">
                    {course.tuitionAndFinancing.paymentOptions.map((option, index) => (
                      <li key={index} className="flex items-start gap-2">
                        <CheckCircleIcon className="h-4 w-4 text-green-600 mt-0.5 flex-shrink-0" />
                        <span className="text-sm">{option}</span>
                      </li>
                    ))}
                  </ul>
                </div>

                {course.tuitionAndFinancing.scholarships && (
                  <div>
                    <h4 className="font-semibold mb-2">Beasiswa Tersedia</h4>
                    <ul className="space-y-1">
                      {course.tuitionAndFinancing.scholarships.map((scholarship, index) => (
                        <li key={index} className="flex items-start gap-2">
                          <AwardIcon className="h-4 w-4 text-yellow-600 mt-0.5 flex-shrink-0" />
                          <span className="text-sm">{scholarship}</span>
                        </li>
                      ))}
                    </ul>
                  </div>
                )}
              </>
            ) : (
              <div className="text-center py-8">
                <div className="text-4xl font-bold text-green-600 mb-2">GRATIS</div>
                <p className="text-gray-600">Kursus ini tersedia tanpa biaya</p>
              </div>
            )}
          </CardContent>
        </Card>
      </TabsContent>

      <TabsContent value="careers" className="space-y-6">
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <BriefcaseIcon className="h-5 w-5" />
              Prospek Karier
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            {course.careers ? (
              <>
                <div>
                  <h4 className="font-semibold mb-2">Prospek Karier</h4>
                  <ul className="space-y-1">
                    {course.careers.outcomes.map((outcome, index) => (
                      <li key={index} className="flex items-start gap-2">
                        <CheckCircleIcon className="h-4 w-4 text-blue-600 mt-0.5 flex-shrink-0" />
                        <span className="text-sm">{outcome}</span>
                      </li>
                    ))}
                  </ul>
                </div>

                <div>
                  <h4 className="font-semibold mb-2">Industri</h4>
                  <div className="flex flex-wrap gap-2">
                    {course.careers.industries.map((industry, index) => (
                      <Badge key={index} variant="outline">
                        {industry}
                      </Badge>
                    ))}
                  </div>
                </div>

                {course.careers.averageSalary && (
                  <div>
                    <h4 className="font-semibold mb-2">Gaji Rata-rata</h4>
                    <div className="text-2xl font-bold text-green-600">
                      {course.careers.averageSalary}
                    </div>
                  </div>
                )}
              </>
            ) : (
              <div className="text-center py-8">
                <BriefcaseIcon className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                <p className="text-gray-600">Informasi karier tidak tersedia</p>
              </div>
            )}
          </CardContent>
        </Card>
      </TabsContent>

      <TabsContent value="experience" className="space-y-6">
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <StarIcon className="h-5 w-5" />
              Pengalaman Mahasiswa
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-6">
            {course.studentExperience ? (
              <>
                {course.studentExperience.testimonials.length > 0 && (
                  <div>
                    <h4 className="font-semibold mb-4">Testimoni Mahasiswa</h4>
                    <div className="space-y-4">
                      {course.studentExperience.testimonials.map((testimonial, index) => (
                        <Card key={index} className="p-4">
                          <div className="flex items-start gap-3">
                            <div className="flex-shrink-0">
                              <div className="h-10 w-10 bg-blue-100 rounded-full flex items-center justify-center">
                                <UsersIcon className="h-5 w-5 text-blue-600" />
                              </div>
                            </div>
                            <div>
                              <p className="text-sm italic mb-2">&quot;{testimonial.feedback}&quot;</p>
                              <p className="font-medium text-sm">- {testimonial.name}</p>
                            </div>
                          </div>
                        </Card>
                      ))}
                    </div>
                  </div>
                )}

                <div className="grid md:grid-cols-2 gap-6">
                  {course.studentExperience.facilities.length > 0 && (
                    <div>
                      <h4 className="font-semibold mb-2 flex items-center gap-2">
                        <BuildingIcon className="h-4 w-4" />
                        Fasilitas
                      </h4>
                      <ul className="space-y-1">
                        {course.studentExperience.facilities.map((facility, index) => (
                          <li key={index} className="flex items-start gap-2">
                            <CheckCircleIcon className="h-4 w-4 text-green-600 mt-0.5 flex-shrink-0" />
                            <span className="text-sm">{facility}</span>
                          </li>
                        ))}
                      </ul>
                    </div>
                  )}

                  {course.studentExperience.support.length > 0 && (
                    <div>
                      <h4 className="font-semibold mb-2 flex items-center gap-2">
                        <SupportIcon className="h-4 w-4" />
                        Dukungan Mahasiswa
                      </h4>
                      <ul className="space-y-1">
                        {course.studentExperience.support.map((supportItem, index) => (
                          <li key={index} className="flex items-start gap-2">
                            <CheckCircleIcon className="h-4 w-4 text-blue-600 mt-0.5 flex-shrink-0" />
                            <span className="text-sm">{supportItem}</span>
                          </li>
                        ))}
                      </ul>
                    </div>
                  )}
                </div>
              </>
            ) : (
              <div className="text-center py-8">
                <StarIcon className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                <p className="text-gray-600">Informasi pengalaman mahasiswa tidak tersedia</p>
              </div>
            )}
          </CardContent>
        </Card>
      </TabsContent>
    </Tabs>
  );
};

export default CourseDetailTabs;