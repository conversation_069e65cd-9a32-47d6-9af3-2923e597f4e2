'use client';

import { useAuth } from '@/hooks/use-auth';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';

export default function ProfileViewPage() {
  const { user, loading, signOut } = useAuth();

  if (loading) {
    return <div>Loading...</div>;
  }

  if (!user) {
    return <div>Please sign in to view your profile.</div>;
  }

  return (
    <div className='space-y-6 p-4 sm:p-6 lg:p-8'>
      <Card>
        <CardHeader>
          <div className='flex flex-col items-center gap-4 text-center sm:flex-row sm:text-left'>
            <Avatar className='h-24 w-24 text-3xl'>
              <AvatarImage
                src={`https://ui-avatars.com/api/?name=${user.name}&background=random`}
              />
              <AvatarFallback>{user.name.charAt(0)}</AvatarFallback>
            </Avatar>
            <div className='space-y-1'>
              <CardTitle className='text-2xl'>{user.name}</CardTitle>
              <p className='text-muted-foreground'>{user.email}</p>
            </div>
          </div>
        </CardHeader>
      </Card>

      <Card>
        <CardHeader>
          <CardTitle>Account Details</CardTitle>
        </CardHeader>
        <CardContent className='space-y-4'>
          <div className='grid grid-cols-1 gap-4 sm:grid-cols-2'>
            <div className='space-y-1'>
              <p className='text-muted-foreground text-sm font-medium'>
                Full Name
              </p>
              <p>{user.name}</p>
            </div>
            <div className='space-y-1'>
              <p className='text-muted-foreground text-sm font-medium'>
                Email address
              </p>
              <p>{user.email}</p>
            </div>
            <div className='space-y-1'>
              <p className='text-muted-foreground text-sm font-medium'>Role</p>
              <p className='capitalize'>{user.role.replace('_', ' ')}</p>
            </div>
            {user.institutionId && (
              <div className='space-y-1'>
                <p className='text-muted-foreground text-sm font-medium'>
                  Institution ID
                </p>
                <p>{user.institutionId}</p>
              </div>
            )}
          </div>
          <div className='flex justify-end pt-4'>
            <Button onClick={signOut} variant='destructive'>
              Sign Out
            </Button>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
