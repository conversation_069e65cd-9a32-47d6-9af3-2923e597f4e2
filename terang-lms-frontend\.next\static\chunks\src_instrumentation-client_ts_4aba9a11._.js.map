{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/File%20Kerja%20Daffa/GAWEAN/TERANG_LMS/terang-lms-frontend/src/instrumentation-client.ts"], "sourcesContent": ["// This file configures the initialization of Sentry on the client.\r\n// The added config here will be used whenever a users loads a page in their browser.\r\n// https://docs.sentry.io/platforms/javascript/guides/nextjs/\r\n;globalThis[\"_sentryRouteManifest\"] = \"{\\\"dynamicRoutes\\\":[{\\\"path\\\":\\\"/my-courses/:courseId\\\",\\\"regex\\\":\\\"^/my-courses/([^/]+)$\\\",\\\"paramNames\\\":[\\\"courseId\\\"]},{\\\"path\\\":\\\"/my-courses/:courseId/detail\\\",\\\"regex\\\":\\\"^/my-courses/([^/]+)/detail$\\\",\\\"paramNames\\\":[\\\"courseId\\\"]},{\\\"path\\\":\\\"/my-courses/:courseId/exam\\\",\\\"regex\\\":\\\"^/my-courses/([^/]+)/exam$\\\",\\\"paramNames\\\":[\\\"courseId\\\"]},{\\\"path\\\":\\\"/my-courses/:courseId/exam/results\\\",\\\"regex\\\":\\\"^/my-courses/([^/]+)/exam/results$\\\",\\\"paramNames\\\":[\\\"courseId\\\"]},{\\\"path\\\":\\\"/my-courses/:courseId/learn\\\",\\\"regex\\\":\\\"^/my-courses/([^/]+)/learn$\\\",\\\"paramNames\\\":[\\\"courseId\\\"]},{\\\"path\\\":\\\"/auth/sign-in/:sign-in*?\\\",\\\"regex\\\":\\\"^/auth/sign-in(?:/(.*))?$\\\",\\\"paramNames\\\":[\\\"sign-in\\\"]},{\\\"path\\\":\\\"/auth/sign-up/:sign-up*?\\\",\\\"regex\\\":\\\"^/auth/sign-up(?:/(.*))?$\\\",\\\"paramNames\\\":[\\\"sign-up\\\"]},{\\\"path\\\":\\\"/dashboard/admin/institutions/:id\\\",\\\"regex\\\":\\\"^/dashboard/admin/institutions/([^/]+)$\\\",\\\"paramNames\\\":[\\\"id\\\"]},{\\\"path\\\":\\\"/dashboard/admin/users/:id\\\",\\\"regex\\\":\\\"^/dashboard/admin/users/([^/]+)$\\\",\\\"paramNames\\\":[\\\"id\\\"]},{\\\"path\\\":\\\"/dashboard/product/:productId\\\",\\\"regex\\\":\\\"^/dashboard/product/([^/]+)$\\\",\\\"paramNames\\\":[\\\"productId\\\"]},{\\\"path\\\":\\\"/dashboard/profile/:profile*?\\\",\\\"regex\\\":\\\"^/dashboard/profile(?:/(.*))?$\\\",\\\"paramNames\\\":[\\\"profile\\\"]},{\\\"path\\\":\\\"/dashboard/student/courses/:id\\\",\\\"regex\\\":\\\"^/dashboard/student/courses/([^/]+)$\\\",\\\"paramNames\\\":[\\\"id\\\"]},{\\\"path\\\":\\\"/dashboard/teacher/classes/:id\\\",\\\"regex\\\":\\\"^/dashboard/teacher/classes/([^/]+)$\\\",\\\"paramNames\\\":[\\\"id\\\"]},{\\\"path\\\":\\\"/dashboard/teacher/classes/:id/courses\\\",\\\"regex\\\":\\\"^/dashboard/teacher/classes/([^/]+)/courses$\\\",\\\"paramNames\\\":[\\\"id\\\"]},{\\\"path\\\":\\\"/dashboard/teacher/classes/:id/students\\\",\\\"regex\\\":\\\"^/dashboard/teacher/classes/([^/]+)/students$\\\",\\\"paramNames\\\":[\\\"id\\\"]},{\\\"path\\\":\\\"/dashboard/teacher/courses/:id\\\",\\\"regex\\\":\\\"^/dashboard/teacher/courses/([^/]+)$\\\",\\\"paramNames\\\":[\\\"id\\\"]}],\\\"staticRoutes\\\":[{\\\"path\\\":\\\"/\\\"},{\\\"path\\\":\\\"/courses\\\"},{\\\"path\\\":\\\"/my-courses\\\"},{\\\"path\\\":\\\"/dashboard\\\"},{\\\"path\\\":\\\"/dashboard/admin\\\"},{\\\"path\\\":\\\"/dashboard/admin/institutions\\\"},{\\\"path\\\":\\\"/dashboard/admin/institutions/new\\\"},{\\\"path\\\":\\\"/dashboard/admin/subscriptions\\\"},{\\\"path\\\":\\\"/dashboard/admin/users\\\"},{\\\"path\\\":\\\"/dashboard/admin/users/new\\\"},{\\\"path\\\":\\\"/dashboard/kanban\\\"},{\\\"path\\\":\\\"/dashboard/overview/@area_stats\\\"},{\\\"path\\\":\\\"/dashboard/overview/@bar_stats\\\"},{\\\"path\\\":\\\"/dashboard/overview/@pie_stats\\\"},{\\\"path\\\":\\\"/dashboard/overview/@sales\\\"},{\\\"path\\\":\\\"/dashboard/product\\\"},{\\\"path\\\":\\\"/dashboard/student\\\"},{\\\"path\\\":\\\"/dashboard/student/certificates\\\"},{\\\"path\\\":\\\"/dashboard/student/courses\\\"},{\\\"path\\\":\\\"/dashboard/student/progress\\\"},{\\\"path\\\":\\\"/dashboard/teacher\\\"},{\\\"path\\\":\\\"/dashboard/teacher/classes\\\"},{\\\"path\\\":\\\"/dashboard/teacher/classes/new\\\"},{\\\"path\\\":\\\"/dashboard/teacher/courses\\\"},{\\\"path\\\":\\\"/dashboard/teacher/courses/generate\\\"},{\\\"path\\\":\\\"/dashboard/teacher/courses/new\\\"},{\\\"path\\\":\\\"/dashboard/teacher/reports\\\"}]}\";import * as Sentry from '@sentry/nextjs';\r\n\r\nif (!process.env.NEXT_PUBLIC_SENTRY_DISABLED) {\r\n  Sentry.init({\r\n    dsn: process.env.NEXT_PUBLIC_SENTRY_DSN,\r\n\r\n    // Add optional integrations for additional features\r\n    integrations: [Sentry.replayIntegration()],\r\n\r\n    // Adds request headers and IP for users, for more info visit\r\n    sendDefaultPii: true,\r\n\r\n    // Define how likely traces are sampled. Adjust this value in production, or use tracesSampler for greater control.\r\n    tracesSampleRate: 1,\r\n\r\n    // Define how likely Replay events are sampled.\r\n    // This sets the sample rate to be 10%. You may want this to be 100% while\r\n    // in development and sample at a lower rate in production\r\n    replaysSessionSampleRate: 0.1,\r\n\r\n    // Define how likely Replay events are sampled when an error occurs.\r\n    replaysOnErrorSampleRate: 1.0,\r\n\r\n    // Setting this option to true will print useful information to the console while you're setting up Sentry.\r\n    debug: false\r\n  });\r\n}\r\n\r\nexport const onRouterTransitionStart = Sentry.captureRouterTransitionStart;\r\n"], "names": [], "mappings": "AAAA,mEAAmE;AACnE,qFAAqF;AACrF,6DAA6D;;;;AAGxD;AAF2+F;AAAA;AAAA;AAA/+F,UAAU,CAAC,uBAAuB,GAAG;;AAEtC,IAAI,CAAC,gKAAA,CAAA,UAAO,CAAC,GAAG,CAAC,2BAA2B,EAAE;IAC5C,CAAA,GAAA,wLAAA,CAAA,OAAW,AAAD,EAAE;QACV,KAAK,gKAAA,CAAA,UAAO,CAAC,GAAG,CAAC,sBAAsB;QAEvC,oDAAoD;QACpD,cAAc;YAAC,CAAA,GAAA,iLAAA,CAAA,oBAAwB,AAAD;SAAI;QAE1C,6DAA6D;QAC7D,gBAAgB;QAEhB,mHAAmH;QACnH,kBAAkB;QAElB,+CAA+C;QAC/C,0EAA0E;QAC1E,0DAA0D;QAC1D,0BAA0B;QAE1B,oEAAoE;QACpE,0BAA0B;QAE1B,2GAA2G;QAC3G,OAAO;IACT;AACF;AAEO,MAAM,0BAA0B,6MAAA,CAAA,+BAAmC", "debugId": null}}]}