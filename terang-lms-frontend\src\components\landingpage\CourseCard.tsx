'use client';

import { Course } from '@/types/lms';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Card, CardContent } from '@/components/ui/card';
import {
  BookOpen01Icon as BookOpenIcon,
  Calendar01Icon as CalendarIcon,
  UserIcon,
  Award01Icon as AwardIcon,
  ArrowRight01Icon as ArrowRightIcon
} from 'hugeicons-react';

interface CourseCardProps {
  course: Course;
  onEnroll: (courseId: string) => void;
  onPreview: (courseId: string) => void;
  isEnrolled?: boolean;
}

export default function CourseCard({ 
  course, 
  onEnroll, 
  onPreview, 
  isEnrolled = false 
}: CourseCardProps) {
  const totalModules = course.modules.length;
  const completedModules = course.modules.filter(module => 
    module.chapters.every(chapter => 
      chapter.contents.every(content => content.isCompleted)
    )
  ).length;
  
  const progressPercentage = totalModules > 0 ? (completedModules / totalModules) * 100 : 0;

  return (
    <Card className="w-full shadow-lg hover:shadow-xl transition-shadow duration-300 overflow-hidden">
      <CardContent className="p-0">
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-0">
          {/* Course Image/Preview */}
          <div className="lg:col-span-1">
            <div className="h-64 lg:h-full bg-gradient-to-br from-blue-500 to-indigo-600 relative overflow-hidden">
              <div className="absolute inset-0 bg-black bg-opacity-20"></div>
              <div className="absolute inset-0 flex flex-col items-center justify-center text-white p-6">
                <div className="w-16 h-16 bg-white bg-opacity-20 rounded-full flex items-center justify-center mb-4">
                  <BookOpenIcon className="w-8 h-8" />
                </div>
                <div className="text-center">
                  <h3 className="text-lg font-bold mb-2">{course.code}</h3>
                  <p className="text-sm opacity-90">Sertifikasi Profesional</p>
                </div>
                
                {/* Floating badges */}
                <div className="absolute top-4 right-4">
                  <Badge variant="secondary" className="bg-white bg-opacity-20 text-white border-white border-opacity-30">
                    <AwardIcon className="w-3 h-3 mr-1" />
                    Sertifikat
                  </Badge>
                </div>
                
                {isEnrolled && (
                  <div className="absolute bottom-4 right-4">
                    <Badge variant="outline" className="bg-green-500 text-white border-green-400">
                      Terdaftar
                    </Badge>
                  </div>
                )}
              </div>
            </div>
          </div>

          {/* Course Content */}
          <div className="lg:col-span-2 p-6 lg:p-8">
            <div className="h-full flex flex-col">
              {/* Header */}
              <div className="mb-6">
                <h2 className="text-2xl lg:text-3xl font-bold text-gray-900 mb-3 leading-tight">
                  {course.name}
                </h2>
                <p className="text-gray-600 text-base lg:text-lg leading-relaxed line-clamp-3">
                  {course.description}
                </p>
              </div>

              {/* Course Info */}
              <div className="grid grid-cols-1 sm:grid-cols-2 gap-4 mb-6">
                <div className="flex items-center space-x-3">
                  <UserIcon className="w-5 h-5 text-blue-600" />
                  <div>
                    <p className="text-sm text-gray-500">Instruktur</p>
                    <p className="font-medium text-gray-900">{course.instructor}</p>
                  </div>
                </div>
                
                <div className="flex items-center space-x-3">
                  <CalendarIcon className="w-5 h-5 text-blue-600" />
                  <div>
                    <p className="text-sm text-gray-500">Durasi</p>
                    <p className="font-medium text-gray-900">
                      {new Date(course.startDate).toLocaleDateString()} - {new Date(course.endDate).toLocaleDateString()}
                    </p>
                  </div>
                </div>
                
                <div className="flex items-center space-x-3">
                  <BookOpenIcon className="w-5 h-5 text-blue-600" />
                  <div>
                    <p className="text-sm text-gray-500">Modul</p>
                    <p className="font-medium text-gray-900">{totalModules} Modul</p>
                  </div>
                </div>
                
                <div className="flex items-center space-x-3">
                  <AwardIcon className="w-5 h-5 text-blue-600" />
                  <div>
                    <p className="text-sm text-gray-500">Nilai Lulus</p>
                    <p className="font-medium text-gray-900">{course.minPassingScore}%</p>
                  </div>
                </div>
              </div>

              {/* Progress (if enrolled) */}
              {isEnrolled && (
                <div className="mb-6 p-4 bg-blue-50 rounded-lg">
                  <div className="flex justify-between items-center mb-2">
                    <span className="text-sm font-medium text-blue-800">Kemajuan Anda</span>
                    <span className="text-sm text-blue-600">{Math.round(progressPercentage)}%</span>
                  </div>
                  <div className="w-full bg-blue-200 rounded-full h-2">
                    <div 
                      className="bg-blue-600 h-2 rounded-full transition-all duration-300" 
                      style={{ width: `${progressPercentage}%` }}
                    ></div>
                  </div>
                  <p className="text-xs text-blue-600 mt-2">
                    {completedModules} dari {totalModules} modul selesai
                  </p>
                </div>
              )}

              {/* Module Preview */}
              <div className="mb-6">
                <h4 className="font-semibold text-gray-900 mb-3">Modul Kursus</h4>
                <div className="space-y-2">
                  {course.modules.slice(0, 3).map((module, index) => (
                    <div key={module.id} className="flex items-center space-x-3 p-2 bg-gray-50 rounded">
                      <div className="w-6 h-6 bg-blue-100 rounded-full flex items-center justify-center text-xs font-medium text-blue-600">
                        {index + 1}
                      </div>
                      <span className="text-sm text-gray-700 flex-1">{module.title}</span>
                      {isEnrolled && module.isUnlocked && (
                        <Badge variant="outline" className="text-xs">
                          {module.completionPercentage === 100 ? 'Selesai' : 'Tersedia'}
                        </Badge>
                      )}
                    </div>
                  ))}
                  {course.modules.length > 3 && (
                    <p className="text-xs text-gray-500 pl-9">
                      +{course.modules.length - 3} modul lainnya
                    </p>
                  )}
                </div>
              </div>

              {/* Actions */}
              <div className="mt-auto pt-4">
                <div className="flex flex-col sm:flex-row gap-3">
                  <Button
                    onClick={() => onPreview(course.id)}
                    variant="outline"
                    className="flex-1 border-blue-200 text-blue-600 hover:bg-blue-50"
                  >
                    Lihat Pratinjau
                  </Button>
                  
                  {isEnrolled ? (
                    <Button
                      onClick={() => onEnroll(course.id)}
                      className="flex-1 bg-green-600 hover:bg-green-700"
                    >
                      Lanjutkan Belajar
                      <ArrowRightIcon className="w-4 h-4 ml-2" />
                    </Button>
                  ) : (
                    <Button
                      onClick={() => onEnroll(course.id)}
                      className="flex-1 bg-blue-600 hover:bg-blue-700"
                    >
                      Daftar Sekarang
                      <ArrowRightIcon className="w-4 h-4 ml-2" />
                    </Button>
                  )}
                </div>
              </div>
            </div>
          </div>
        </div>
      </CardContent>
    </Card>
  );
}