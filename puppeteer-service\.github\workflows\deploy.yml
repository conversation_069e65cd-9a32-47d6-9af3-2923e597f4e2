name: Build and Deploy Puppeteer Service
 
on:
  push:
    branches: [ main ]
  pull_request:
    branches: [ main ]

env:
  PROJECT_ID: ************
  PROJECT_NAME: terang-ai
  GCR_REGION: asia-southeast2
  IMAGE_NAME: puppeteer-service
  GKE_CLUSTER: terang-ai-autopilot-cluster
  GKE_ZONE: asia-southeast2

jobs:
  build-and-push-deploy:
    runs-on: ubicloud-standard-16
    
    permissions:
      contents: 'read'
      id-token: 'write'

    steps:
    - uses: actions/checkout@v4

    - name: Set up Node.js
      uses: actions/setup-node@v3
      with:
        node-version: '20.5.0'
        cache: 'npm'
        
    # Cache node_modules
    - name: Cache node_modules
      id: cache-node-modules
      uses: actions/cache@v3
      with:
        path: node_modules
        key: ${{ runner.os }}-node-modules-${{ hashFiles('package-lock.json') }}
        
    - name: Clean npm cache (optional)
      run: npm cache clean --force
      
    - name: Install dependencies
      if: steps.cache-node-modules.outputs.cache-hit != 'true'
      run: npm ci

    - name: Run tests (if available)
      run: npm test || echo "No tests defined"

    - id: 'auth'
      name: 'Authenticate to Google Cloud'
      uses: 'google-github-actions/auth@v1'
      with:
        workload_identity_provider: 'projects/************/locations/global/workloadIdentityPools/pool/providers/pool'
        service_account: '<EMAIL>'

    - name: 'Set up Cloud SDK'
      uses: 'google-github-actions/setup-gcloud@v1'

    - name: 'Use gcloud CLI'
      run: 'gcloud info'

    # Configure Docker authentication BEFORE using build-push-action
    - name: Configure Docker to use gcloud as a credential helper
      run: |
        gcloud auth configure-docker asia-southeast2-docker.pkg.dev

    # Set up Docker Buildx AFTER authentication
    - name: Set up Docker Buildx
      uses: docker/setup-buildx-action@v3

    # Optimize Docker context for Puppeteer service
    - name: Optimize Docker context
      run: |
        echo "**/.git" >> .dockerignore
        echo "**/.github" >> .dockerignore
        echo "node_modules/.cache" >> .dockerignore
        echo "screenshots/*.png" >> .dockerignore
        echo "*.log" >> .dockerignore

    # Build and push using Docker Buildx with caching
    - name: Build and push Docker image
      uses: docker/build-push-action@v5
      with:
        context: .
        push: true
        tags: asia-southeast2-docker.pkg.dev/${{ env.PROJECT_NAME }}/terang-ai-registry/${{ env.IMAGE_NAME }}:${{ github.sha }}
        cache-from: type=gha
        cache-to: type=gha,mode=max
        compression: zstd
        platforms: linux/amd64
        provenance: false
        build-args: |
          BUILDKIT_INLINE_CACHE=1

    - name: 'Install GKE Auth Plugin'
      run: |
        gcloud components install gke-gcloud-auth-plugin
        echo "CLOUDSDK_AUTH_PLUGIN_GKE_GCLOUD_AUTH_PLUGIN=true" >> $GITHUB_ENV

    - name: Get GKE credentials
      run: |
        gcloud container clusters get-credentials ${{ env.GKE_CLUSTER }} --region ${{ env.GKE_ZONE }} --project ${{ env.PROJECT_NAME }}

    - name: Deploy to GKE
      run: |
        kubectl set image deployment/puppeteer-service puppeteer-service=asia-southeast2-docker.pkg.dev/${{ env.PROJECT_NAME }}/terang-ai-registry/${{ env.IMAGE_NAME }}:${{ github.sha }}
        kubectl rollout status deployment/puppeteer-service

    - name: Verify deployment
      run: |
        kubectl get pods -l app=puppeteer-service
        kubectl get service puppeteer-service