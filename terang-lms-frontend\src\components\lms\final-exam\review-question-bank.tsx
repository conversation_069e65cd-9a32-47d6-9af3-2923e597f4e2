import React from 'react';
import { <PERSON>, <PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { CheckmarkCircle01Icon as CheckCircle2Icon, Cancel01Icon as XCircleIcon, Flag01Icon as FlagIcon } from 'hugeicons-react';
import { Question as QuestionType } from '@/types/lms';

interface ReviewQuestionBankProps {
  questions: QuestionType[];
  currentQuestion: number;
  results: { [key: string]: boolean };
  onQuestionSelect: (questionIndex: number) => void;
  flaggedQuestions: Set<number>;
}

export const ReviewQuestionBank: React.FC<ReviewQuestionBankProps> = ({
  questions,
  currentQuestion,
  results,
  onQuestionSelect,
  flaggedQuestions
}) => {
  const correctCount = Object.values(results).filter(Boolean).length;
  const incorrectCount = questions.length - correctCount;

  return (
    <Card className="sticky top-24">
      <CardHeader className="pb-4">
        <CardTitle className="text-lg">Review Soal</CardTitle>
        <div className="grid grid-cols-2 gap-2 text-sm">
          <div className="flex items-center space-x-2">
            <CheckCircle2Icon className="h-4 w-4 text-green-600" />
            <span className="text-green-600 font-medium">{correctCount} Benar</span>
          </div>
          <div className="flex items-center space-x-2">
            <XCircleIcon className="h-4 w-4 text-red-600" />
            <span className="text-red-600 font-medium">{incorrectCount} Salah</span>
          </div>
        </div>
      </CardHeader>
      
      <CardContent className="pt-0">
        <div className="space-y-3">
          <div>
            <h4 className="text-sm font-medium text-gray-700 mb-2">Navigasi Soal</h4>
            <div className="grid grid-cols-5 gap-2">
              {questions.map((question, index) => {
                const isCorrect = results[question.id];
                const isCurrent = index === currentQuestion;
                const isFlagged = flaggedQuestions.has(index);
                
                return (
                  <Button
                    key={question.id}
                    variant={isCurrent ? 'default' : 'outline'}
                    size="sm"
                    className={`
                      relative h-10 w-10 p-0 text-xs font-medium
                      ${isCurrent 
                        ? 'bg-blue-600 hover:bg-blue-700 text-white' 
                        : isCorrect 
                          ? 'border-green-500 bg-green-50 text-green-700 hover:bg-green-100'
                          : 'border-red-500 bg-red-50 text-red-700 hover:bg-red-100'
                      }
                    `}
                    onClick={() => onQuestionSelect(index)}
                  >
                    <span>{index + 1}</span>
                    {isFlagged && (
                      <FlagIcon className="absolute -top-1 -right-1 h-3 w-3 text-yellow-500" />
                    )}
                    <div className={`
                      absolute -bottom-1 -right-1 w-3 h-3 rounded-full border-2 border-white
                      ${isCorrect ? 'bg-green-500' : 'bg-red-500'}
                    `} />
                  </Button>
                );
              })}
            </div>
          </div>

          {/* Legend */}
          <div className="pt-3 border-t border-gray-200">
            <h4 className="text-sm font-medium text-gray-700 mb-2">Keterangan</h4>
            <div className="space-y-1 text-xs">
              <div className="flex items-center space-x-2">
                <div className="w-3 h-3 rounded-full bg-green-500"></div>
                <span className="text-gray-600">Jawaban Benar</span>
              </div>
              <div className="flex items-center space-x-2">
                <div className="w-3 h-3 rounded-full bg-red-500"></div>
                <span className="text-gray-600">Jawaban Salah</span>
              </div>
              <div className="flex items-center space-x-2">
                <FlagIcon className="h-3 w-3 text-yellow-500" />
                <span className="text-gray-600">Soal Ditandai</span>
              </div>
            </div>
          </div>

          {/* Quick Stats */}
          <div className="pt-3 border-t border-gray-200">
            <h4 className="text-sm font-medium text-gray-700 mb-2">Statistik</h4>
            <div className="space-y-2">
              <div className="flex justify-between text-sm">
                <span className="text-gray-600">Total Soal:</span>
                <span className="font-medium">{questions.length}</span>
              </div>
              <div className="flex justify-between text-sm">
                <span className="text-gray-600">Akurasi:</span>
                <span className="font-medium text-blue-600">
                  {Math.round((correctCount / questions.length) * 100)}%
                </span>
              </div>
              {flaggedQuestions.size > 0 && (
                <div className="flex justify-between text-sm">
                  <span className="text-gray-600">Ditandai:</span>
                  <span className="font-medium text-yellow-600">{flaggedQuestions.size}</span>
                </div>
              )}
            </div>
          </div>
        </div>
      </CardContent>
    </Card>
  );
};