{"name": "puppeteer-service", "version": "1.0.0", "description": "", "main": "index.js", "type": "module", "scripts": {"start": "node server.js", "dev": "node --watch server.js", "docker:build": "docker build -t puppeteer-service .", "docker:run": "docker run -p 3000:3000 puppeteer-service", "docker:dev": "docker run -p 3000:3000 -v $(pwd)/screenshots:/usr/src/app/screenshots puppeteer-service", "test": "echo \"Error: no test specified\" && exit 1"}, "keywords": [], "author": "", "license": "ISC", "dependencies": {"express": "^5.1.0", "puppeteer": "^24.16.1"}}