'use client';
import {
  Collapsible,
  CollapsibleContent,
  CollapsibleTrigger
} from '@/components/ui/collapsible';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuGroup,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger
} from '@/components/ui/dropdown-menu';
import {
  Sidebar,
  SidebarContent,
  SidebarFooter,
  SidebarGroup,
  SidebarGroupLabel,
  SidebarHeader,
  SidebarMenu,
  SidebarMenuButton,
  SidebarMenuItem,
  SidebarMenuSub,
  SidebarMenuSubButton,
  SidebarMenuSubItem,
  SidebarRail,
  useSidebar
} from '@/components/ui/sidebar';
import { UserAvatarProfile } from '@/components/user-avatar-profile';
import { getNavigationItems } from '@/config/navigation';
import { authStorage } from '@/lib/auth';
import { getNavItems } from '@/constants/data';
import { useMediaQuery } from '@/hooks/use-media-query';
import { ChevronRight, LogOut, Building2 } from 'lucide-react';
import Link from 'next/link';
import Image from 'next/image';
import { usePathname, useRouter } from 'next/navigation';
import * as React from 'react';
import { NavItem } from '@/types';
import { Icons } from '../icons';
import { RoleIndicator } from '../role-indicator';

export const company = {
  name: 'Acme Inc',
  logo: Building2,
  plan: 'Enterprise'
};

export default function AppSidebar() {
  const pathname = usePathname();
  const { isOpen } = useMediaQuery();
  const router = useRouter();
  const { state } = useSidebar();
  const [navItems, setNavItems] = React.useState<NavItem[]>([]);

  React.useEffect(() => {
    // Get user role and set appropriate navigation items
    const user = authStorage.getUser();
    if (user) {
      // Temporary: If user is student, use fallback navigation items
      // This is for demonstration or specific testing purposes.
      // In a production scenario, you would typically use role-based navigation.
      if (user.role === 'student') {
        const fallbackNavItems = getNavItems(pathname); // (Temporary)
        setNavItems(fallbackNavItems); // (Temporary)
      } else {
        const roleNavItems = getNavigationItems(user.role);
        setNavItems(roleNavItems);
      }
    } else {
      // Use fallback navigation items if no user is found
      const fallbackNavItems = getNavItems(pathname);
      setNavItems(fallbackNavItems);
    }
  }, [pathname]);

  const displayNavItems = navItems.length > 0 ? navItems : getNavItems(pathname);

  return (
    <Sidebar collapsible='icon'>
      <SidebarHeader className="p-4">
        <div className="flex items-center justify-center">
          <Link href="/">
            <Image
              src="/assets/logo-iai.png"
              alt="IAI Logo"
              width={state === 'collapsed' ? 48 : 160}
              height={state === 'collapsed' ? 48 : 60}
              className="object-contain transition-all duration-200 cursor-pointer hover:opacity-80"
              priority
            />
          </Link>
        </div>
        <div className={`mt-3 ${state === 'collapsed' ? 'hidden' : 'block'}`}>
          <RoleIndicator />
        </div>
      </SidebarHeader>
      <SidebarContent className='overflow-x-hidden'>
        <SidebarGroup>
          <SidebarGroupLabel>Overview</SidebarGroupLabel>
          <SidebarMenu className="gap-0">
            {displayNavItems.map((item: NavItem) => {
              const Icon = item.icon ? Icons[item.icon] : Icons.logo;
              return item?.items && item?.items?.length > 0 ? (
                <Collapsible
                  key={item.title}
                  asChild
                  defaultOpen={item.isActive}
                  className='group/collapsible'
                >
                  <SidebarMenuItem>
                    <CollapsibleTrigger asChild>
                      <SidebarMenuButton
                        tooltip={item.title}
                        isActive={item.items?.some(
                          (subItem) => pathname === subItem.url
                        )}
                        className="py-3 px-4 text-base font-medium h-auto"
                      >
                        {item.icon && <Icon />}
                        <span>{item.title}</span>
                        <ChevronRight className='ml-auto transition-transform duration-200 group-data-[state=open]/collapsible:rotate-90' />
                      </SidebarMenuButton>
                    </CollapsibleTrigger>
                    <CollapsibleContent>
                      <SidebarMenuSub>
                        {item.items?.map((subItem: NavItem) => (
                          <SidebarMenuSubItem key={subItem.title}>
                            <SidebarMenuSubButton
                              asChild
                              isActive={pathname === subItem.url}
                              className="py-2 px-6 text-sm font-medium"
                            >
                              <Link href={subItem.url}>
                                <span>{subItem.title}</span>
                              </Link>
                            </SidebarMenuSubButton>
                          </SidebarMenuSubItem>
                        ))}
                      </SidebarMenuSub>
                    </CollapsibleContent>
                  </SidebarMenuItem>
                </Collapsible>
              ) : (
                <SidebarMenuItem key={item.title}>
                  <SidebarMenuButton
                    asChild
                    tooltip={item.title}
                    isActive={pathname === item.url}
                    className="py-3 px-4 text-base font-medium h-auto"
                  >
                    <Link href={item.url}>
                      <Icon />
                      <span>{item.title}</span>
                    </Link>
                  </SidebarMenuButton>
                </SidebarMenuItem>
              );
            })}
          </SidebarMenu>
        </SidebarGroup>
      </SidebarContent>
      <SidebarFooter>
        <SidebarMenu>
          <SidebarMenuItem>
            <DropdownMenu>
              <DropdownMenuTrigger asChild>
                <SidebarMenuButton
                  size='lg'
                  className='data-[state=open]:bg-sidebar-accent data-[state=open]:text-sidebar-accent-foreground py-3 px-4 text-base font-medium h-auto'
                >
                  <UserAvatarProfile className='h-8 w-8 rounded-lg' showInfo />
                  <Icons.chevronRight className='ml-auto size-4' />
                </SidebarMenuButton>
              </DropdownMenuTrigger>
              <DropdownMenuContent
                className='w-(--radix-dropdown-menu-trigger-width) min-w-56 rounded-lg'
                side='bottom'
                align='end'
                sideOffset={4}
              >
                <DropdownMenuLabel className='p-0 font-normal'>
                  <div className='px-1 py-1.5'>
                    <UserAvatarProfile
                      className='h-8 w-8 rounded-lg'
                      showInfo
                    />
                  </div>
                </DropdownMenuLabel>
                <DropdownMenuSeparator />

                <DropdownMenuGroup>
                  <DropdownMenuItem
                    onClick={() => router.push('/dashboard/profile')}
                  >
                    <Icons.user className='mr-2 h-4 w-4' />
                    Profile
                  </DropdownMenuItem>
                  <DropdownMenuItem>
                    <Icons.billing className='mr-2 h-4 w-4' />
                    Billing
                  </DropdownMenuItem>
                  <DropdownMenuItem>
                    <Icons.help className='mr-2 h-4 w-4' />
                    Notifications
                  </DropdownMenuItem>
                </DropdownMenuGroup>
                <DropdownMenuSeparator />
                <DropdownMenuItem
                  onClick={() => {
                    authStorage.removeUser();
                    window.location.href = '/auth/sign-in';
                  }}
                >
                  <LogOut className='mr-2 h-4 w-4' />
                  <span>Logout</span>
                </DropdownMenuItem>
              </DropdownMenuContent>
            </DropdownMenu>
          </SidebarMenuItem>
        </SidebarMenu>
      </SidebarFooter>
      <SidebarRail />
    </Sidebar>
  );
}