import React from 'react';
import { Card, CardContent } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Target, BookMarked, Trophy, Clock, CheckCircle } from 'lucide-react';
import { QuizCardProps } from '@/types/lms';

export const QuizCard: React.FC<QuizCardProps> = ({
  quiz,
  isUnlocked,
  onStartQuiz
}) => {
  const getQuizTypeColor = () => {
    switch (quiz.type) {
      case 'chapter':
        return 'bg-blue-100 text-blue-700 border-blue-200';
      case 'module':
        return 'bg-purple-100 text-purple-700 border-purple-200';
      case 'final':
        return 'bg-red-100 text-red-700 border-red-200';
      default:
        return 'bg-gray-100 text-gray-700 border-gray-200';
    }
  };

  const getQuizIcon = () => {
    switch (quiz.type) {
      case 'chapter':
        return <Target className='h-4 w-4' />;
      case 'module':
        return <BookMarked className='h-4 w-4' />;
      case 'final':
        return <Trophy className='h-4 w-4' />;
      default:
        return <Target className='h-4 w-4' />;
    }
  };

  return (
    <Card className={`my-3 ml-6 border-2 ${!isUnlocked ? 'opacity-50' : ''}`}>
      <CardContent className='p-4'>
        <div className='flex items-center justify-between'>
          <div className='flex items-center space-x-3'>
            <div className={`rounded-lg p-2 ${getQuizTypeColor()}`}>
              {getQuizIcon()}
            </div>
            <div>
              <h4 className='font-medium'>{quiz.title}</h4>
              <div className='mt-1 flex items-center space-x-3'>
                <span className='text-sm text-gray-600'>
                  Min. Score: {quiz.minimumScore}%
                </span>
                {quiz.timeLimit && (
                  <span className='text-sm text-gray-600'>
                    <Clock className='mr-1 inline h-3 w-3' />
                    {quiz.timeLimit} min
                  </span>
                )}
                <span className='text-sm text-gray-600'>
                  Attempts: {quiz.attempts}/{quiz.maxAttempts}
                </span>
              </div>
            </div>
          </div>
          <div className='flex items-center space-x-2'>
            {quiz.isPassed && (
              <Badge className='bg-green-100 text-green-700'>
                <CheckCircle className='mr-1 h-3 w-3' />
                Passed ({quiz.lastScore}%)
              </Badge>
            )}
            <Button
              size='sm'
              disabled={!isUnlocked || quiz.attempts >= quiz.maxAttempts}
              onClick={onStartQuiz}
              variant={quiz.isPassed ? 'outline' : 'default'}
            >
              {quiz.attempts === 0
                ? 'Start Quiz'
                : quiz.isPassed
                  ? 'Retake'
                  : 'Continue'}
            </Button>
          </div>
        </div>
      </CardContent>
    </Card>
  );
};
