'use client';

import React, { useState } from 'react';
import { <PERSON><PERSON> } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Switch } from '@/components/ui/switch';
import { Dialog, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog';
import { AlertDialog, AlertDialogAction, AlertDialogCancel, AlertDialogContent, AlertDialogDescription, AlertDialogFooter, AlertDialogHeader, AlertDialogTitle, AlertDialogTrigger } from '@/components/ui/alert-dialog';
import { 
  Plus, 
  GripVertical, 
  Edit, 
  Trash2, 
  <PERSON><PERSON><PERSON>, 
  FileText, 
  HelpCircle,
  ChevronDown,
  ChevronRight
} from 'lucide-react';
import { cn } from '@/lib/utils';
import { CourseData, ModuleData, ChapterData } from '../course-creation-wizard';
import { toast } from 'sonner';

interface ModuleStructureStepProps {
  data: CourseData;
  onUpdate: (updates: Partial<CourseData>) => void;
}

export function ModuleStructureStep({ data, onUpdate }: ModuleStructureStepProps) {
  const [expandedModules, setExpandedModules] = useState<Set<string>>(new Set());
  const [editingModule, setEditingModule] = useState<ModuleData | null>(null);
  const [editingChapter, setEditingChapter] = useState<{ moduleId: string; chapter: ChapterData | null }>({
    moduleId: '',
    chapter: null
  });
  const [isModuleDialogOpen, setIsModuleDialogOpen] = useState(false);
  const [isChapterDialogOpen, setIsChapterDialogOpen] = useState(false);

  const toggleModuleExpansion = (moduleId: string) => {
    const newExpanded = new Set(expandedModules);
    if (newExpanded.has(moduleId)) {
      newExpanded.delete(moduleId);
    } else {
      newExpanded.add(moduleId);
    }
    setExpandedModules(newExpanded);
  };

  const createNewModule = () => {
    const newModule: ModuleData = {
      id: `module-${Date.now()}`,
      name: '',
      description: '',
      orderIndex: data.modules.length,
      chapters: [],
      hasModuleQuiz: false
    };
    setEditingModule(newModule);
    setIsModuleDialogOpen(true);
  };

  const editModule = (moduleItem: ModuleData) => {
    setEditingModule({ ...moduleItem });
    setIsModuleDialogOpen(true);
  };

  const saveModule = () => {
    if (!editingModule || !editingModule.name.trim()) {
      toast.error('Nama modul harus diisi');
      return;
    }

    const updatedModules = [...data.modules];
    const existingIndex = updatedModules.findIndex(m => m.id === editingModule.id);
    
    if (existingIndex >= 0) {
      updatedModules[existingIndex] = editingModule;
      toast.success('Modul berhasil diperbarui');
    } else {
      updatedModules.push(editingModule);
      toast.success('Modul berhasil ditambahkan');
    }

    onUpdate({ modules: updatedModules });
    setIsModuleDialogOpen(false);
    setEditingModule(null);
  };

  const deleteModule = (moduleId: string) => {
    const updatedModules = data.modules
      .filter(m => m.id !== moduleId)
      .map((m, index) => ({ ...m, orderIndex: index }));
    
    onUpdate({ modules: updatedModules });
    toast.success('Modul berhasil dihapus');
  };

  const createNewChapter = (moduleId: string) => {
    const moduleItem = data.modules.find(m => m.id === moduleId);
    if (!moduleItem) return;

    const newChapter: ChapterData = {
      id: `chapter-${Date.now()}`,
      name: '',
      content: [],
      orderIndex: moduleItem.chapters.length,
      hasChapterQuiz: false
    };
    
    setEditingChapter({ moduleId, chapter: newChapter });
    setIsChapterDialogOpen(true);
  };

  const editChapter = (moduleId: string, chapter: ChapterData) => {
    setEditingChapter({ moduleId, chapter: { ...chapter } });
    setIsChapterDialogOpen(true);
  };

  const saveChapter = () => {
    if (!editingChapter.chapter || !editingChapter.chapter.name.trim()) {
      toast.error('Nama chapter harus diisi');
      return;
    }

    const updatedModules = data.modules.map(moduleItem => {
      if (moduleItem.id === editingChapter.moduleId) {
        const updatedChapters = [...moduleItem.chapters];
        const existingIndex = updatedChapters.findIndex(c => c.id === editingChapter.chapter!.id);
        
        if (existingIndex >= 0) {
          updatedChapters[existingIndex] = editingChapter.chapter!;
        } else {
          updatedChapters.push(editingChapter.chapter!);
        }
        
        return { ...moduleItem, chapters: updatedChapters };
      }
      return moduleItem;
    });

    onUpdate({ modules: updatedModules });
    setIsChapterDialogOpen(false);
    setEditingChapter({ moduleId: '', chapter: null });
    toast.success('Chapter berhasil disimpan');
  };

  const deleteChapter = (moduleId: string, chapterId: string) => {
    const updatedModules = data.modules.map(moduleItem => {
      if (moduleItem.id === moduleId) {
        const updatedChapters = moduleItem.chapters
          .filter(c => c.id !== chapterId)
          .map((c, index) => ({ ...c, orderIndex: index }));
        return { ...moduleItem, chapters: updatedChapters };
      }
      return moduleItem;
    });

    onUpdate({ modules: updatedModules });
    toast.success('Chapter berhasil dihapus');
  };

  const moveModule = (moduleId: string, direction: 'up' | 'down') => {
    const currentIndex = data.modules.findIndex(m => m.id === moduleId);
    if (currentIndex === -1) return;

    const newIndex = direction === 'up' ? currentIndex - 1 : currentIndex + 1;
    if (newIndex < 0 || newIndex >= data.modules.length) return;

    const updatedModules = [...data.modules];
    [updatedModules[currentIndex], updatedModules[newIndex]] = 
    [updatedModules[newIndex], updatedModules[currentIndex]];
    
    // Update order indices
    updatedModules.forEach((moduleItem, index) => {
      moduleItem.orderIndex = index;
    });

    onUpdate({ modules: updatedModules });
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h3 className="text-lg font-semibold">Struktur Modul Course</h3>
          <p className="text-sm text-muted-foreground">
            Buat modul dan chapter untuk mengorganisir konten course
          </p>
        </div>
        <Button onClick={createNewModule}>
          <Plus className="w-4 h-4 mr-2" />
          Tambah Modul
        </Button>
      </div>

      {/* Modules List */}
      {data.modules.length === 0 ? (
        <Card>
          <CardContent className="flex flex-col items-center justify-center py-12">
            <BookOpen className="w-12 h-12 text-muted-foreground mb-4" />
            <h3 className="text-lg font-semibold mb-2">Belum ada modul</h3>
            <p className="text-muted-foreground text-center mb-4">
              Mulai dengan membuat modul pertama untuk course Anda
            </p>
            <Button onClick={createNewModule}>
              <Plus className="w-4 h-4 mr-2" />
              Buat Modul Pertama
            </Button>
          </CardContent>
        </Card>
      ) : (
        <div className="space-y-4">
          {data.modules.map((moduleItem, moduleIndex) => {
            const isExpanded = expandedModules.has(moduleItem.id);
            
            return (
              <Card key={moduleItem.id} className="overflow-hidden">
                <CardHeader className="pb-3">
                  <div className="flex items-center justify-between">
                    <div className="flex items-center space-x-3">
                      <div className="flex items-center space-x-2">
                        <GripVertical className="w-4 h-4 text-muted-foreground cursor-move" />
                        <Badge variant="outline">Modul {moduleIndex + 1}</Badge>
                      </div>
                      <div>
                        <CardTitle className="text-base">{moduleItem.name || 'Modul Tanpa Nama'}</CardTitle>
                        {moduleItem.description && (
                          <CardDescription className="mt-1">
                            {moduleItem.description}
                          </CardDescription>
                        )}
                      </div>
                    </div>
                    
                    <div className="flex items-center space-x-2">
                      {moduleItem.hasModuleQuiz && (
                        <Badge variant="secondary">
                          <HelpCircle className="w-3 h-3 mr-1" />
                          Quiz Modul
                        </Badge>
                      )}
                      <Badge variant="outline">
                        {moduleItem.chapters.length} Chapter
                      </Badge>
                      
                      <div className="flex items-center space-x-1">
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={() => moveModule(moduleItem.id, 'up')}
                          disabled={moduleIndex === 0}
                        >
                          ↑
                        </Button>
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={() => moveModule(moduleItem.id, 'down')}
                          disabled={moduleIndex === data.modules.length - 1}
                        >
                          ↓
                        </Button>
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={() => editModule(moduleItem)}
                        >
                          <Edit className="w-4 h-4" />
                        </Button>
                        <AlertDialog>
                          <AlertDialogTrigger asChild>
                            <Button variant="ghost" size="sm">
                              <Trash2 className="w-4 h-4" />
                            </Button>
                          </AlertDialogTrigger>
                          <AlertDialogContent>
                            <AlertDialogHeader>
                              <AlertDialogTitle>Hapus Modul</AlertDialogTitle>
                              <AlertDialogDescription>
                                Apakah Anda yakin ingin menghapus modul &ldquo;{moduleItem.name}&rdquo;? 
                                Semua chapter di dalam modul ini juga akan terhapus.
                              </AlertDialogDescription>
                            </AlertDialogHeader>
                            <AlertDialogFooter>
                              <AlertDialogCancel>Batal</AlertDialogCancel>
                              <AlertDialogAction onClick={() => deleteModule(moduleItem.id)}>
                                Hapus
                              </AlertDialogAction>
                            </AlertDialogFooter>
                          </AlertDialogContent>
                        </AlertDialog>
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={() => toggleModuleExpansion(moduleItem.id)}
                        >
                          {isExpanded ? (
                            <ChevronDown className="w-4 h-4" />
                          ) : (
                            <ChevronRight className="w-4 h-4" />
                          )}
                        </Button>
                      </div>
                    </div>
                  </div>
                </CardHeader>
                
                {isExpanded && (
                  <CardContent className="pt-0">
                    <div className="space-y-3">
                      <div className="flex items-center justify-between">
                        <h4 className="text-sm font-medium">Chapters</h4>
                        <Button 
                          variant="outline" 
                          size="sm"
                          onClick={() => createNewChapter(moduleItem.id)}
                        >
                          <Plus className="w-4 h-4 mr-2" />
                          Tambah Chapter
                        </Button>
                      </div>
                      
                      {moduleItem.chapters.length === 0 ? (
                        <div className="text-center py-8 text-muted-foreground">
                          <FileText className="w-8 h-8 mx-auto mb-2" />
                          <p className="text-sm">Belum ada chapter</p>
                        </div>
                      ) : (
                        <div className="space-y-2">
                          {moduleItem.chapters.map((chapter, chapterIndex) => (
                            <div 
                              key={chapter.id}
                              className="flex items-center justify-between p-3 bg-muted/50 rounded-lg"
                            >
                              <div className="flex items-center space-x-3">
                                <GripVertical className="w-4 h-4 text-muted-foreground cursor-move" />
                                <Badge variant="outline" className="text-xs">
                                  {chapterIndex + 1}
                                </Badge>
                                <div>
                                  <p className="text-sm font-medium">
                                    {chapter.name || 'Chapter Tanpa Nama'}
                                  </p>
                                  {chapter.hasChapterQuiz && (
                                    <Badge variant="secondary" className="text-xs mt-1">
                                      <HelpCircle className="w-3 h-3 mr-1" />
                                      Quiz
                                    </Badge>
                                  )}
                                </div>
                              </div>
                              
                              <div className="flex items-center space-x-1">
                                <Button
                                  variant="ghost"
                                  size="sm"
                                  onClick={() => editChapter(moduleItem.id, chapter)}
                                >
                                  <Edit className="w-4 h-4" />
                                </Button>
                                <AlertDialog>
                                  <AlertDialogTrigger asChild>
                                    <Button variant="ghost" size="sm">
                                      <Trash2 className="w-4 h-4" />
                                    </Button>
                                  </AlertDialogTrigger>
                                  <AlertDialogContent>
                                    <AlertDialogHeader>
                                      <AlertDialogTitle>Hapus Chapter</AlertDialogTitle>
                                      <AlertDialogDescription>
                                        Apakah Anda yakin ingin menghapus chapter &ldquo;{chapter.name}&rdquo;?
                                      </AlertDialogDescription>
                                    </AlertDialogHeader>
                                    <AlertDialogFooter>
                                      <AlertDialogCancel>Batal</AlertDialogCancel>
                                      <AlertDialogAction 
                                        onClick={() => deleteChapter(moduleItem.id, chapter.id)}
                                      >
                                        Hapus
                                      </AlertDialogAction>
                                    </AlertDialogFooter>
                                  </AlertDialogContent>
                                </AlertDialog>
                              </div>
                            </div>
                          ))}
                        </div>
                      )}
                    </div>
                  </CardContent>
                )}
              </Card>
            );
          })}
        </div>
      )}

      {/* Module Dialog */}
      <Dialog open={isModuleDialogOpen} onOpenChange={setIsModuleDialogOpen}>
        <DialogContent className="sm:max-w-md">
          <DialogHeader>
            <DialogTitle>
              {editingModule?.name ? 'Edit Modul' : 'Tambah Modul Baru'}
            </DialogTitle>
            <DialogDescription>
              Isi informasi dasar untuk modul ini
            </DialogDescription>
          </DialogHeader>
          
          <div className="space-y-4">
            <div className="space-y-2">
              <Label htmlFor="moduleName">Nama Modul *</Label>
              <Input
                id="moduleName"
                placeholder="Masukkan nama modul"
                value={editingModule?.name || ''}
                onChange={(e) => setEditingModule(prev => 
                  prev ? { ...prev, name: e.target.value } : null
                )}
              />
            </div>
            
            <div className="space-y-2">
              <Label htmlFor="moduleDescription">Deskripsi</Label>
              <Textarea
                id="moduleDescription"
                placeholder="Jelaskan tentang modul ini..."
                value={editingModule?.description || ''}
                onChange={(e) => setEditingModule(prev => 
                  prev ? { ...prev, description: e.target.value } : null
                )}
                rows={3}
              />
            </div>
            
            <div className="flex items-center space-x-2">
              <Switch
                id="hasModuleQuiz"
                checked={editingModule?.hasModuleQuiz || false}
                onCheckedChange={(checked) => setEditingModule(prev => 
                  prev ? { ...prev, hasModuleQuiz: checked } : null
                )}
              />
              <Label htmlFor="hasModuleQuiz">Tambahkan quiz di akhir modul</Label>
            </div>
          </div>
          
          <DialogFooter>
            <Button variant="outline" onClick={() => setIsModuleDialogOpen(false)}>
              Batal
            </Button>
            <Button onClick={saveModule}>
              {editingModule?.name ? 'Perbarui' : 'Tambah'} Modul
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* Chapter Dialog */}
      <Dialog open={isChapterDialogOpen} onOpenChange={setIsChapterDialogOpen}>
        <DialogContent className="sm:max-w-md">
          <DialogHeader>
            <DialogTitle>
              {editingChapter.chapter?.name ? 'Edit Chapter' : 'Tambah Chapter Baru'}
            </DialogTitle>
            <DialogDescription>
              Isi informasi dasar untuk chapter ini
            </DialogDescription>
          </DialogHeader>
          
          <div className="space-y-4">
            <div className="space-y-2">
              <Label htmlFor="chapterName">Nama Chapter *</Label>
              <Input
                id="chapterName"
                placeholder="Masukkan nama chapter"
                value={editingChapter.chapter?.name || ''}
                onChange={(e) => setEditingChapter(prev => ({
                  ...prev,
                  chapter: prev.chapter ? { ...prev.chapter, name: e.target.value } : null
                }))}
              />
            </div>
            
            <div className="flex items-center space-x-2">
              <Switch
                id="hasChapterQuiz"
                checked={editingChapter.chapter?.hasChapterQuiz || false}
                onCheckedChange={(checked) => setEditingChapter(prev => ({
                  ...prev,
                  chapter: prev.chapter ? { ...prev.chapter, hasChapterQuiz: checked } : null
                }))}
              />
              <Label htmlFor="hasChapterQuiz">Tambahkan quiz untuk chapter ini</Label>
            </div>
          </div>
          
          <DialogFooter>
            <Button variant="outline" onClick={() => setIsChapterDialogOpen(false)}>
              Batal
            </Button>
            <Button onClick={saveChapter}>
              {editingChapter.chapter?.name ? 'Perbarui' : 'Tambah'} Chapter
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* Summary */}
      {data.modules.length > 0 && (
        <Card>
          <CardHeader>
            <CardTitle className="text-lg">Ringkasan Struktur</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-center">
              <div>
                <div className="text-2xl font-bold text-primary">{data.modules.length}</div>
                <div className="text-sm text-muted-foreground">Modul</div>
              </div>
              <div>
                <div className="text-2xl font-bold text-primary">
                  {data.modules.reduce((acc, moduleItem) => acc + moduleItem.chapters.length, 0)}
                </div>
                <div className="text-sm text-muted-foreground">Chapter</div>
              </div>
              <div>
                <div className="text-2xl font-bold text-primary">
                  {data.modules.filter(m => m.hasModuleQuiz).length}
                </div>
                <div className="text-sm text-muted-foreground">Quiz Modul</div>
              </div>
              <div>
                <div className="text-2xl font-bold text-primary">
                  {data.modules.reduce((acc, moduleItem) => 
                    acc + moduleItem.chapters.filter(c => c.hasChapterQuiz).length, 0
                  )}
                </div>
                <div className="text-sm text-muted-foreground">Quiz Chapter</div>
              </div>
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  );
}