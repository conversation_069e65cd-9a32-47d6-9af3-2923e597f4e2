import { CourseData, ModuleData, ChapterData, QuizData, QuestionData } from '@/components/course/course-creation-wizard';

// AI Generation specific types
export interface AIGenerationConfig {
  courseName?: string;
  courseDescription?: string;
  targetAudience?: string;
  difficulty?: 'beginner' | 'intermediate' | 'advanced';
  maxModules?: number;
  maxChaptersPerModule?: number;
  includeQuizzes?: boolean;
  includeFinalExam?: boolean;
}

// Course outline from AI
export interface AICourseOutline {
  courseName: string;
  description: string;
  modules: AIModuleOutline[];
  hasFinalExam: boolean;
  category?: string;
  difficulty?: string;
  estimatedDuration?: number;
}

export interface AIModuleOutline {
  name: string;
  description: string;
  chapters: AIChapterOutline[];
  hasModuleQuiz: boolean;
}

export interface AIChapterOutline {
  name: string;
  description: string;
  hasQuiz: boolean;
}

// Generated content from AI
export interface AIGeneratedContent {
  content: Array<{ id: number; type: 'text'; value: string }>; // Array of content blocks
  quiz?: AIGeneratedQuiz;
}

export interface AIGeneratedQuiz {
  name: string; // Changed from 'title' back to 'name'
  description: string;
  timeLimit: number;
  minimumScore: number;
  questions: AIGeneratedQuestion[];
}

export interface AIGeneratedQuestion {
  orderIndex: number;
  type: 'multiple_choice' | 'true_false' | 'essay';
  question: Array<{ id: number; type: 'text'; value: string }>;
  options?: Array<{ id: number; isCorrect: boolean; content: Array<{ id: number; type: 'text'; value: string }> }>;
  essayAnswer: string;
  explanation: Array<{ id: number; type: 'text'; value: string }>;
  points: number;
}

// Progress tracking for sequential generation
export interface GenerationProgress {
  currentStep: string;
  totalSteps: number;
  completedSteps: number;
  isGenerating: boolean;
  error?: string;
}

export interface GenerationStep {
  id: string;
  name: string;
  type: 'module' | 'chapter' | 'quiz' | 'final_exam';
  status: 'pending' | 'generating' | 'completed' | 'error';
  moduleIndex?: number;
  chapterIndex?: number;
}

// Conversion utilities types
export interface ConversionContext {
  courseData: CourseData;
  pdfFile: File;
  outline: AICourseOutline;
}

// Batch generation types
export interface BatchGenerationRequest {
  chapters: Array<{
    name: string;
    description: string;
    moduleContext: string;
  }>;
  courseContext: string;
}

export interface BatchGenerationResult {
  chapterName: string;
  content: AIGeneratedContent;
  success: boolean;
  error?: string;
}

// Stream update callback type
export type StreamUpdateCallback = (chunk: string) => void;

// Error types
export class AIGenerationError extends Error {
  constructor(
    message: string,
    public code: string,
    public details?: any
  ) {
    super(message);
    this.name = 'AIGenerationError';
  }
}

export interface AIErrorDetails {
  step: string;
  originalError: any;
  retryable: boolean;
}