import { NextRequest, NextResponse } from 'next/server';
import bcrypt from 'bcryptjs';
import { query } from '@/lib/db/raw';
import { RegisterData, ApiResponse, AuthUser } from '@/types/database';

export async function POST(request: NextRequest) {
  try {
    const body: RegisterData = await request.json();
    const { name, email, password, role, institutionId } = body;

    if (!name || !email || !password || !role) {
      return NextResponse.json(
        {
          success: false,
          error: 'Name, email, password, and role are required'
        } as ApiResponse,
        { status: 400 }
      );
    }

    // Check if user already exists
    const existingUserResult =
      await query`SELECT id FROM users WHERE email = ${email.toLowerCase()}`;
    const existingUser = existingUserResult[0];

    if (existingUser) {
      return NextResponse.json(
        {
          success: false,
          error: 'User with this email already exists'
        } as ApiResponse,
        { status: 409 }
      );
    }

    // Hash password
    const saltRounds = 12;
    const hashedPassword = await bcrypt.hash(password, saltRounds);

    // Create new user - institutionId is always null for Teacher and Student roles
    const finalInstitutionId = (role === 'teacher' || role === 'student') ? null : (institutionId || null);
    const newUserResult = await query`
      INSERT INTO users (name, email, password, role, institution_id)
      VALUES (${name}, ${email.toLowerCase()}, ${hashedPassword}, ${role}, ${finalInstitutionId})
      RETURNING id, name, email, role, institution_id
    `;
    const newUser = newUserResult;

    if (!newUser[0]) {
      return NextResponse.json(
        { success: false, error: 'Failed to create user' } as ApiResponse,
        { status: 500 }
      );
    }

    // Create auth user object (without password)
    const authUser: AuthUser = {
      id: newUser[0].id,
      name: newUser[0].name,
      email: newUser[0].email,
      role: newUser[0].role,
      institutionId: newUser[0].institutionId || undefined
    };

    return NextResponse.json({
      success: true,
      data: { user: authUser },
      message: 'User created successfully'
    } as ApiResponse);
  } catch (error) {
    console.error('Sign up error:', error);
    return NextResponse.json(
      { success: false, error: 'Internal server error' } as ApiResponse,
      { status: 500 }
    );
  }
}
