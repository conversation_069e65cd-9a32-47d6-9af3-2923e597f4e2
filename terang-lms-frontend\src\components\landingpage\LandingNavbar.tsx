'use client';

import React, { useState } from 'react';
import Link from 'next/link';
import Image from 'next/image';
import { Button } from '@/components/ui/button';
import {
  Menu03Icon as MenuIcon,
  Cancel01Icon as XIcon,
  BookOpen01Icon as BookOpenIcon,
  Award01Icon as AwardIcon,
  UserIcon,
  Call02Icon as PhoneIcon
} from 'hugeicons-react';
import { useRouter } from 'next/navigation';
import { authStorage, getRedirectPath } from '@/lib/auth';
import { AuthUser } from '@/types/database';
import { LayoutDashboard } from 'lucide-react';
 
export default function LandingNavbar() {
  const [isMenuOpen, setIsMenuOpen] = useState(false);
  const router = useRouter();
  const [user, setUser] = useState<AuthUser | null>(null);
 
  React.useEffect(() => {
    setUser(authStorage.getUser());
  }, []);
 
  const navItems = [
    { name: '<PERSON><PERSON><PERSON>', href: '#home' },
    { name: 'Kurs<PERSON>', href: '#courses' },
    { name: 'Fitur', href: '#features' },
    { name: 'Tentang', href: '#about' },
    { name: 'Kontak', href: '#contact' }
  ];

  const handleScroll = (e: React.MouseEvent<HTMLAnchorElement>, href: string) => {
    if (href.startsWith('#')) {
      e.preventDefault();
      const element = document.getElementById(href.substring(1));
      if (element) {
        element.scrollIntoView({ behavior: 'smooth' });
      }
      setIsMenuOpen(false);
    }
  };

  return (
    <nav className="fixed top-0 left-0 right-0 z-50 bg-white/95 backdrop-blur-sm border-b border-gray-200 shadow-sm">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="flex justify-between items-center h-20">
          {/* Logo */}
          <div className="flex items-center space-x-3">
            <Link href="/" className="flex items-center space-x-3">
              <Image
                src="/assets/logo-iai.png"
                alt="IAI Logo"
                width={100}
                height={100}
                className="object-contain"
              />
              <div className="hidden sm:block">
                <div className="font-bold text-xl text-gray-900">Akademi IAI</div>
                <div className="text-xs text-gray-600">Sertifikasi Profesional</div>
              </div>
            </Link>
          </div>

          {/* Desktop Navigation */}
          <div className="hidden md:flex items-center space-x-8">
            {navItems.map((item) => (
              <a
                key={item.name}
                href={item.href}
                onClick={(e) => handleScroll(e, item.href)}
                className="text-gray-700 hover:text-blue-600 font-medium transition-colors duration-200 cursor-pointer"
              >
                {item.name}
              </a>
            ))}
          </div>

          {/* Desktop Actions */}
          <div className="hidden md:flex items-center space-x-4">
            {user ? (
              <Button
                onClick={() => router.push(getRedirectPath(user))}
                className="bg-black hover:bg-gray-800 text-white h-12 px-6 font-semibold"
              >
                <LayoutDashboard className="mr-2 h-5 w-5" />
                Masuk Dashboard
              </Button>
            ) : (
              <>
                <Button
                  variant="ghost"
                  onClick={() => router.push('/auth/sign-in')}
                  className="text-gray-700 hover:text-blue-600"
                >
                  Masuk
                </Button>
                <Button
                  onClick={() => router.push('/auth/sign-up')}
                  className="bg-black hover:bg-gray-800 text-white h-12 px-6 font-semibold"
                >
                  Daftar Sekarang
                </Button>
              </>
            )}
          </div>

          {/* Mobile menu button */}
          <div className="md:hidden">
            <Button
              variant="ghost"
              size="icon"
              onClick={() => setIsMenuOpen(!isMenuOpen)}
              className="text-gray-700"
            >
              {isMenuOpen ? (
                <XIcon className="h-6 w-6" />
              ) : (
                <MenuIcon className="h-6 w-6" />
              )}
            </Button>
          </div>
        </div>

        {/* Mobile Navigation Menu */}
        {isMenuOpen && (
          <div className="md:hidden">
            <div className="px-2 pt-2 pb-3 space-y-1 bg-white border-t border-gray-200">
              {navItems.map((item) => (
                <a
                  key={item.name}
                  href={item.href}
                  onClick={(e) => handleScroll(e, item.href)}
                  className="block px-3 py-2 text-gray-700 hover:text-blue-600 hover:bg-gray-50 rounded-md font-medium transition-colors duration-200 cursor-pointer"
                >
                  {item.name}
                </a>
              ))}
              
              {/* Mobile Actions */}
              <div className="pt-4 space-y-3">
                {user ? (
                  <Button
                    onClick={() => {
                      router.push(getRedirectPath(user));
                      setIsMenuOpen(false);
                    }}
                    className="w-full justify-center bg-black hover:bg-gray-800 text-white h-12 font-semibold"
                  >
                    <LayoutDashboard className="mr-2 h-5 w-5" />
                    Masuk Dashboard
                  </Button>
                ) : (
                  <>
                    <Button
                      variant="outline"
                      onClick={() => {
                        router.push('/auth/sign-in');
                        setIsMenuOpen(false);
                      }}
                      className="w-full justify-center"
                    >
                      Masuk
                    </Button>
                    <Button
                      onClick={() => {
                        router.push('/auth/sign-up');
                        setIsMenuOpen(false);
                      }}
                      className="w-full justify-center bg-black hover:bg-gray-800 text-white h-12 font-semibold"
                    >
                      Daftar Sekarang
                    </Button>
                  </>
                )}
              </div>
            </div>
          </div>
        )}
      </div>
    </nav>
  );
}