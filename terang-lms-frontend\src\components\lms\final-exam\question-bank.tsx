import React from 'react';
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Question } from '@/types/lms';

interface QuestionBankProps {
  questions: Question[];
  currentQuestion: number;
  answeredQuestions: Set<number>;
  onQuestionSelect: (questionIndex: number) => void;
  flaggedQuestions?: Set<number>;
  onToggleFlag?: (questionIndex: number) => void;
  showFlags?: boolean;
  onSubmit?: () => void;
  canSubmit?: boolean;
  isSubmitting?: boolean;
  reviewMode?: boolean;
  results?: { [key: string]: boolean };
}

export const QuestionBank: React.FC<QuestionBankProps> = ({
  questions,
  currentQuestion,
  answeredQuestions,
  onQuestionSelect,
  flaggedQuestions = new Set(),
  onToggleFlag,
  showFlags = true,
  onSubmit,
  canSubmit = false,
  isSubmitting = false,
  reviewMode = false,
  results = {}
}) => {
  const getQuestionStatus = (index: number) => {
    if (reviewMode) {
      const question = questions[index];
      const isCorrect = results[question.id];
      if (index === currentQuestion) {
        return isCorrect ? 'current-correct' : 'current-incorrect';
      }
      return isCorrect ? 'correct' : 'incorrect';
    }
    
    if (index === currentQuestion) return 'current';
    if (answeredQuestions.has(index)) return 'answered';
    return 'unanswered';
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'current':
        return 'bg-blue-600 text-white border-blue-600';
      case 'current-correct':
        return 'bg-green-600 text-white border-green-600 ring-2 ring-green-200';
      case 'current-incorrect':
        return 'bg-red-600 text-white border-red-600 ring-2 ring-red-200';
      case 'correct':
        return 'bg-green-100 text-green-800 border-green-300 hover:bg-green-200';
      case 'incorrect':
        return 'bg-red-100 text-red-800 border-red-300 hover:bg-red-200';
      case 'answered':
        return 'bg-green-100 text-green-800 border-green-300 hover:bg-green-200';
      case 'unanswered':
        return 'bg-gray-50 text-gray-600 border-gray-300 hover:bg-gray-100';
      default:
        return 'bg-gray-50 text-gray-600 border-gray-300';
    }
  };

  const answeredCount = answeredQuestions.size;
  const unansweredCount = questions.length - answeredCount;
  
  // Count correct/incorrect in review mode
  const correctCount = reviewMode ? questions.filter(q => results[q.id]).length : 0;
  const incorrectCount = reviewMode ? questions.filter(q => !results[q.id]).length : 0;

  return (
    <Card className="h-fit sticky top-4">
      <CardHeader className="pb-4">
        <CardTitle className="text-lg">{reviewMode ? 'Review Soal' : 'Nomor Soal'}</CardTitle>
        {reviewMode ? (
          <div className="grid grid-cols-2 gap-4 text-sm">
            <div className="flex items-center space-x-2">
              <div className="w-4 h-4 bg-green-100 border border-green-300 rounded"></div>
              <span>Benar: {correctCount}</span>
            </div>
            <div className="flex items-center space-x-2">
              <div className="w-4 h-4 bg-red-100 border border-red-300 rounded"></div>
              <span>Salah: {incorrectCount}</span>
            </div>
          </div>
        ) : (
          <div className="grid grid-cols-2 gap-4 text-sm">
            <div className="flex items-center space-x-2">
              <div className="w-4 h-4 bg-green-100 border border-green-300 rounded"></div>
              <span>Terjawab: {answeredCount}</span>
            </div>
            <div className="flex items-center space-x-2">
              <div className="w-4 h-4 bg-gray-50 border border-gray-300 rounded"></div>
              <span>Belum: {unansweredCount}</span>
            </div>
          </div>
        )}
        {showFlags && flaggedQuestions.size > 0 && (
          <div className="flex items-center space-x-2 text-sm">
            <div className="w-4 h-4 bg-yellow-100 border border-yellow-400 rounded relative">
              <div className="absolute -top-1 -right-1 w-2 h-2 bg-yellow-500 rounded-full"></div>
            </div>
            <span>Ditandai: {flaggedQuestions.size}</span>
          </div>
        )}
      </CardHeader>
      
      <CardContent className="space-y-4">
        {/* Question Grid */}
        <div className="grid grid-cols-4 gap-2">
          {questions.map((_, index) => {
            const status = getQuestionStatus(index);
            const isFlagged = flaggedQuestions.has(index);
            
            return (
              <Button
                key={index}
                variant="outline"
                size="sm"
                className={`
                  relative h-12 w-12 p-0 font-medium transition-all
                  ${getStatusColor(status)}
                  ${isFlagged ? 'ring-2 ring-yellow-400' : ''}
                `}
                onClick={() => onQuestionSelect(index)}
              >
                {index + 1}
                {isFlagged && (
                  <div className="absolute -top-1 -right-1 w-3 h-3 bg-yellow-500 rounded-full border-2 border-white"></div>
                )}
              </Button>
            );
          })}
        </div>

        {/* Legend */}
        <div className="pt-4 border-t space-y-2 text-xs text-gray-600">
          {reviewMode ? (
            <>
              <div className="flex items-center space-x-2">
                <div className="w-3 h-3 bg-green-600 rounded"></div>
                <span>Soal saat ini (Benar)</span>
              </div>
              <div className="flex items-center space-x-2">
                <div className="w-3 h-3 bg-red-600 rounded"></div>
                <span>Soal saat ini (Salah)</span>
              </div>
              <div className="flex items-center space-x-2">
                <div className="w-3 h-3 bg-green-100 border border-green-300 rounded"></div>
                <span>Jawaban benar</span>
              </div>
              <div className="flex items-center space-x-2">
                <div className="w-3 h-3 bg-red-100 border border-red-300 rounded"></div>
                <span>Jawaban salah</span>
              </div>
            </>
          ) : (
            <>
              <div className="flex items-center space-x-2">
                <div className="w-3 h-3 bg-blue-600 rounded"></div>
                <span>Soal saat ini</span>
              </div>
              <div className="flex items-center space-x-2">
                <div className="w-3 h-3 bg-green-100 border border-green-300 rounded"></div>
                <span>Sudah dijawab</span>
              </div>
              <div className="flex items-center space-x-2">
                <div className="w-3 h-3 bg-gray-50 border border-gray-300 rounded"></div>
                <span>Belum dijawab</span>
              </div>
            </>
          )}
          {showFlags && (
            <div className="flex items-center space-x-2">
              <div className="w-3 h-3 bg-yellow-100 border border-yellow-400 rounded relative">
                <div className="absolute -top-0.5 -right-0.5 w-1.5 h-1.5 bg-yellow-500 rounded-full"></div>
              </div>
              <span>Ditandai untuk review</span>
            </div>
          )}
        </div>

        {/* Quick Actions */}
        {showFlags && onToggleFlag && (
          <div className="pt-4 border-t">
            <Button
              variant="outline"
              size="sm"
              className="w-full text-xs"
              onClick={() => onToggleFlag(currentQuestion)}
            >
              {flaggedQuestions.has(currentQuestion) ? 'Hapus Tanda' : 'Tandai Soal'}
            </Button>
          </div>
        )}

        {/* Submit Button */}
        {onSubmit && (
          <div className="pt-4 border-t">
            <Button
              onClick={onSubmit}
              disabled={!canSubmit || isSubmitting}
              className="w-full bg-blue-600 hover:bg-blue-700 text-white"
              size="lg"
            >
              {isSubmitting ? 'Menyerahkan...' : 'Submit Ujian'}
            </Button>
          </div>
        )}
      </CardContent>
    </Card>
  );
};