import React from 'react';
import { <PERSON><PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { 
  Target, 
  Clock, 
  Award, 
  BookOpen, 
  CheckCircle,
  AlertCircle,
  Trophy,
  FileText
} from 'lucide-react';
import { Quiz } from '@/types/lms';

interface QuizIntroductionProps {
  quiz: Quiz;
  quizType: 'chapter' | 'module' | 'final';
  onStartQuiz: () => void;
  onCancel: () => void;
}

export const QuizIntroduction: React.FC<QuizIntroductionProps> = ({
  quiz,
  quizType,
  onStartQuiz,
  onCancel
}) => {
  const getQuizTypeInfo = () => {
    switch (quizType) {
      case 'chapter':
        return {
          title: 'Chapter Quiz',
          description: 'Kuis untuk menguji pemahaman Anda pada bab ini',
          icon: <BookOpen className="h-6 w-6" />,
          color: 'bg-blue-50 text-blue-700 border-blue-200'
        };
      case 'module':
        return {
          title: 'Module Quiz',
          description: 'Kuis untuk menguji pemahaman Anda pada modul ini',
          icon: <Target className="h-6 w-6" />,
          color: 'bg-green-50 text-green-700 border-green-200'
        };
      case 'final':
        return {
          title: 'Final Exam',
          description: 'Ujian akhir untuk menyelesaikan kursus ini',
          icon: <Trophy className="h-6 w-6" />,
          color: 'bg-purple-50 text-purple-700 border-purple-200'
        };
    }
  };

  const quizInfo = getQuizTypeInfo();

  return (
    <div className="max-w-4xl mx-auto">
      <div className="mb-6">
        <div className="flex items-center gap-3 mb-4">
          {quizInfo.icon}
          <div>
            <h1 className="text-2xl font-bold text-gray-900">
              {quizInfo.title}
            </h1>
            <p className="text-gray-600">{quizInfo.description}</p>
          </div>
        </div>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {/* Quiz Information */}
        <div className="lg:col-span-2">
          <Card className="mb-6">
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <FileText className="h-5 w-5" />
                Informasi Kuis
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-2 gap-4">
                <div className="flex items-center gap-2">
                  <Clock className="h-4 w-4 text-gray-500" />
                  <span className="text-sm text-gray-600">Durasi:</span>
                  <span className="font-medium">{quiz.timeLimit} menit</span>
                </div>
                <div className="flex items-center gap-2">
                  <Target className="h-4 w-4 text-gray-500" />
                  <span className="text-sm text-gray-600">Skor Minimum:</span>
                  <span className="font-medium">{quiz.minimumScore}%</span>
                </div>
                <div className="flex items-center gap-2">
                  <Award className="h-4 w-4 text-gray-500" />
                  <span className="text-sm text-gray-600">Percobaan:</span>
                  <span className="font-medium">
                    {quiz.attempts} / {quiz.maxAttempts}
                  </span>
                </div>
                <div className="flex items-center gap-2">
                  <CheckCircle className="h-4 w-4 text-gray-500" />
                  <span className="text-sm text-gray-600">Status:</span>
                  <Badge variant={quiz.isPassed ? "default" : "secondary"}>
                    {quiz.isPassed ? "Lulus" : "Belum Lulus"}
                  </Badge>
                </div>
              </div>
              
              {quiz.lastScore > 0 && (
                <div className="bg-gray-50 p-4 rounded-lg">
                  <h4 className="font-medium text-gray-900 mb-2">Skor Terakhir</h4>
                  <div className="flex items-center gap-2">
                    <span className="text-2xl font-bold text-blue-600">
                      {quiz.lastScore}%
                    </span>
                    <span className="text-sm text-gray-600">
                      ({quiz.attempts} percobaan)
                    </span>
                  </div>
                </div>
              )}
            </CardContent>
          </Card>

          {/* Quiz Instructions */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <AlertCircle className="h-5 w-5" />
                Petunjuk Kuis
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-3 text-sm text-gray-700">
                <div className="flex items-start gap-3">
                  <div className="w-6 h-6 bg-blue-100 text-blue-600 rounded-full flex items-center justify-center text-xs font-medium flex-shrink-0 mt-0.5">
                    1
                  </div>
                  <p>Bacalah setiap pertanyaan dengan teliti sebelum menjawab.</p>
                </div>
                <div className="flex items-start gap-3">
                  <div className="w-6 h-6 bg-blue-100 text-blue-600 rounded-full flex items-center justify-center text-xs font-medium flex-shrink-0 mt-0.5">
                    2
                  </div>
                  <p>Anda memiliki waktu {quiz.timeLimit} menit untuk menyelesaikan kuis ini.</p>
                </div>
                <div className="flex items-start gap-3">
                  <div className="w-6 h-6 bg-blue-100 text-blue-600 rounded-full flex items-center justify-center text-xs font-medium flex-shrink-0 mt-0.5">
                    3
                  </div>
                  <p>Skor minimum untuk lulus adalah {quiz.minimumScore}%.</p>
                </div>
                <div className="flex items-start gap-3">
                  <div className="w-6 h-6 bg-blue-100 text-blue-600 rounded-full flex items-center justify-center text-xs font-medium flex-shrink-0 mt-0.5">
                    4
                  </div>
                  <p>Anda dapat mencoba sebanyak {quiz.maxAttempts} kali.</p>
                </div>
                <div className="flex items-start gap-3">
                  <div className="w-6 h-6 bg-blue-100 text-blue-600 rounded-full flex items-center justify-center text-xs font-medium flex-shrink-0 mt-0.5">
                    5
                  </div>
                  <p>Pastikan koneksi internet Anda stabil selama mengerjakan kuis.</p>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Action Panel */}
        <div className="lg:col-span-1">
          <Card className={`${quizInfo.color} border-2`}>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                {quizInfo.icon}
                Siap Memulai?
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <p className="text-sm">
                Pastikan Anda sudah mempersiapkan diri dengan baik sebelum memulai kuis.
              </p>
              
              <div className="space-y-2">
                <Button 
                  onClick={onStartQuiz}
                  className="w-full"
                  size="lg"
                >
                  <Target className="h-4 w-4 mr-2" />
                  Mulai Kuis
                </Button>
                
                <Button 
                  variant="outline" 
                  onClick={onCancel}
                  className="w-full"
                >
                  Kembali
                </Button>
              </div>

              {quiz.attempts > 0 && (
                <div className="bg-white/50 p-3 rounded-lg">
                  <p className="text-xs text-gray-600 mb-1">Percobaan sebelumnya:</p>
                  <p className="text-sm font-medium">
                    Skor: {quiz.lastScore}% 
                    {quiz.isPassed ? (
                      <span className="text-green-600 ml-2">✓ Lulus</span>
                    ) : (
                      <span className="text-red-600 ml-2">✗ Belum Lulus</span>
                    )}
                  </p>
                </div>
              )}
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  );
};
