import { NextRequest, NextResponse } from 'next/server';
import { db } from '@/lib/db';
import { chapters, modules, courses, quizzes } from '@/lib/db/schema';
import { eq, and } from 'drizzle-orm';

// GET /api/chapters - Get chapters for a module
export async function GET(request: NextRequest) {
  try {
    const searchParams = request.nextUrl.searchParams;
    const moduleId = searchParams.get('moduleId');
    const teacherId = searchParams.get('teacherId');
    
    if (!moduleId) {
      return NextResponse.json({ error: 'Module ID required' }, { status: 400 });
    }

    // Verify module exists and teacher has access
    if (teacherId) {
      const moduleWithCourse = await db
        .select({
          moduleId: modules.id,
          courseId: modules.courseId,
          teacherId: courses.teacherId
        })
        .from(modules)
        .leftJoin(courses, eq(modules.courseId, courses.id))
        .where(
          and(
            eq(modules.id, parseInt(moduleId)),
            eq(courses.teacherId, parseInt(teacherId))
          )
        )
        .limit(1);

      if (moduleWithCourse.length === 0) {
        return NextResponse.json(
          { error: 'Module not found or access denied' },
          { status: 403 }
        );
      }
    }

    // Get chapters with quiz count
    const moduleChapters = await db
      .select()
      .from(chapters)
      .where(eq(chapters.moduleId, parseInt(moduleId)));

    const chaptersWithQuizCount = await Promise.all(
      moduleChapters.map(async (chapter) => {
        const quizCount = await db
          .select({ count: quizzes.id })
          .from(quizzes)
          .where(eq(quizzes.chapterId, chapter.id));

        return {
          ...chapter,
          content: chapter.content ? JSON.parse(chapter.content as string) : null,
          quizCount: quizCount.length
        };
      })
    );

    return NextResponse.json({ chapters: chaptersWithQuizCount });
  } catch (error) {
    console.error('Error fetching chapters:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}

// POST /api/chapters - Create a new chapter
export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const {
      name,
      content,
      moduleId,
      teacherId,
      orderIndex
    } = body;

    // Validate required fields
    if (!name || !moduleId) {
      return NextResponse.json(
        { error: 'Name and module ID are required' },
        { status: 400 }
      );
    }

    // Verify module exists and teacher has access
    const moduleWithCourse = await db
      .select({
        moduleId: modules.id,
        courseId: modules.courseId,
        teacherId: courses.teacherId
      })
      .from(modules)
      .leftJoin(courses, eq(modules.courseId, courses.id))
      .where(eq(modules.id, moduleId))
      .limit(1);

    if (moduleWithCourse.length === 0) {
      return NextResponse.json({ error: 'Module not found' }, { status: 404 });
    }

    if (teacherId && moduleWithCourse[0].teacherId !== teacherId) {
      return NextResponse.json(
        { error: 'Not authorized to add chapters to this module' },
        { status: 403 }
      );
    }

    // If no orderIndex provided, set it to the next available
    let finalOrderIndex = orderIndex;
    if (finalOrderIndex === undefined) {
      const existingChapters = await db
        .select({ orderIndex: chapters.orderIndex })
        .from(chapters)
        .where(eq(chapters.moduleId, moduleId));
      
      finalOrderIndex = existingChapters.length > 0 
        ? Math.max(...existingChapters.map(c => c.orderIndex || 0)) + 1 
        : 1;
    }

    // Create the chapter
    const newChapter = await db
      .insert(chapters)
      .values({
        name,
        content: content ? JSON.stringify(content) : null,
        moduleId,
        orderIndex: finalOrderIndex
      })
      .returning();

    return NextResponse.json(
      {
        chapter: {
          ...newChapter[0],
          content: newChapter[0].content, // Drizzle ORM handles JSON parsing
        },
        message: 'Chapter created successfully'
      },
      { status: 201 }
    );
  } catch (error) {
    console.error('Error creating chapter:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}