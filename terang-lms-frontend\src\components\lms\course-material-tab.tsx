'use client';

import React from 'react';
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';
import {
  BookOpen01Icon as BookOpenIcon,
  PlayIcon,
  CheckmarkCircle01Icon as CheckCircleIcon,
  Locker01Icon as LockIcon,
  Clock01Icon as ClockIcon
} from 'hugeicons-react';
import { Course } from '@/types/lms';

interface CourseMaterialTabProps {
  courseData: Course;
  onStartLearning: () => void;
}

const CourseMaterialTab: React.FC<CourseMaterialTabProps> = ({ 
  courseData, 
  onStartLearning 
}) => {
  const getModuleProgress = (module: any) => {
    const totalChapters = module.chapters.length;
    const completedChapters = module.chapters.filter(
      (chapter: any) => chapter.contents.every((content: any) => content.isCompleted) && chapter.quiz.isPassed
    ).length;
    return totalChapters > 0 ? (completedChapters / totalChapters) * 100 : 0;
  };

  const getChapterStatus = (chapter: any) => {
    const allContentsCompleted = chapter.contents.every((content: any) => content.isCompleted);
    const quizPassed = chapter.quiz.isPassed;
    
    if (allContentsCompleted && quizPassed) {
      return 'completed';
    } else if (chapter.isUnlocked) {
      return 'in-progress';
    } else {
      return 'locked';
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'completed':
        return <CheckCircleIcon className="h-4 w-4 text-green-600" />;
      case 'in-progress':
        return <ClockIcon className="h-4 w-4 text-blue-600" />;
      case 'locked':
        return <LockIcon className="h-4 w-4 text-gray-400" />;
      default:
        return <ClockIcon className="h-4 w-4 text-gray-400" />;
    }
  };

  const getStatusText = (status: string) => {
    switch (status) {
      case 'completed':
        return 'Selesai';
      case 'in-progress':
        return 'Sedang Belajar';
      case 'locked':
        return 'Terkunci';
      default:
        return 'Belum Dimulai';
    }
  };

  const getStatusBadgeVariant = (status: string) => {
    switch (status) {
      case 'completed':
        return 'default';
      case 'in-progress':
        return 'secondary';
      case 'locked':
        return 'outline';
      default:
        return 'outline';
    }
  };

  return (
    <div className="space-y-6">
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <BookOpenIcon className="h-5 w-5" />
            Materi Kursus
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-6">
          {/* Course Overview */}
          <div className="bg-gray-50 p-4 rounded-lg">
            <h3 className="font-semibold mb-2">{courseData.name}</h3>
            <p className="text-sm text-gray-600 mb-3">{courseData.description}</p>
            <div className="flex items-center justify-between">
              <div className="text-sm text-gray-500">
                {courseData.modules.length} Modul • {courseData.modules.reduce((total, module) => total + module.chapters.length, 0)} Bab
              </div>
              <Button onClick={onStartLearning} className="flex items-center gap-2">
                <PlayIcon className="h-4 w-4" />
                Mulai Pembelajaran
              </Button>
            </div>
          </div>

          {/* Modules */}
          <div className="space-y-4">
            {courseData.modules.map((module, moduleIndex) => {
              const moduleProgress = getModuleProgress(module);
              const isModuleUnlocked = module.isUnlocked;
              
              return (
                <Card key={module.id} className={`${!isModuleUnlocked ? 'opacity-60' : ''}`}>
                  <CardHeader>
                    <div className="flex items-center justify-between">
                      <div className="flex items-center gap-3">
                        <div className="flex items-center gap-2">
                          {isModuleUnlocked ? (
                            <CheckCircleIcon className="h-5 w-5 text-green-600" />
                          ) : (
                            <LockIcon className="h-5 w-5 text-gray-400" />
                          )}
                          <CardTitle className="text-lg">
                            Modul {moduleIndex + 1}: {module.title}
                          </CardTitle>
                        </div>
                        <Badge variant={isModuleUnlocked ? 'default' : 'outline'}>
                          {isModuleUnlocked ? 'Terbuka' : 'Terkunci'}
                        </Badge>
                      </div>
                      <div className="text-right">
                        <div className="text-sm text-gray-500">Progress</div>
                        <div className="text-sm font-medium">{Math.round(moduleProgress)}%</div>
                      </div>
                    </div>
                    <p className="text-sm text-gray-600 mt-2">{module.description}</p>
                    <Progress value={moduleProgress} className="mt-3 h-2" />
                  </CardHeader>
                  <CardContent>
                    <div className="space-y-3">
                      {module.chapters.map((chapter, chapterIndex) => {
                        const chapterStatus = getChapterStatus(chapter);
                        const statusIcon = getStatusIcon(chapterStatus);
                        const statusText = getStatusText(chapterStatus);
                        const statusBadgeVariant = getStatusBadgeVariant(chapterStatus);
                        
                        return (
                          <div key={chapter.id} className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                            <div className="flex items-center gap-3">
                              {statusIcon}
                              <div>
                                <div className="font-medium text-sm">
                                  Bab {chapterIndex + 1}: {chapter.title}
                                </div>
                                <div className="text-xs text-gray-500">
                                  {chapter.contents.length} konten
                                </div>
                              </div>
                            </div>
                            <div className="flex items-center gap-2">
                              <Badge variant={statusBadgeVariant} className="text-xs">
                                {statusText}
                              </Badge>
                              <div className="text-xs text-gray-500">
                                {Math.round(chapter.completionPercentage)}%
                              </div>
                            </div>
                          </div>
                        );
                      })}
                    </div>
                  </CardContent>
                </Card>
              );
            })}
          </div>

          {/* Final Exam */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <BookOpenIcon className="h-5 w-5" />
                Ujian Akhir
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                <div className="flex items-center gap-3">
                  <ClockIcon className="h-4 w-4 text-blue-600" />
                  <div>
                    <div className="font-medium">Ujian Akhir</div>
                    <div className="text-sm text-gray-500">
                      {courseData.finalExam.questions.length} soal
                    </div>
                  </div>
                </div>
                <Badge variant="outline">
                  {courseData.finalExam.isPassed ? 'Lulus' : 'Belum Lulus'}
                </Badge>
              </div>
            </CardContent>
          </Card>

          {/* Certificate */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <BookOpenIcon className="h-5 w-5" />
                Sertifikat
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                <div className="flex items-center gap-3">
                  {courseData.certificate.isEligible ? (
                    <CheckCircleIcon className="h-4 w-4 text-green-600" />
                  ) : (
                    <ClockIcon className="h-4 w-4 text-gray-400" />
                  )}
                  <div>
                    <div className="font-medium">Sertifikat Penyelesaian</div>
                    <div className="text-sm text-gray-500">
                      {courseData.certificate.isEligible ? 'Eligible' : 'Not Eligible'}
                    </div>
                  </div>
                </div>
                <Badge variant={courseData.certificate.isEligible ? 'default' : 'outline'}>
                  {courseData.certificate.isEligible ? 'Tersedia' : 'Belum Tersedia'}
                </Badge>
              </div>
            </CardContent>
          </Card>
        </CardContent>
      </Card>
    </div>
  );
};

export { CourseMaterialTab };
