import React from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { <PERSON><PERSON>, <PERSON><PERSON>Content, <PERSON><PERSON>List, TabsTrigger } from '@/components/ui/tabs';
import { CourseData } from '../course-creation-wizard';
import { AdmissionsStep } from './admissions-step';
import { AcademicsStep } from './academics-step';
import { TuitionFinancingStep } from './tuition-financing-step';
import { CareersStep } from './careers-step';
import { StudentExperienceStep } from './student-experience-step';

interface CourseDetailsStepProps {
  data: CourseData;
  onUpdate: (updates: Partial<CourseData>) => void;
}

export function CourseDetailsStep({ data, onUpdate }: CourseDetailsStepProps) {
  return (
    <Card>
      <CardHeader>
        <CardTitle>Detail Course</CardTitle>
        <CardDescription>
          Kelola detail penerimaan, akademik, pembia<PERSON>an, karir, dan pengalaman siswa.
        </CardDescription>
      </CardHeader>
      <CardContent>
        <Tabs defaultValue="admissions" className="w-full">
          <TabsList className="grid w-full grid-cols-5">
            <TabsTrigger value="admissions">Penerimaan</TabsTrigger>
            <TabsTrigger value="academics">Akademik</TabsTrigger>
            <TabsTrigger value="tuition-financing">Biaya & Pembiayaan</TabsTrigger>
            <TabsTrigger value="careers">Karir</TabsTrigger>
            <TabsTrigger value="student-experience">Pengalaman Siswa</TabsTrigger>
          </TabsList>
          <div className="h-[400px] overflow-y-auto pr-4"> {/* Fixed height with scroll */}
            <TabsContent value="admissions">
              <AdmissionsStep data={data} onUpdate={onUpdate} />
            </TabsContent>
            <TabsContent value="academics">
              <AcademicsStep data={data} onUpdate={onUpdate} />
            </TabsContent>
            <TabsContent value="tuition-financing">
              <TuitionFinancingStep data={data} onUpdate={onUpdate} />
            </TabsContent>
            <TabsContent value="careers">
              <CareersStep data={data} onUpdate={onUpdate} />
            </TabsContent>
            <TabsContent value="student-experience">
              <StudentExperienceStep data={data} onUpdate={onUpdate} />
            </TabsContent>
          </div>
        </Tabs>
      </CardContent>
    </Card>
  );
}