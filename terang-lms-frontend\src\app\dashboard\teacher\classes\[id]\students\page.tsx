'use client';

import { useState, useEffect } from 'react';
import { useRouter, useParams } from 'next/navigation';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle
} from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Badge } from '@/components/ui/badge';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow
} from '@/components/ui/table';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogTrigger
} from '@/components/ui/dialog';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue
} from '@/components/ui/select';
import {
  ArrowLeft,
  Search,
  UserPlus,
  Trash2,
  Loader2,
  Users
} from 'lucide-react';
import Link from 'next/link';
import { authStorage } from '@/lib/auth';
import { toast } from 'sonner';

interface Student {
  id: number;
  name: string;
  email: string;
  enrolledAt: string;
}

interface AvailableStudent {
  id: number;
  name: string;
  email: string;
}

interface ClassData {
  id: number;
  name: string;
  description: string;
}

export default function ClassStudentsPage() {
  const router = useRouter();
  const params = useParams();
  const classId = params.id as string;
  
  const [searchTerm, setSearchTerm] = useState('');
  const [students, setStudents] = useState<Student[]>([]);
  const [availableStudents, setAvailableStudents] = useState<AvailableStudent[]>([]);
  const [classData, setClassData] = useState<ClassData | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [isAddingStudent, setIsAddingStudent] = useState(false);
  const [isRemovingStudent, setIsRemovingStudent] = useState<number | null>(null);
  const [selectedStudentId, setSelectedStudentId] = useState<string>('');
  const [isDialogOpen, setIsDialogOpen] = useState(false);

  useEffect(() => {
    if (classId) {
      fetchClassData();
      fetchStudents();
      fetchAvailableStudents();
    }
  }, [classId]);

  const fetchClassData = async () => {
    try {
      const user = authStorage.getUser();
      if (!user) return;

      const response = await fetch(`/api/classes/${classId}?teacherId=${user.id}`);
      const data = await response.json();

      if (data.success && data.class) {
        setClassData(data.class);
      }
    } catch (error) {
      console.error('Error fetching class:', error);
    }
  };

  const fetchStudents = async () => {
    try {
      const user = authStorage.getUser();
      if (!user) {
        toast.error('Please log in to view students');
        return;
      }

      const response = await fetch(`/api/class-enrollments?classId=${classId}`);
      const data = await response.json();

      if (data.success) {
        // Transform the data to match the expected Student interface
        const transformedStudents = data.data.map((enrollment: any) => ({
          id: enrollment.studentId,
          name: enrollment.studentName,
          email: enrollment.studentEmail,
          enrolledAt: enrollment.enrolledAt
        }));
        setStudents(transformedStudents || []);
      } else {
        toast.error(data.error || 'Failed to fetch students');
      }
    } catch (error) {
      console.error('Error fetching students:', error);
      toast.error('Failed to fetch students');
    } finally {
      setIsLoading(false);
    }
  };

  const fetchAvailableStudents = async () => {
    try {
      const user = authStorage.getUser();
      if (!user) return;

      // Fetch students from the same institution who are not in this class
      const apiUrl = `/api/users?role=student&institutionId=${user.institutionId}&excludeClassId=${classId}`;
      console.log('🔍 Fetching available students from:', apiUrl);
      console.log('👤 Current user:', { id: user.id, institutionId: user.institutionId });
      
      const response = await fetch(apiUrl);
      const data = await response.json();
      
      console.log('📥 API Response:', data);
      console.log('✅ Response success:', data.success);
      console.log('👥 Users in response:', data.users);

      if (data.success) {
        const studentsToSet = data.data?.users || [];
        console.log('🎯 Setting availableStudents to:', studentsToSet);
        setAvailableStudents(studentsToSet);
        console.log('📊 Available students count:', studentsToSet.length);
      } else {
        console.error('❌ API returned error:', data.error);
      }
    } catch (error) {
      console.error('💥 Error fetching available students:', error);
    }
  };

  const handleAddStudent = async () => {
    if (!selectedStudentId) {
      toast.error('Please select a student to add');
      return;
    }

    setIsAddingStudent(true);
    try {
      const user = authStorage.getUser();
      if (!user) {
        toast.error('Please log in to add students');
        return;
      }

      const response = await fetch('/api/class-enrollments', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          studentId: parseInt(selectedStudentId),
          classId: parseInt(classId),
          status: 'active'
        })
      });

      const data = await response.json();

      if (data.success) {
        toast.success('Student added to class successfully!');
        setIsDialogOpen(false);
        setSelectedStudentId('');
        fetchStudents();
        fetchAvailableStudents();
      } else {
        toast.error(data.error || 'Failed to add student to class');
      }
    } catch (error) {
      console.error('Error adding student:', error);
      toast.error('Failed to add student to class');
    } finally {
      setIsAddingStudent(false);
    }
  };

  const handleRemoveStudent = async (studentId: number) => {
    if (!confirm('Are you sure you want to remove this student from the class?')) {
      return;
    }

    setIsRemovingStudent(studentId);
    try {
      const user = authStorage.getUser();
      if (!user) {
        toast.error('Please log in to remove students');
        return;
      }

      // Find the enrollment ID for this student in this class
      const response = await fetch(`/api/class-enrollments?studentId=${studentId}&classId=${classId}`);
      const data = await response.json();

      if (data.success && data.data.length > 0) {
        const enrollmentId = data.data[0].id;
        
        const deleteResponse = await fetch(`/api/class-enrollments/${enrollmentId}`, {
          method: 'DELETE'
        });
        const deleteData = await deleteResponse.json();

        if (deleteData.success) {
          toast.success('Student removed from class successfully!');
          fetchStudents();
          fetchAvailableStudents();
        } else {
          toast.error(deleteData.error || 'Failed to remove student from class');
        }
      } else {
        toast.error('Enrollment not found');
      }
    } catch (error) {
      console.error('Error removing student:', error);
      toast.error('Failed to remove student from class');
    } finally {
      setIsRemovingStudent(null);
    }
  };

  const filteredStudents = students.filter(
    (student) =>
      student.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
      student.email.toLowerCase().includes(searchTerm.toLowerCase())
  );

  if (isLoading) {
    return (
      <div className='flex items-center justify-center min-h-screen'>
        <Loader2 className='h-8 w-8 animate-spin' />
        <span className='ml-2'>Loading students...</span>
      </div>
    );
  }

  return (
    <div className='space-y-6'>
      <div className='flex items-center space-x-4'>
        <Link href={`/dashboard/teacher/classes/${classId}`}>
          <Button variant='outline' size='sm'>
            <ArrowLeft className='mr-2 h-4 w-4' />
            Back
          </Button>
        </Link>
        <div>
          <h1 className='text-3xl font-bold tracking-tight'>
            Manage Students
          </h1>
          <p className='text-muted-foreground'>
            {classData ? `Students in ${classData.name}` : 'Loading class...'}
          </p>
        </div>
      </div>

      <Card>
        <CardHeader>
          <div className='flex items-center justify-between'>
            <div>
              <CardTitle>Class Students</CardTitle>
              <CardDescription>
                Manage students enrolled in this class
              </CardDescription>
            </div>
            <Dialog open={isDialogOpen} onOpenChange={setIsDialogOpen}>
              <DialogTrigger asChild>
                <Button>
                  <UserPlus className='mr-2 h-4 w-4' />
                  Add Student
                </Button>
              </DialogTrigger>
              <DialogContent>
                <DialogHeader>
                  <DialogTitle>Add Student to Class</DialogTitle>
                  <DialogDescription>
                    Select a student from your institution to add to this class.
                  </DialogDescription>
                </DialogHeader>
                <div className='space-y-4'>
                  <Select value={selectedStudentId} onValueChange={setSelectedStudentId}>
                    <SelectTrigger>
                      <SelectValue placeholder='Select a student' />
                    </SelectTrigger>
                    <SelectContent>
                      {(() => {
                        console.log('🎨 Rendering dropdown with availableStudents:', availableStudents);
                        console.log('📝 Available students length:', availableStudents.length);
                        return availableStudents.map((student) => {
                          console.log('👤 Rendering student:', student);
                          return (
                            <SelectItem key={student.id} value={student.id.toString()}>
                              {student.name} ({student.email})
                            </SelectItem>
                          );
                        });
                      })()}
                    </SelectContent>
                  </Select>
                  <div className='flex justify-end space-x-2'>
                    <Button
                      variant='outline'
                      onClick={() => {
                        setIsDialogOpen(false);
                        setSelectedStudentId('');
                      }}
                    >
                      Cancel
                    </Button>
                    <Button onClick={handleAddStudent} disabled={isAddingStudent}>
                      {isAddingStudent ? (
                        <Loader2 className='mr-2 h-4 w-4 animate-spin' />
                      ) : (
                        <UserPlus className='mr-2 h-4 w-4' />
                      )}
                      {isAddingStudent ? 'Adding...' : 'Add Student'}
                    </Button>
                  </div>
                </div>
              </DialogContent>
            </Dialog>
          </div>
        </CardHeader>
        <CardContent>
          <div className='mb-4 flex items-center space-x-2'>
            <div className='relative flex-1'>
              <Search className='text-muted-foreground absolute top-2.5 left-2 h-4 w-4' />
              <Input
                placeholder='Search students...'
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className='pl-8'
              />
            </div>
          </div>

          <div className='rounded-md border'>
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>Student Name</TableHead>
                  <TableHead>Email</TableHead>
                  <TableHead>Enrolled Date</TableHead>
                  <TableHead className='w-[70px]'>Actions</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {filteredStudents.map((student) => (
                  <TableRow key={student.id}>
                    <TableCell>
                      <div className='font-medium'>{student.name}</div>
                    </TableCell>
                    <TableCell>
                      <div className='text-muted-foreground'>{student.email}</div>
                    </TableCell>
                    <TableCell>
                      <span className='text-sm'>
                        {new Date(student.enrolledAt).toLocaleDateString()}
                      </span>
                    </TableCell>
                    <TableCell>
                      <Button
                        variant='ghost'
                        size='sm'
                        className='text-red-600 hover:text-red-700'
                        onClick={() => handleRemoveStudent(student.id)}
                        disabled={isRemovingStudent === student.id}
                      >
                        {isRemovingStudent === student.id ? (
                          <Loader2 className='h-4 w-4 animate-spin' />
                        ) : (
                          <Trash2 className='h-4 w-4' />
                        )}
                      </Button>
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          </div>

          {filteredStudents.length === 0 && (
            <div className='py-8 text-center'>
              <Users className='text-muted-foreground mx-auto h-12 w-12' />
              <h3 className='mt-2 text-sm font-semibold'>No students found</h3>
              <p className='text-muted-foreground mt-1 text-sm'>
                {searchTerm
                  ? 'Try adjusting your search terms.'
                  : 'No students are enrolled in this class yet.'}
              </p>
              {!searchTerm && (
                <div className='mt-6'>
                  <Button onClick={() => setIsDialogOpen(true)}>
                    <UserPlus className='mr-2 h-4 w-4' />
                    Add First Student
                  </Button>
                </div>
              )}
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  );
}