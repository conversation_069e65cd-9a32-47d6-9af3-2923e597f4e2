import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { useAuth } from '@/hooks/use-auth';

interface UserAvatarProfileProps {
  className?: string;
  showInfo?: boolean;
}

export function UserAvatarProfile({
  className,
  showInfo = false
}: UserAvatarProfileProps) {
  const { user } = useAuth();

  if (!user) {
    return (
      <Avatar className={className}>
        <AvatarFallback>G</AvatarFallback>
      </Avatar>
    );
  }

  return (
    <div className='flex items-center gap-2'>
      <Avatar className={className}>
        <AvatarImage
          src={`https://ui-avatars.com/api/?name=${user.name}&background=random`}
          alt={user.name}
        />
        <AvatarFallback className='rounded-lg'>
          {user.name?.slice(0, 2)?.toUpperCase() || 'U'}
        </AvatarFallback>
      </Avatar>

      {showInfo && (
        <div className='grid flex-1 text-left text-sm leading-tight'>
          <span className='truncate font-semibold'>{user.name}</span>
          <span className='truncate text-xs'>{user.email}</span>
        </div>
      )}
    </div>
  );
}
