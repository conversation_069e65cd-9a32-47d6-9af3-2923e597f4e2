(globalThis.TURBOPACK = globalThis.TURBOPACK || []).push([typeof document === "object" ? document.currentScript : undefined, {

"[project]/src/instrumentation-client.ts [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname, k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
// This file configures the initialization of Sentry on the client.
// The added config here will be used whenever a users loads a page in their browser.
// https://docs.sentry.io/platforms/javascript/guides/nextjs/
__turbopack_context__.s({
    "onRouterTransitionStart": (()=>onRouterTransitionStart)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$polyfills$2f$process$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/build/polyfills/process.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$sentry$2f$nextjs$2f$build$2f$esm$2f$client$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__ = __turbopack_context__.i("[project]/node_modules/@sentry/nextjs/build/esm/client/index.js [app-client] (ecmascript) <locals>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$sentry$2d$internal$2f$replay$2f$build$2f$npm$2f$esm$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@sentry-internal/replay/build/npm/esm/index.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$sentry$2f$nextjs$2f$build$2f$esm$2f$client$2f$routing$2f$appRouterRoutingInstrumentation$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@sentry/nextjs/build/esm/client/routing/appRouterRoutingInstrumentation.js [app-client] (ecmascript)");
globalThis["_sentryRouteManifest"] = "{\"dynamicRoutes\":[{\"path\":\"/my-courses/:courseId\",\"regex\":\"^/my-courses/([^/]+)$\",\"paramNames\":[\"courseId\"]},{\"path\":\"/my-courses/:courseId/detail\",\"regex\":\"^/my-courses/([^/]+)/detail$\",\"paramNames\":[\"courseId\"]},{\"path\":\"/my-courses/:courseId/exam\",\"regex\":\"^/my-courses/([^/]+)/exam$\",\"paramNames\":[\"courseId\"]},{\"path\":\"/my-courses/:courseId/exam/results\",\"regex\":\"^/my-courses/([^/]+)/exam/results$\",\"paramNames\":[\"courseId\"]},{\"path\":\"/my-courses/:courseId/learn\",\"regex\":\"^/my-courses/([^/]+)/learn$\",\"paramNames\":[\"courseId\"]},{\"path\":\"/auth/sign-in/:sign-in*?\",\"regex\":\"^/auth/sign-in(?:/(.*))?$\",\"paramNames\":[\"sign-in\"]},{\"path\":\"/auth/sign-up/:sign-up*?\",\"regex\":\"^/auth/sign-up(?:/(.*))?$\",\"paramNames\":[\"sign-up\"]},{\"path\":\"/dashboard/admin/institutions/:id\",\"regex\":\"^/dashboard/admin/institutions/([^/]+)$\",\"paramNames\":[\"id\"]},{\"path\":\"/dashboard/admin/users/:id\",\"regex\":\"^/dashboard/admin/users/([^/]+)$\",\"paramNames\":[\"id\"]},{\"path\":\"/dashboard/product/:productId\",\"regex\":\"^/dashboard/product/([^/]+)$\",\"paramNames\":[\"productId\"]},{\"path\":\"/dashboard/profile/:profile*?\",\"regex\":\"^/dashboard/profile(?:/(.*))?$\",\"paramNames\":[\"profile\"]},{\"path\":\"/dashboard/student/courses/:id\",\"regex\":\"^/dashboard/student/courses/([^/]+)$\",\"paramNames\":[\"id\"]},{\"path\":\"/dashboard/teacher/classes/:id\",\"regex\":\"^/dashboard/teacher/classes/([^/]+)$\",\"paramNames\":[\"id\"]},{\"path\":\"/dashboard/teacher/classes/:id/courses\",\"regex\":\"^/dashboard/teacher/classes/([^/]+)/courses$\",\"paramNames\":[\"id\"]},{\"path\":\"/dashboard/teacher/classes/:id/students\",\"regex\":\"^/dashboard/teacher/classes/([^/]+)/students$\",\"paramNames\":[\"id\"]},{\"path\":\"/dashboard/teacher/courses/:id\",\"regex\":\"^/dashboard/teacher/courses/([^/]+)$\",\"paramNames\":[\"id\"]}],\"staticRoutes\":[{\"path\":\"/\"},{\"path\":\"/courses\"},{\"path\":\"/my-courses\"},{\"path\":\"/dashboard\"},{\"path\":\"/dashboard/admin\"},{\"path\":\"/dashboard/admin/institutions\"},{\"path\":\"/dashboard/admin/institutions/new\"},{\"path\":\"/dashboard/admin/subscriptions\"},{\"path\":\"/dashboard/admin/users\"},{\"path\":\"/dashboard/admin/users/new\"},{\"path\":\"/dashboard/kanban\"},{\"path\":\"/dashboard/overview/@area_stats\"},{\"path\":\"/dashboard/overview/@bar_stats\"},{\"path\":\"/dashboard/overview/@pie_stats\"},{\"path\":\"/dashboard/overview/@sales\"},{\"path\":\"/dashboard/product\"},{\"path\":\"/dashboard/student\"},{\"path\":\"/dashboard/student/certificates\"},{\"path\":\"/dashboard/student/courses\"},{\"path\":\"/dashboard/student/progress\"},{\"path\":\"/dashboard/teacher\"},{\"path\":\"/dashboard/teacher/classes\"},{\"path\":\"/dashboard/teacher/classes/new\"},{\"path\":\"/dashboard/teacher/courses\"},{\"path\":\"/dashboard/teacher/courses/generate\"},{\"path\":\"/dashboard/teacher/courses/new\"},{\"path\":\"/dashboard/teacher/reports\"}]}";
;
if (!__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$polyfills$2f$process$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].env.NEXT_PUBLIC_SENTRY_DISABLED) {
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$sentry$2f$nextjs$2f$build$2f$esm$2f$client$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__["init"])({
        dsn: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$polyfills$2f$process$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].env.NEXT_PUBLIC_SENTRY_DSN,
        // Add optional integrations for additional features
        integrations: [
            (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$sentry$2d$internal$2f$replay$2f$build$2f$npm$2f$esm$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["replayIntegration"])()
        ],
        // Adds request headers and IP for users, for more info visit
        sendDefaultPii: true,
        // Define how likely traces are sampled. Adjust this value in production, or use tracesSampler for greater control.
        tracesSampleRate: 1,
        // Define how likely Replay events are sampled.
        // This sets the sample rate to be 10%. You may want this to be 100% while
        // in development and sample at a lower rate in production
        replaysSessionSampleRate: 0.1,
        // Define how likely Replay events are sampled when an error occurs.
        replaysOnErrorSampleRate: 1.0,
        // Setting this option to true will print useful information to the console while you're setting up Sentry.
        debug: false
    });
}
const onRouterTransitionStart = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$sentry$2f$nextjs$2f$build$2f$esm$2f$client$2f$routing$2f$appRouterRoutingInstrumentation$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["captureRouterTransitionStart"];
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
}]);

//# sourceMappingURL=src_instrumentation-client_ts_4aba9a11._.js.map