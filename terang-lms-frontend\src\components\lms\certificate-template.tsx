import React from 'react';
import { Building, Award } from 'lucide-react';
import Image from 'next/image'; // Import the Image component
import { CertificateTemplateProps } from '@/types/lms';

export const CertificateTemplate: React.FC<CertificateTemplateProps> = ({
  institution,
  course,
  studentName,
  completionDate
}) => {
  const primaryColor =
    institution.certificateTemplate?.primaryColor || '#1e40af';
  const secondaryColor =
    institution.certificateTemplate?.secondaryColor || '#f59e0b';

  return (
    <div
      className='space-y-6 rounded-lg border-4 p-8 text-center'
      style={{
        borderColor: secondaryColor,
        background: `linear-gradient(to right, ${primaryColor}10, ${secondaryColor}10)`
      }}
    >
      <div className='border-b-2 pb-4' style={{ borderColor: secondaryColor }}>
        <div className='mb-2 flex items-center justify-center space-x-3'>
          {institution.certificateTemplate?.logoUrl && (
            <Image
              src={institution.certificateTemplate.logoUrl}
              alt={institution.name}
              width={48} // Add the width property
              height={48} // Add the height property
              className='h-12 w-12'
            />
          )}
          <Building className='h-8 w-8' style={{ color: primaryColor }} />
        </div>
        <h2 className='text-3xl font-bold' style={{ color: primaryColor }}>
          {institution.name}
        </h2>
        <p style={{ color: primaryColor }}>Certificate of Completion</p>
      </div>

      <div className='space-y-4'>
        <p className='text-lg' style={{ color: `${primaryColor}cc` }}>
          This is to certify that
        </p>
        <h3 className='text-2xl font-bold' style={{ color: primaryColor }}>
          {studentName}
        </h3>
        <p className='text-lg' style={{ color: `${primaryColor}cc` }}>
          has successfully completed the course
        </p>
        <h4 className='text-xl font-semibold' style={{ color: primaryColor }}>
          {course.name}
        </h4>
        <p style={{ color: `${primaryColor}cc` }}>Course Code: {course.code}</p>
      </div>

      <div className='flex justify-center space-x-12 py-6'>
        <div className='text-center'>
          <div
            className='mb-2 h-1 w-32'
            style={{ backgroundColor: secondaryColor }}
          ></div>
          <p className='text-sm' style={{ color: `${primaryColor}cc` }}>
            Instructor
          </p>
          <p className='font-medium' style={{ color: primaryColor }}>
            {course.instructor}
          </p>
        </div>
        <div className='text-center'>
          <div
            className='mb-2 h-1 w-32'
            style={{ backgroundColor: secondaryColor }}
          ></div>
          <p className='text-sm' style={{ color: `${primaryColor}cc` }}>
            Date of Completion
          </p>
          <p className='font-medium' style={{ color: primaryColor }}>
            {completionDate}
          </p>
        </div>
      </div>

      <div className='border-t-2 pt-4' style={{ borderColor: secondaryColor }}>
        <Award
          className='mx-auto mb-2 h-12 w-12'
          style={{ color: secondaryColor }}
        />
        <p className='text-sm' style={{ color: `${primaryColor}99` }}>
          Certificate ID: {institution.shortName}-{course.code}-2024-001
        </p>
        {institution.certificateTemplate?.signatoryName && (
          <div className='mt-4'>
            <p className='font-medium' style={{ color: primaryColor }}>
              {institution.certificateTemplate.signatoryName}
            </p>
            <p className='text-sm' style={{ color: `${primaryColor}cc` }}>
              {institution.certificateTemplate.signatoryTitle}
            </p>
          </div>
        )}
      </div>
    </div>
  );
};