import { NavItem } from '@/types';

// Super Admin Navigation
export const superAdminNavItems: NavItem[] = [
  {
    title: 'Dashboard',
    url: '/dashboard/admin',
    icon: 'dashboard',
    isActive: false,
    shortcut: ['d', 'd'],
    items: []
  },
  {
    title: 'Institutions',
    url: '/dashboard/admin/institutions',
    icon: 'building',
    isActive: false,
    shortcut: ['i', 'i'],
    items: [
      {
        title: 'All Institutions',
        url: '/dashboard/admin/institutions',
        icon: 'building'
      },
      {
        title: 'Add Institution',
        url: '/dashboard/admin/institutions/new',
        icon: 'plus'
      }
    ]
  },
  {
    title: 'Users',
    url: '/dashboard/admin/users',
    icon: 'users',
    isActive: false,
    shortcut: ['u', 'u'],
    items: [
      {
        title: 'All Users',
        url: '/dashboard/admin/users',
        icon: 'users'
      },
      {
        title: 'Add User',
        url: '/dashboard/admin/users/new',
        icon: 'userPlus'
      }
    ]
  },
  {
    title: 'Subscriptions',
    url: '/dashboard/admin/subscriptions',
    icon: 'creditCard',
    isActive: false,
    shortcut: ['s', 's'],
    items: []
  },
  {
    title: 'Analytics',
    url: '/dashboard/admin/analytics',
    icon: 'barChart',
    isActive: false,
    shortcut: ['a', 'a'],
    items: []
  },
  {
    title: 'Account',
    url: '#',
    icon: 'billing',
    isActive: false,
    items: [
      {
        title: 'Profile',
        url: '/dashboard/profile',
        icon: 'userPen',
        shortcut: ['p', 'p']
      },
      {
        title: 'Settings',
        url: '/dashboard/settings',
        icon: 'settings'
      }
    ]
  }
];

// Teacher Navigation
export const teacherNavItems: NavItem[] = [
  {
    title: 'Dashboard',
    url: '/dashboard/teacher',
    icon: 'dashboard',
    isActive: false,
    shortcut: ['d', 'd'],
    items: []
  },
  /*
  {
    title: 'Classes',
    url: '/dashboard/teacher/classes',
    icon: 'users',
    isActive: false,
    shortcut: ['c', 'c'],
    items: [
      {
        title: 'My Classes',
        url: '/dashboard/teacher/classes',
        icon: 'users'
      },
      {
        title: 'Create Class',
        url: '/dashboard/teacher/classes/new',
        icon: 'plus'
      }
    ]
  },
  */
  {
    title: 'Courses',
    url: '/dashboard/teacher/courses',
    icon: 'bookOpen',
    isActive: false,
    shortcut: ['o', 'o'],
    items: [
      {
        title: 'My Courses',
        url: '/dashboard/teacher/courses',
        icon: 'bookOpen'
      },
      {
        title: 'Create Course',
        url: '/dashboard/teacher/courses/new',
        icon: 'plus'
      }
    ]
  },

  /*
  {
    title: 'Quizzes',
    url: '/dashboard/teacher/quizzes',
    icon: 'post',
    isActive: false,
    shortcut: ['q', 'q'],
    items: [
      {
        title: 'All Quizzes',
        url: '/dashboard/teacher/quizzes',
        icon: 'post'
      },
      {
        title: 'Create Quiz',
        url: '/dashboard/teacher/quizzes/new',
        icon: 'plus'
      }
    ]
  },
  */
  /*
  {
    title: 'Reports',
    url: '/dashboard/teacher/reports',
    icon: 'barChart',
    isActive: false,
    shortcut: ['r', 'r'],
    items: [
      {
        title: 'Student Progress',
        url: '/dashboard/teacher/reports/progress',
        icon: 'trendingUp'
      },
      {
        title: 'Quiz Results',
        url: '/dashboard/teacher/reports/quizzes',
        icon: 'post'
      },
      {
        title: 'Certificates',
        url: '/dashboard/teacher/reports/certificates',
        icon: 'award'
      }
    ]
  },
  {
    title: 'Account',
    url: '#',
    icon: 'billing',
    isActive: false,
    items: [
      {
        title: 'Profile',
        url: '/dashboard/profile',
        icon: 'userPen',
        shortcut: ['p', 'p']
      },
      {
        title: 'Settings',
        url: '/dashboard/settings',
        icon: 'settings'
      }
    ]
  }
  */
];

// Student Navigation
export const studentNavItems: NavItem[] = [
  {
    title: 'Dashboard',
    url: '/dashboard/student',
    icon: 'dashboard',
    isActive: false,
    shortcut: ['d', 'd'],
    items: []
  },
  {
    title: 'My Courses',
    url: '/dashboard/student/courses',
    icon: 'bookOpen',
    isActive: false,
    shortcut: ['c', 'c'],
    items: []
  },
  {
    title: 'Progress',
    url: '/dashboard/student/progress',
    icon: 'trendingUp',
    isActive: false,
    shortcut: ['p', 'p'],
    items: []
  },
  {
    title: 'Certificates',
    url: '/dashboard/student/certificates',
    icon: 'award',
    isActive: false,
    shortcut: ['e', 'e'],
    items: []
  },
  {
    title: 'Account',
    url: '#',
    icon: 'billing',
    isActive: false,
    items: [
      {
        title: 'Profile',
        url: '/dashboard/profile',
        icon: 'userPen',
        shortcut: ['r', 'r']
      },
      {
        title: 'Settings',
        url: '/dashboard/settings',
        icon: 'settings'
      }
    ]
  }
];

// Function to get navigation items based on user role
export const getNavigationItems = (role: string): NavItem[] => {
  switch (role) {
    case 'super_admin':
      return superAdminNavItems;
    case 'teacher':
      return teacherNavItems;
    case 'student':
      return studentNavItems;
    default:
      return [];
  }
};
