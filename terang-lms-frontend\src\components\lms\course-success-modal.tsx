'use client';

import React from 'react';
import { <PERSON><PERSON>, <PERSON>alog<PERSON>ontent, <PERSON>alogHeader, DialogTitle } from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Card, CardContent } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import {
  CheckmarkCircle01Icon as CheckCircleIcon,
  BookOpen01Icon as BookOpenIcon,
  PlayIcon,
  ArrowRight01Icon as ArrowRightIcon,
  UserIcon as UsersIcon,
  Calendar03Icon as CalendarIcon,
  Award05Icon as AwardIcon
} from 'hugeicons-react';
import Image from 'next/image'; // Import the Image component
import { Course } from '@/types/lms';
import Link from 'next/link';

interface CourseSuccessModalProps {
  course: Course;
  isOpen: boolean;
  onClose: () => void;
  actionType: 'purchase' | 'enrollment';
}

const CourseSuccessModal: React.FC<CourseSuccessModalProps> = ({
  course,
  isOpen,
  onClose,
  actionType
}) => {
  const formatPrice = (price: number, currency: string = 'IDR') => {
    if (currency === 'IDR') {
      return 'Rp' + new Intl.NumberFormat('id-ID').format(price);
    }
    return new Intl.NumberFormat('id-ID', {
      style: 'currency',
      currency: currency
    }).format(price);
  };

  const getSuccessMessage = () => {
    if (actionType === 'purchase') {
      return 'Pembelian Berhasil!';
    }
    return 'Pendaftaran Berhasil!';
  };

  const getSuccessDescription = () => {
    if (actionType === 'purchase') {
      return 'Selamat! Anda telah berhasil membeli dan terdaftar di kursus ini. Anda sekarang dapat mengakses semua materi pembelajaran.';
    }
    return 'Selamat! Anda telah berhasil terdaftar di kursus ini. Anda sekarang dapat mengakses semua materi pembelajaran.';
  };

  return (
    <Dialog open={isOpen} onOpenChange={() => {}}>
      <DialogContent className="sm:max-w-2xl w-[95vw] max-h-[85vh] flex flex-col p-0 gap-0">
        {/* Fixed Header */}
        <div className="flex-shrink-0 p-6 pb-4">
          <DialogHeader className="text-center">
            <div className="mx-auto w-16 h-16 bg-green-100 rounded-full flex items-center justify-center mb-4">
              <CheckCircleIcon className="h-8 w-8 text-green-600" />
            </div>
            <DialogTitle className="text-2xl font-bold text-green-900 text-center">
              {getSuccessMessage()}
            </DialogTitle>
            <p className="text-gray-600 mt-2">
              {getSuccessDescription()}
            </p>
          </DialogHeader>
        </div>

        {/* Scrollable Content Area */}
        <div className="flex-1 overflow-y-auto px-6 min-h-0">
          {/* Course Card */}
          <Card className="border-2 border-green-200 bg-green-50/30">
            <CardContent className="p-0">
              {/* Course Image/Thumbnail */}
              <div className="h-32 rounded-t-lg flex items-center justify-center relative overflow-hidden">
                {course.thumbnail ? (
                  <Image 
                    src={course.thumbnail} 
                    alt={course.name}
                    fill // Use fill to make the image cover the parent div
                    className="object-cover"
                    sizes="(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 33vw" // Recommended for performance
                  />
                ) : (
                  <div className="h-32 bg-gradient-to-br from-blue-500 to-purple-600 w-full flex items-center justify-center">
                    <div className="absolute inset-0 bg-black/20" />
                    <div className="relative z-10 text-center text-white">
                      <BookOpenIcon className="h-12 w-12 mx-auto mb-2 opacity-80" />
                      <p className="text-sm font-medium">{course.code}</p>
                    </div>
                  </div>
                )}
                {actionType === 'purchase' && course.price && (
                  <Badge 
                    className="absolute top-3 right-3 bg-green-600 hover:bg-green-600"
                    variant="default"
                  >
                    {formatPrice(course.price, course.currency)}
                  </Badge>
                )}
              </div>

              <div className="p-6">
                {/* Course Header */}
                <div className="space-y-2 mb-4">
                  <h3 className="text-lg font-bold text-gray-900 line-clamp-2">
                    {course.name}
                  </h3>
                  <p className="text-gray-600 text-sm line-clamp-2">
                    {course.description}
                  </p>
                </div>

                {/* Course Meta Information */}
                <div className="grid grid-cols-2 gap-4 mb-4 text-sm text-gray-600">
                  <div className="flex items-center">
                    <UsersIcon className="mr-2 h-4 w-4" />
                    <span>Instruktur: {course.instructor}</span>
                  </div>
                  <div className="flex items-center">
                    <CalendarIcon className="mr-2 h-4 w-4" />
                    <span>
                      {new Date(course.startDate).toLocaleDateString('id-ID')} - {' '}
                      {new Date(course.endDate).toLocaleDateString('id-ID')}
                    </span>
                  </div>
                  <div className="flex items-center">
                    <BookOpenIcon className="mr-2 h-4 w-4" />
                    <span>{course.modules.length} modul</span>
                  </div>
                  {course.certificate.isEligible && (
                    <div className="flex items-center">
                      <AwardIcon className="mr-2 h-4 w-4" />
                      <span>Sertifikat tersedia</span>
                    </div>
                  )}
                </div>

                {/* Course Features */}
                <div className="flex flex-wrap gap-2 mb-4">
                  {course.certificate.isEligible && (
                    <Badge variant="secondary">
                      <AwardIcon className="mr-1 h-3 w-3" />
                      Sertifikat
                    </Badge>
                  )}
                  {course.academics?.workload && (
                    <Badge variant="outline">{course.academics.workload}</Badge>
                  )}
                  {course.academics?.credits && (
                    <Badge variant="outline">{course.academics.credits} kredit</Badge>
                  )}
                </div>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Fixed Footer with Action Buttons */}
        <div className="flex-shrink-0 border-t bg-white p-6 space-y-4">
          {/* Action Buttons */}
          <div className="flex flex-col sm:flex-row gap-3">
            <Link href={`/my-courses/${course.id}`} className="flex-1">
              <Button className="w-full bg-green-600 hover:bg-green-700" size="lg">
                <PlayIcon className="mr-2 h-4 w-4" />
                Mulai Belajar Sekarang
                <ArrowRightIcon className="ml-2 h-4 w-4" />
              </Button>
            </Link>
            <Link href="/my-courses" className="flex-1">
              <Button variant="outline" className="w-full" size="lg">
                <BookOpenIcon className="mr-2 h-4 w-4" />
                Lihat Semua Kursus Saya
              </Button>
            </Link>
          </div>

          {/* Close Option */}
          <div className="text-center">
            <Button 
              variant="ghost" 
              onClick={onClose}
              className="text-gray-500 hover:text-gray-700"
            >
              Tutup dan kembali ke katalog
            </Button>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
};

export default CourseSuccessModal;