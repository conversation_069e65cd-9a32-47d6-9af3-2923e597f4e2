import React, { useState } from 'react';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { DollarSign, CreditCard, Gift, X } from 'lucide-react';
import { CourseData, TuitionAndFinancingData } from '../course-creation-wizard';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';

interface TuitionFinancingStepProps {
  data: CourseData;
  onUpdate: (updates: Partial<CourseData>) => void;
}

export function TuitionFinancingStep({ data, onUpdate }: TuitionFinancingStepProps) {
  const tuitionAndFinancing = data.tuitionAndFinancing || { totalCost: 0, paymentOptions: [], scholarships: [] };
  const [newPaymentOption, setNewPaymentOption] = useState('');
  const [newScholarship, setNewScholarship] = useState('');

  const handleUpdate = (field: keyof TuitionAndFinancingData, value: string | number | string[]) => {
    onUpdate({
      tuitionAndFinancing: {
        ...tuitionAndFinancing,
        [field]: value,
      },
    });
  };

  const addPaymentOption = () => {
    if (newPaymentOption.trim() !== '' && !tuitionAndFinancing.paymentOptions.includes(newPaymentOption.trim())) {
      handleUpdate('paymentOptions', [...tuitionAndFinancing.paymentOptions, newPaymentOption.trim()]);
      setNewPaymentOption('');
    }
  };

  const removePaymentOption = (index: number) => {
    const updatedOptions = tuitionAndFinancing.paymentOptions.filter((_, i) => i !== index);
    handleUpdate('paymentOptions', updatedOptions);
  };

  const addScholarship = () => {
    if (newScholarship.trim() !== '' && !tuitionAndFinancing.scholarships.includes(newScholarship.trim())) {
      handleUpdate('scholarships', [...tuitionAndFinancing.scholarships, newScholarship.trim()]);
      setNewScholarship('');
    }
  };

  const removeScholarship = (index: number) => {
    const updatedScholarships = tuitionAndFinancing.scholarships.filter((_, i) => i !== index);
    handleUpdate('scholarships', updatedScholarships);
  };

  return (
    <Card className="w-full">
      <CardHeader>
        <CardTitle>Biaya & Pembiayaan</CardTitle>
        <CardDescription>Detail terkait biaya kursus, opsi pembayaran, dan peluang beasiswa.</CardDescription>
      </CardHeader>
      <CardContent className="space-y-6">
        <div className="flex items-center space-x-2">
          <DollarSign className="h-5 w-5 text-gray-500" />
          <Label htmlFor="totalCost">Total Biaya</Label>
        </div>
        <Input
          id="totalCost"
          type="number"
          value={tuitionAndFinancing.totalCost}
          onChange={(e) => handleUpdate('totalCost', parseFloat(e.target.value))}
          placeholder="Contoh: 6000000"
        />

        <div className="space-y-2">
          <div className="flex items-center space-x-2">
            <CreditCard className="h-5 w-5 text-gray-500" />
            <Label htmlFor="newPaymentOption">Opsi Pembayaran</Label>
          </div>
          <div className="flex space-x-2">
            <Input
              id="newPaymentOption"
              value={newPaymentOption}
              onChange={(e) => setNewPaymentOption(e.target.value)}
              placeholder="Tambahkan opsi pembayaran baru"
              onKeyPress={(e) => {
                if (e.key === 'Enter') {
                  e.preventDefault();
                  addPaymentOption();
                }
              }}
            />
            <Button type="button" onClick={addPaymentOption}>Tambah</Button>
          </div>
          <div className="flex flex-wrap gap-2 mt-2">
            {tuitionAndFinancing.paymentOptions.map((option, index) => (
              <Badge key={index} variant="secondary" className="pr-1">
                {option}
                <Button
                  type="button"
                  variant="ghost"
                  size="sm"
                  className="ml-1 h-auto px-1 py-0.5"
                  onClick={() => removePaymentOption(index)}
                >
                  <X className="h-3 w-3" />
                </Button>
              </Badge>
            ))}
          </div>
        </div>

        <div className="space-y-2">
          <div className="flex items-center space-x-2">
            <Gift className="h-5 w-5 text-gray-500" />
            <Label htmlFor="newScholarship">Beasiswa</Label>
          </div>
          <div className="flex space-x-2">
            <Input
              id="newScholarship"
              value={newScholarship}
              onChange={(e) => setNewScholarship(e.target.value)}
              placeholder="Tambahkan beasiswa baru"
              onKeyPress={(e) => {
                if (e.key === 'Enter') {
                  e.preventDefault();
                  addScholarship();
                }
              }}
            />
            <Button type="button" onClick={addScholarship}>Tambah</Button>
          </div>
          <div className="flex flex-wrap gap-2 mt-2">
            {tuitionAndFinancing.scholarships.map((scholarship, index) => (
              <Badge key={index} variant="secondary" className="pr-1">
                {scholarship}
                <Button
                  type="button"
                  variant="ghost"
                  size="sm"
                  className="ml-1 h-auto px-1 py-0.5"
                  onClick={() => removeScholarship(index)}
                >
                  <X className="h-3 w-3" />
                </Button>
              </Badge>
            ))}
          </div>
        </div>
      </CardContent>
    </Card>
  );
}