import React, { useState } from 'react';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Plus, X, MessageSquare, HardHat, LifeBuoy } from 'lucide-react';
import { CourseData, StudentExperienceData } from '../course-creation-wizard';
import { Badge } from '@/components/ui/badge';

interface StudentExperienceStepProps {
  data: CourseData;
  onUpdate: (updates: Partial<CourseData>) => void;
}

export function StudentExperienceStep({ data, onUpdate }: StudentExperienceStepProps) {
  const studentExperience = data.studentExperience || { testimonials: [], facilities: [], support: [] };
  const [newFacility, setNewFacility] = useState('');
  const [newSupport, setNewSupport] = useState('');

  const handleUpdate = (field: keyof StudentExperienceData, value: string | string[] | { name: string; feedback: string }[]) => {
    onUpdate({
      studentExperience: {
        ...studentExperience,
        [field]: value,
      },
    });
  };

  const addTestimonial = () => {
    handleUpdate('testimonials', [...studentExperience.testimonials, { name: '', feedback: '' }]);
  };

  const updateTestimonial = (index: number, field: 'name' | 'feedback', value: string) => {
    const updatedTestimonials = [...studentExperience.testimonials];
    updatedTestimonials[index] = { ...updatedTestimonials[index], [field]: value };
    handleUpdate('testimonials', updatedTestimonials);
  };

  const removeTestimonial = (index: number) => {
    const updatedTestimonials = studentExperience.testimonials.filter((_, i) => i !== index);
    handleUpdate('testimonials', updatedTestimonials);
  };

  const addFacility = () => {
    if (newFacility.trim() !== '' && !studentExperience.facilities.includes(newFacility.trim())) {
      handleUpdate('facilities', [...studentExperience.facilities, newFacility.trim()]);
      setNewFacility('');
    }
  };

  const removeFacility = (index: number) => {
    const updatedFacilities = studentExperience.facilities.filter((_, i) => i !== index);
    handleUpdate('facilities', updatedFacilities);
  };

  const addSupport = () => {
    if (newSupport.trim() !== '' && !studentExperience.support.includes(newSupport.trim())) {
      handleUpdate('support', [...studentExperience.support, newSupport.trim()]);
      setNewSupport('');
    }
  };

  const removeSupport = (index: number) => {
    const updatedSupport = studentExperience.support.filter((_, i) => i !== index);
    handleUpdate('support', updatedSupport);
  };

  return (
    <Card className="w-full">
      <CardHeader>
        <CardTitle>Pengalaman Mahasiswa</CardTitle>
        <CardDescription>Detail terkait pengalaman, fasilitas, dan dukungan yang akan didapat mahasiswa.</CardDescription>
      </CardHeader>
      <CardContent className="space-y-6">
        <div>
          <div className="flex items-center space-x-2 mb-2">
            <MessageSquare className="h-5 w-5 text-gray-500" />
            <Label>Testimoni</Label>
          </div>
          {studentExperience.testimonials.map((testimonial, index) => (
            <div key={index} className="flex items-end space-x-2 mb-4">
              <div className="flex-grow space-y-2">
                <Input
                  placeholder="Nama"
                  value={testimonial.name}
                  onChange={(e) => updateTestimonial(index, 'name', e.target.value)}
                />
                <Textarea
                  placeholder="Umpan Balik"
                  value={testimonial.feedback}
                  onChange={(e) => updateTestimonial(index, 'feedback', e.target.value)}
                />
              </div>
              <Button variant="destructive" size="icon" onClick={() => removeTestimonial(index)}>
                <X className="h-4 w-4" />
              </Button>
            </div>
          ))}
          <Button variant="outline" onClick={addTestimonial}>
            <Plus className="h-4 w-4 mr-2" /> Tambah Testimoni
          </Button>
        </div>

        <div className="space-y-2">
          <div className="flex items-center space-x-2">
            <HardHat className="h-5 w-5 text-gray-500" />
            <Label htmlFor="newFacility">Fasilitas</Label>
          </div>
          <div className="flex space-x-2">
            <Input
              id="newFacility"
              value={newFacility}
              onChange={(e) => setNewFacility(e.target.value)}
              placeholder="Tambahkan fasilitas baru"
              onKeyPress={(e) => {
                if (e.key === 'Enter') {
                  e.preventDefault();
                  addFacility();
                }
              }}
            />
            <Button type="button" onClick={addFacility}>Tambah</Button>
          </div>
          <div className="flex flex-wrap gap-2 mt-2">
            {studentExperience.facilities.map((facility, index) => (
              <Badge key={index} variant="secondary" className="pr-1">
                {facility}
                <Button
                  type="button"
                  variant="ghost"
                  size="sm"
                  className="ml-1 h-auto px-1 py-0.5"
                  onClick={() => removeFacility(index)}
                >
                  <X className="h-3 w-3" />
                </Button>
              </Badge>
            ))}
          </div>
        </div>

        <div className="space-y-2">
          <div className="flex items-center space-x-2">
            <LifeBuoy className="h-5 w-5 text-gray-500" />
            <Label htmlFor="newSupport">Dukungan</Label>
          </div>
          <div className="flex space-x-2">
            <Input
              id="newSupport"
              value={newSupport}
              onChange={(e) => setNewSupport(e.target.value)}
              placeholder="Tambahkan dukungan baru"
              onKeyPress={(e) => {
                if (e.key === 'Enter') {
                  e.preventDefault();
                  addSupport();
                }
              }}
            />
            <Button type="button" onClick={addSupport}>Tambah</Button>
          </div>
          <div className="flex flex-wrap gap-2 mt-2">
            {studentExperience.support.map((supportItem, index) => (
              <Badge key={index} variant="secondary" className="pr-1">
                {supportItem}
                <Button
                  type="button"
                  variant="ghost"
                  size="sm"
                  className="ml-1 h-auto px-1 py-0.5"
                  onClick={() => removeSupport(index)}
                >
                  <X className="h-3 w-3" />
                </Button>
              </Badge>
            ))}
          </div>
        </div>
      </CardContent>
    </Card>
  );
}