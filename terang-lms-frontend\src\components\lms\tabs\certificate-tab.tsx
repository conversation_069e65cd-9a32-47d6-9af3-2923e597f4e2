import React, { useState } from 'react';
import { <PERSON>, Card<PERSON><PERSON>nt, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Award, CheckCircle, XCircle, Lock, Download, Loader2 } from 'lucide-react';
import { Course, Institution } from '@/types/lms';
import { CertificateData, downloadCertificateAsPDF, generateCertificateId } from '@/lib/certificate';
import { authStorage } from '@/lib/auth';

interface CertificateTabProps {
  courseData: Course;
  institution: Institution;
  overallProgress: number;
  onGenerateCertificate: () => void;
  onDownloadPDF?: () => void;
}

export const CertificateTab: React.FC<CertificateTabProps> = ({
  courseData,
  institution,
  overallProgress,
  onGenerateCertificate
}) => {
  const [isDownloading, setIsDownloading] = useState(false);

  const handleDownloadPDF = async () => {
    try {
      setIsDownloading(true);

      // Get user from local storage
      const user = authStorage.getUser();

      // Create certificate data
      const certificateData: CertificateData = {
        studentName: user?.name || '<PERSON>', // Use user's name from context
        courseName: courseData.name,
        courseCode: courseData.code,
        completionDate: courseData.certificate.completionDate || new Date().toISOString().split('T')[0],
        finalScore: courseData.finalExam.lastScore || 0,
        instructorName: courseData.instructor,
        institutionName: institution.name,
        certificateId: generateCertificateId()
      };

      // Use the new PDF download function
      await downloadCertificateAsPDF(certificateData);
    } catch (error) {
      console.error('Error downloading PDF:', error);
      // You could add a toast notification here if you have one
    } finally {
      setIsDownloading(false);
    }
  };

  return (
    <Card>
      <CardHeader>
        <CardTitle
          className='flex items-center space-x-2'
          style={{ color: institution.certificateTemplate?.primaryColor }}
        >
          <Award className='h-6 w-6' />
          <span>Sertifikasi Profesional</span>
        </CardTitle>
      </CardHeader>
      <CardContent className='p-6'>
        {courseData.finalExam.attempts === 0 ? (
          /* User hasn't taken final exam yet */
          <div className='space-y-6 text-center'>
            <div className='rounded-lg border-2 border-yellow-200 bg-gradient-to-r from-yellow-50 to-orange-50 p-8'>
              <Lock className='mx-auto mb-4 h-16 w-16 text-yellow-600' />
              <h3 className='mb-2 text-2xl font-bold text-yellow-800'>
                Belum Mengikuti Final Exam
              </h3>
              <p className='text-yellow-700'>
                Kamu belum mengikuti final exam. Selesaikan final exam terlebih dahulu untuk mendapatkan sertifikat.
              </p>
            </div>
          </div>
        ) : courseData.certificate.isEligible &&
        courseData.certificate.isGenerated ? (
          /* Certificate Generated */
          <div className='space-y-6 text-center'>
            <div className='rounded-lg border-2 border-green-200 bg-gradient-to-r from-green-50 to-blue-50 p-8'>
              <Award className='mx-auto mb-4 h-16 w-16 text-green-600' />
              <h3 className='mb-2 text-2xl font-bold text-green-800'>
                Selamat!
              </h3>
              <p className='text-green-700'>
                Anda telah berhasil menyelesaikan kursus {courseData.name} dan
                memperoleh sertifikasi.
              </p>
              {courseData.certificate.completionDate && (
                <p className='mt-2 text-sm text-green-600'>
                  Diselesaikan pada:{' '}
                  {new Date(
                    courseData.certificate.completionDate
                  ).toLocaleDateString()}
                </p>
              )}
            </div>
            <div className='flex justify-center'>
              <Button
                variant='outline'
                className='border-green-600 text-green-600 hover:bg-green-50'
                onClick={handleDownloadPDF}
                disabled={isDownloading}
              >
                {isDownloading ? (
                  <Loader2 className='mr-2 h-4 w-4 animate-spin' />
                ) : (
                  <Download className='mr-2 h-4 w-4' />
                )}
                {isDownloading ? 'Mengunduh...' : 'Unduh PDF'}
              </Button>
            </div>
          </div>
        ) : courseData.certificate.isEligible ? (
          /* Certificate Ready - Show View and Download */
          <div className='space-y-6 text-center'>
            <div className='rounded-lg border-2 border-green-200 bg-gradient-to-r from-green-50 to-blue-50 p-8'>
              <Award className='mx-auto mb-4 h-16 w-16 text-green-600' />
              <h3 className='mb-2 text-2xl font-bold text-green-800'>
                Selamat!
              </h3>
              <p className='text-green-700'>
                Anda telah berhasil menyelesaikan kursus {courseData.name} dan
                memperoleh sertifikasi.
              </p>
              {courseData.certificate.completionDate && (
                <p className='mt-2 text-sm text-green-600'>
                  Diselesaikan pada:{' '}
                  {new Date(
                    courseData.certificate.completionDate
                  ).toLocaleDateString()}
                </p>
              )}
            </div>
            <div className='flex justify-center'>
              <Button
                variant='outline'
                className='border-green-600 text-green-600 hover:bg-green-50'
                onClick={handleDownloadPDF}
                disabled={isDownloading}
              >
                {isDownloading ? (
                  <Loader2 className='mr-2 h-4 w-4 animate-spin' />
                ) : (
                  <Download className='mr-2 h-4 w-4' />
                )}
                {isDownloading ? 'Mengunduh...' : 'Unduh PDF'}
              </Button>
            </div>
          </div>
        ) : (
          /* Not Eligible Yet */
          <div className='space-y-6'>
            <div className='rounded-lg border-2 border-gray-200 bg-gray-50 p-8 text-center'>
              <Lock className='mx-auto mb-4 h-16 w-16 text-gray-400' />
              <h3 className='mb-2 text-2xl font-bold text-gray-700'>
                Persyaratan Sertifikat
              </h3>
              <p className='mb-4 text-gray-600'>
                Selesaikan semua persyaratan kursus untuk memperoleh sertifikasi
                profesional Anda.
              </p>
            </div>

            <div className='grid grid-cols-1 gap-4 md:grid-cols-2'>
              <div className='space-y-3'>
                <h4 className='font-medium text-gray-900'>Penyelesaian Modul</h4>
                {courseData.modules.map((module) => (
                  <div
                    key={module.id}
                    className='flex items-center justify-between rounded-lg bg-gray-50 p-3'
                  >
                    <span className='text-sm'>{module.title}</span>
                    {module.moduleQuiz.isPassed ? (
                      <CheckCircle className='h-5 w-5 text-green-600' />
                    ) : (
                      <XCircle className='h-5 w-5 text-gray-400' />
                    )}
                  </div>
                ))}
              </div>
              <div className='space-y-3'>
                <h4 className='font-medium text-gray-900'>
                  Persyaratan Akhir
                </h4>
                <div className='flex items-center justify-between rounded-lg bg-gray-50 p-3'>
                  <span className='text-sm'>
                    Final Exam (Min. {courseData.finalExam.minimumScore}%)
                  </span>
                  {courseData.finalExam.isPassed ? (
                    <CheckCircle className='h-5 w-5 text-green-600' />
                  ) : (
                    <XCircle className='h-5 w-5 text-gray-400' />
                  )}
                </div>
                <div className='flex items-center justify-between rounded-lg bg-gray-50 p-3'>
                  <span className='text-sm'>Skor Keseluruhan (Min. 70%)</span>
                  {overallProgress >= 70 ? (
                    <CheckCircle className='h-5 w-5 text-green-600' />
                  ) : (
                    <XCircle className='h-5 w-5 text-gray-400' />
                  )}
                </div>
              </div>
            </div>
          </div>
        )}
      </CardContent>
    </Card>
  );
};
