import puppeteer from 'puppeteer';

const browser = await puppeteer.launch({ headless: false });
const page = await browser.newPage();

await page.goto('https://terang.ai', { waitUntil: 'networkidle2' });
await page.setViewport({width: 1080, height: 1024});

console.log('Loading terang.ai...');

const title = await page.title();
console.log(`Page title: ${title}`);

await page.screenshot({ path: 'terang-ai.png' });
console.log('Screenshot saved: terang-ai.png');

await browser.close();