import { generateC<PERSON>pterContent } from './content-generator';
import { generateModuleQuiz, generateFinalExam } from './quiz-generator';
import { 
  AICourseOutline, 
  AIGeneratedContent, 
  AIGeneratedQuiz, 
  GenerationProgress, 
  GenerationStep, 
  AIGenerationError,
  BatchGenerationResult
} from './types';

/**
 * Sequential batch generator for course content
 */
export class BatchContentGenerator {
  private steps: GenerationStep[] = [];
  private currentStepIndex = 0;
  private progress: GenerationProgress;
  private onProgressUpdate?: (progress: GenerationProgress) => void;
  private onStepComplete?: (step: GenerationStep, result: any) => void;
  private sessionId: string;

  constructor(
    private courseOutline: AICourseOutline,
    onProgressUpdate?: (progress: GenerationProgress) => void,
    onStepComplete?: (step: GenerationStep, result: any) => void,
    sessionId?: string,
  ) {
    this.onProgressUpdate = onProgressUpdate;
    this.onStepComplete = onStepComplete;
    this.sessionId = sessionId || `course-generation-${Date.now()}`;
    this.initializeSteps();
    this.progress = {
      currentStep: this.steps[0]?.name || 'Initializing',
      totalSteps: this.steps.length,
      completedSteps: 0,
      isGenerating: false
    };
  }

  /**
   * Initialize generation steps based on course outline
   */
  private initializeSteps(): void {
    this.steps = [];
    let stepId = 1;

    // Add steps for each module and its chapters
    this.courseOutline.modules.forEach((module, moduleIndex) => {
      module.chapters.forEach((chapter, chapterIndex) => {
        this.steps.push({
          id: `step-${stepId++}`,
          name: `${module.name} - ${chapter.name}`,
          type: 'chapter',
          status: 'pending',
          moduleIndex,
          chapterIndex
        });
      });

      // Add module quiz step if needed
      if (module.hasModuleQuiz) {
        this.steps.push({
          id: `step-${stepId++}`,
          name: `${module.name} - Module Quiz`,
          type: 'quiz',
          status: 'pending',
          moduleIndex
        });
      }
    });

    // Add final exam step if needed
    if (this.courseOutline.hasFinalExam) {
      this.steps.push({
        id: `step-${stepId++}`,
        name: 'Final Exam',
        type: 'final_exam',
        status: 'pending'
      });
    }
  }

  /**
   * Start the sequential generation process
   * @param onChunk Optional callback for streaming chunks (only for chapter generation)
   * @returns Promise<BatchGenerationResults> Complete generation results
   */
  async generateAll(onChunk?: (chunk: string) => void): Promise<BatchGenerationResults> {
    const results: BatchGenerationResults = {
      chapters: new Map(),
      moduleQuizzes: new Map(),
      finalExam: null,
      errors: []
    };

    this.progress.isGenerating = true;
    this.updateProgress();

    try {
      for (let i = 0; i < this.steps.length; i++) {
        this.currentStepIndex = i;
        const step = this.steps[i];
        
        this.progress.currentStep = step.name;
        step.status = 'generating';
        this.updateProgress();

        try {
          const result = await this.generateStep(step, onChunk);
          
          // Store result based on step type
          this.storeResult(step, result, results);
          
          step.status = 'completed';
          this.progress.completedSteps++;
          
          if (this.onStepComplete) {
            this.onStepComplete(step, result);
          }
        } catch (error) {
          step.status = 'error';
          const errorInfo = {
            step: step.name,
            error: error instanceof Error ? error.message : 'Unknown error',
            details: error
          };
          results.errors.push(errorInfo);
          
          // Continue with next step instead of failing completely
          console.error(`Failed to generate step: ${step.name}`, error);
        }

        this.updateProgress();
      }
    } finally {
      this.progress.isGenerating = false;
      this.progress.currentStep = 'Completed';
      this.updateProgress();
    }

    return results;
  }

  /**
   * Generate content for a specific step
   * @param step Generation step
   * @param onChunk Optional callback for streaming chunks
   * @returns Promise<any> Generated content
   */
  private async generateStep(step: GenerationStep, onChunk?: (chunk: string) => void): Promise<any> {
    switch (step.type) {
      case 'chapter':
        return this.generateChapterStep(step, onChunk);
      case 'quiz':
        return this.generateModuleQuizStep(step, onChunk);
      case 'final_exam':
        return this.generateFinalExamStep(onChunk);
      default:
        throw new AIGenerationError(
          `Unknown step type: ${step.type}`,
          'INVALID_STEP_TYPE'
        );
    }
  }

  /**
   * Generate chapter content
   * @param step Chapter generation step
   * @param onChunk Optional callback for streaming chunks
   * @returns Promise<AIGeneratedContent> Generated chapter content
   */
  private async generateChapterStep(step: GenerationStep, onChunk?: (chunk: string) => void): Promise<AIGeneratedContent> {
    if (step.moduleIndex === undefined || step.chapterIndex === undefined) {
      throw new AIGenerationError(
        'Module and chapter indices are required for chapter generation',
        'MISSING_INDICES'
      );
    }

    const targetModule = this.courseOutline.modules[step.moduleIndex];
    const chapter = targetModule.chapters[step.chapterIndex];
    return await generateChapterContent(
        chapter,
        targetModule,
        this.courseOutline,
        this.sessionId,
        onChunk
    );
  }

  /**
   * Generate module quiz
   * @param step Module quiz generation step
   * @param onChunk Optional callback for streaming chunks
   * @returns Promise<AIGeneratedQuiz> Generated module quiz
   */
  private async generateModuleQuizStep(step: GenerationStep, onChunk?: (chunk: string) => void): Promise<AIGeneratedQuiz> {
    if (step.moduleIndex === undefined) {
      throw new AIGenerationError(
        'Module index is required for module quiz generation',
        'MISSING_MODULE_INDEX'
      );
    }

    const targetModule = this.courseOutline.modules[step.moduleIndex];
      return await generateModuleQuiz(targetModule, this.courseOutline, this.sessionId, onChunk);
  }

  /**
   * Generate final exam
   * @param onChunk Optional callback for streaming chunks
   * @returns Promise<AIGeneratedQuiz> Generated final exam
   */
  private async generateFinalExamStep(onChunk?: (chunk: string) => void): Promise<AIGeneratedQuiz> {
      return await generateFinalExam(this.courseOutline, this.sessionId, onChunk);
  }

  /**
   * Store generation result in the appropriate collection
   * @param step Generation step
   * @param result Generated content
   * @param results Results collection
   */
  private storeResult(step: GenerationStep, result: any, results: BatchGenerationResults): void {
    switch (step.type) {
      case 'chapter':
        if (step.moduleIndex !== undefined && step.chapterIndex !== undefined) {
          const key = `${step.moduleIndex}-${step.chapterIndex}`;
          results.chapters.set(key, result as AIGeneratedContent);
        }
        break;
      case 'quiz':
        if (step.moduleIndex !== undefined) {
          results.moduleQuizzes.set(step.moduleIndex, result as AIGeneratedQuiz);
        }
        break;
      case 'final_exam':
        results.finalExam = result as AIGeneratedQuiz;
        break;
    }
  }

  /**
   * Update progress and notify listeners
   */
  private updateProgress(): void {
    if (this.onProgressUpdate) {
      this.onProgressUpdate({ ...this.progress });
    }
  }

  /**
   * Get current progress
   * @returns GenerationProgress Current progress state
   */
  getProgress(): GenerationProgress {
    return { ...this.progress };
  }

  /**
   * Get all generation steps
   * @returns GenerationStep[] Array of all steps
   */
  getSteps(): GenerationStep[] {
    return [...this.steps];
  }

  /**
   * Cancel the generation process
   */
  cancel(): void {
    this.progress.isGenerating = false;
    this.progress.currentStep = 'Cancelled';
    this.updateProgress();
  }
}

/**
 * Results from batch generation
 */
export interface BatchGenerationResults {
  chapters: Map<string, AIGeneratedContent>; // key: "moduleIndex-chapterIndex"
  moduleQuizzes: Map<number, AIGeneratedQuiz>; // key: moduleIndex
  finalExam: AIGeneratedQuiz | null;
  errors: Array<{
    step: string;
    error: string;
    details: any;
  }>;
}

/**
 * Generate course content in batches with progress tracking
 * @param courseOutline Course outline to generate content for
 * @param onProgress Progress callback
 * @param onStepComplete Step completion callback
 * @param sessionId Session ID for chat context
 * @param onChunk Optional callback for streaming chunks
 * @returns Promise<BatchGenerationResults> Complete generation results
 */
export const generateCourseContentBatch = async (
  courseOutline: AICourseOutline,
  onProgress?: (progress: GenerationProgress) => void,
  onStepComplete?: (step: GenerationStep, result: any) => void,
  sessionId?: string,
  onChunk?: (chunk: string) => void
): Promise<BatchGenerationResults> => {
  const generator = new BatchContentGenerator(
    courseOutline,
    onProgress,
    onStepComplete,
    sessionId,
  );

  return await generator.generateAll(onChunk);
};



/**
 * Estimate generation time based on course outline
 * @param courseOutline Course outline
 * @returns Estimated time in minutes
 */
export const estimateGenerationTime = (courseOutline: AICourseOutline): number => {
  let totalSteps = 0;
  
  // Count chapters
  courseOutline.modules.forEach(module => {
    totalSteps += module.chapters.length;
    if (module.hasModuleQuiz) {
      totalSteps += 1;
    }
  });
  
  // Add final exam
  if (courseOutline.hasFinalExam) {
    totalSteps += 1;
  }
  
  // Estimate 2-3 minutes per step (chapter content takes longer)
  const avgTimePerStep = 2.5;
  return Math.ceil(totalSteps * avgTimePerStep);
};