{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "file": "eventbuilder.js", "sources": ["file:///C:/Users/<USER>/Documents/File%20Kerja%20Daffa/GAWEAN/TERANG_LMS/terang-lms-frontend/node_modules/%40sentry/browser/src/eventbuilder.ts"], "sourcesContent": ["import type {\n  Event,\n  EventHint,\n  Exception,\n  ParameterizedString,\n  SeverityLevel,\n  StackFrame,\n  StackParser,\n} from '@sentry/core';\nimport {\n  addExceptionMechanism,\n  addExceptionTypeValue,\n  extractExceptionKeysForMessage,\n  getClient,\n  isDOMError,\n  isDOMException,\n  isError,\n  isErrorEvent,\n  isEvent,\n  isParameterizedString,\n  isPlainObject,\n  normalizeToSize,\n  resolvedSyncPromise,\n} from '@sentry/core';\n\ntype Prototype = { constructor: (...args: unknown[]) => unknown };\n\n/**\n * This function creates an exception from a JavaScript Error\n */\nexport function exceptionFromError(stackParser: StackParser, ex: Error): Exception {\n  // Get the frames first since Opera can lose the stack if we touch anything else first\n  const frames = parseStackFrames(stackParser, ex);\n\n  const exception: Exception = {\n    type: extractType(ex),\n    value: extractMessage(ex),\n  };\n\n  if (frames.length) {\n    exception.stacktrace = { frames };\n  }\n\n  if (exception.type === undefined && exception.value === '') {\n    exception.value = 'Unrecoverable error caught';\n  }\n\n  return exception;\n}\n\nfunction eventFromPlainObject(\n  stackParser: StackParser,\n  exception: Record<string, unknown>,\n  syntheticException?: Error,\n  isUnhandledRejection?: boolean,\n): Event {\n  const client = getClient();\n  const normalizeDepth = client?.getOptions().normalizeDepth;\n\n  // If we can, we extract an exception from the object properties\n  const errorFromProp = getErrorPropertyFromObject(exception);\n\n  const extra = {\n    __serialized__: normalizeToSize(exception, normalizeDepth),\n  };\n\n  if (errorFromProp) {\n    return {\n      exception: {\n        values: [exceptionFromError(stackParser, errorFromProp)],\n      },\n      extra,\n    };\n  }\n\n  const event = {\n    exception: {\n      values: [\n        {\n          type: isEvent(exception) ? exception.constructor.name : isUnhandledRejection ? 'UnhandledRejection' : 'Error',\n          value: getNonErrorObjectExceptionValue(exception, { isUnhandledRejection }),\n        } as Exception,\n      ],\n    },\n    extra,\n  } satisfies Event;\n\n  if (syntheticException) {\n    const frames = parseStackFrames(stackParser, syntheticException);\n    if (frames.length) {\n      // event.exception.values[0] has been set above\n      // eslint-disable-next-line @typescript-eslint/no-non-null-assertion\n      event.exception.values[0]!.stacktrace = { frames };\n    }\n  }\n\n  return event;\n}\n\nfunction eventFromError(stackParser: StackParser, ex: Error): Event {\n  return {\n    exception: {\n      values: [exceptionFromError(stackParser, ex)],\n    },\n  };\n}\n\n/** Parses stack frames from an error */\nfunction parseStackFrames(\n  stackParser: StackParser,\n  ex: Error & { framesToPop?: number; stacktrace?: string },\n): StackFrame[] {\n  // Access and store the stacktrace property before doing ANYTHING\n  // else to it because Opera is not very good at providing it\n  // reliably in other circumstances.\n  const stacktrace = ex.stacktrace || ex.stack || '';\n\n  const skipLines = getSkipFirstStackStringLines(ex);\n  const framesToPop = getPopFirstTopFrames(ex);\n\n  try {\n    return stackParser(stacktrace, skipLines, framesToPop);\n  } catch {\n    // no-empty\n  }\n\n  return [];\n}\n\n// Based on our own mapping pattern - https://github.com/getsentry/sentry/blob/9f08305e09866c8bd6d0c24f5b0aabdd7dd6c59c/src/sentry/lang/javascript/errormapping.py#L83-L108\nconst reactMinifiedRegexp = /Minified React error #\\d+;/i;\n\n/**\n * Certain known React errors contain links that would be falsely\n * parsed as frames. This function check for these errors and\n * returns number of the stack string lines to skip.\n */\nfunction getSkipFirstStackStringLines(ex: Error): number {\n  if (ex && reactMinifiedRegexp.test(ex.message)) {\n    return 1;\n  }\n\n  return 0;\n}\n\n/**\n * If error has `framesToPop` property, it means that the\n * creator tells us the first x frames will be useless\n * and should be discarded. Typically error from wrapper function\n * which don't point to the actual location in the developer's code.\n *\n * Example: https://github.com/zertosh/invariant/blob/master/invariant.js#L46\n */\nfunction getPopFirstTopFrames(ex: Error & { framesToPop?: unknown }): number {\n  if (typeof ex.framesToPop === 'number') {\n    return ex.framesToPop;\n  }\n\n  return 0;\n}\n\n// https://developer.mozilla.org/en-US/docs/WebAssembly/JavaScript_interface/Exception\n// @ts-expect-error - WebAssembly.Exception is a valid class\nfunction isWebAssemblyException(exception: unknown): exception is WebAssembly.Exception {\n  // Check for support\n  // @ts-expect-error - WebAssembly.Exception is a valid class\n  if (typeof WebAssembly !== 'undefined' && typeof WebAssembly.Exception !== 'undefined') {\n    // @ts-expect-error - WebAssembly.Exception is a valid class\n    return exception instanceof WebAssembly.Exception;\n  } else {\n    return false;\n  }\n}\n\n/**\n * Extracts from errors what we use as the exception `type` in error events.\n *\n * Usually, this is the `name` property on Error objects but WASM errors need to be treated differently.\n */\nexport function extractType(ex: Error & { message: { error?: Error } }): string | undefined {\n  const name = ex?.name;\n\n  // The name for WebAssembly.Exception Errors needs to be extracted differently.\n  // Context: https://github.com/getsentry/sentry-javascript/issues/13787\n  if (!name && isWebAssemblyException(ex)) {\n    // Emscripten sets array[type, message] to the \"message\" property on the WebAssembly.Exception object\n    const hasTypeInMessage = ex.message && Array.isArray(ex.message) && ex.message.length == 2;\n    return hasTypeInMessage ? ex.message[0] : 'WebAssembly.Exception';\n  }\n\n  return name;\n}\n\n/**\n * There are cases where stacktrace.message is an Event object\n * https://github.com/getsentry/sentry-javascript/issues/1949\n * In this specific case we try to extract stacktrace.message.error.message\n */\nexport function extractMessage(ex: Error & { message: { error?: Error } }): string {\n  const message = ex?.message;\n\n  if (isWebAssemblyException(ex)) {\n    // For Node 18, Emscripten sets array[type, message] to the \"message\" property on the WebAssembly.Exception object\n    if (Array.isArray(ex.message) && ex.message.length == 2) {\n      return ex.message[1];\n    }\n    return 'wasm exception';\n  }\n\n  if (!message) {\n    return 'No error message';\n  }\n\n  if (message.error && typeof message.error.message === 'string') {\n    return message.error.message;\n  }\n\n  return message;\n}\n\n/**\n * Creates an {@link Event} from all inputs to `captureException` and non-primitive inputs to `captureMessage`.\n * @hidden\n */\nexport function eventFromException(\n  stackParser: StackParser,\n  exception: unknown,\n  hint?: EventHint,\n  attachStacktrace?: boolean,\n): PromiseLike<Event> {\n  const syntheticException = hint?.syntheticException || undefined;\n  const event = eventFromUnknownInput(stackParser, exception, syntheticException, attachStacktrace);\n  addExceptionMechanism(event); // defaults to { type: 'generic', handled: true }\n  event.level = 'error';\n  if (hint?.event_id) {\n    event.event_id = hint.event_id;\n  }\n  return resolvedSyncPromise(event);\n}\n\n/**\n * Builds and Event from a Message\n * @hidden\n */\nexport function eventFromMessage(\n  stackParser: StackParser,\n  message: ParameterizedString,\n  level: SeverityLevel = 'info',\n  hint?: EventHint,\n  attachStacktrace?: boolean,\n): PromiseLike<Event> {\n  const syntheticException = hint?.syntheticException || undefined;\n  const event = eventFromString(stackParser, message, syntheticException, attachStacktrace);\n  event.level = level;\n  if (hint?.event_id) {\n    event.event_id = hint.event_id;\n  }\n  return resolvedSyncPromise(event);\n}\n\n/**\n * @hidden\n */\nexport function eventFromUnknownInput(\n  stackParser: StackParser,\n  exception: unknown,\n  syntheticException?: Error,\n  attachStacktrace?: boolean,\n  isUnhandledRejection?: boolean,\n): Event {\n  let event: Event;\n\n  if (isErrorEvent(exception as ErrorEvent) && (exception as ErrorEvent).error) {\n    // If it is an ErrorEvent with `error` property, extract it to get actual Error\n    const errorEvent = exception as ErrorEvent;\n    return eventFromError(stackParser, errorEvent.error as Error);\n  }\n\n  // If it is a `DOMError` (which is a legacy API, but still supported in some browsers) then we just extract the name\n  // and message, as it doesn't provide anything else. According to the spec, all `DOMExceptions` should also be\n  // `Error`s, but that's not the case in IE11, so in that case we treat it the same as we do a `DOMError`.\n  //\n  // https://developer.mozilla.org/en-US/docs/Web/API/DOMError\n  // https://developer.mozilla.org/en-US/docs/Web/API/DOMException\n  // https://webidl.spec.whatwg.org/#es-DOMException-specialness\n  if (isDOMError(exception) || isDOMException(exception as DOMException)) {\n    const domException = exception as DOMException;\n\n    if ('stack' in (exception as Error)) {\n      event = eventFromError(stackParser, exception as Error);\n    } else {\n      const name = domException.name || (isDOMError(domException) ? 'DOMError' : 'DOMException');\n      const message = domException.message ? `${name}: ${domException.message}` : name;\n      event = eventFromString(stackParser, message, syntheticException, attachStacktrace);\n      addExceptionTypeValue(event, message);\n    }\n    if ('code' in domException) {\n      // eslint-disable-next-line deprecation/deprecation\n      event.tags = { ...event.tags, 'DOMException.code': `${domException.code}` };\n    }\n\n    return event;\n  }\n  if (isError(exception)) {\n    // we have a real Error object, do nothing\n    return eventFromError(stackParser, exception);\n  }\n  if (isPlainObject(exception) || isEvent(exception)) {\n    // If it's a plain object or an instance of `Event` (the built-in JS kind, not this SDK's `Event` type), serialize\n    // it manually. This will allow us to group events based on top-level keys which is much better than creating a new\n    // group on any key/value change.\n    const objectException = exception as Record<string, unknown>;\n    event = eventFromPlainObject(stackParser, objectException, syntheticException, isUnhandledRejection);\n    addExceptionMechanism(event, {\n      synthetic: true,\n    });\n    return event;\n  }\n\n  // If none of previous checks were valid, then it means that it's not:\n  // - an instance of DOMError\n  // - an instance of DOMException\n  // - an instance of Event\n  // - an instance of Error\n  // - a valid ErrorEvent (one with an error property)\n  // - a plain Object\n  //\n  // So bail out and capture it as a simple message:\n  event = eventFromString(stackParser, exception as string, syntheticException, attachStacktrace);\n  addExceptionTypeValue(event, `${exception}`, undefined);\n  addExceptionMechanism(event, {\n    synthetic: true,\n  });\n\n  return event;\n}\n\nfunction eventFromString(\n  stackParser: StackParser,\n  message: ParameterizedString,\n  syntheticException?: Error,\n  attachStacktrace?: boolean,\n): Event {\n  const event: Event = {};\n\n  if (attachStacktrace && syntheticException) {\n    const frames = parseStackFrames(stackParser, syntheticException);\n    if (frames.length) {\n      event.exception = {\n        values: [{ value: message, stacktrace: { frames } }],\n      };\n    }\n    addExceptionMechanism(event, { synthetic: true });\n  }\n\n  if (isParameterizedString(message)) {\n    const { __sentry_template_string__, __sentry_template_values__ } = message;\n\n    event.logentry = {\n      message: __sentry_template_string__,\n      params: __sentry_template_values__,\n    };\n    return event;\n  }\n\n  event.message = message;\n  return event;\n}\n\nfunction getNonErrorObjectExceptionValue(\n  exception: Record<string, unknown>,\n  { isUnhandledRejection }: { isUnhandledRejection?: boolean },\n): string {\n  const keys = extractExceptionKeysForMessage(exception);\n  const captureType = isUnhandledRejection ? 'promise rejection' : 'exception';\n\n  // Some ErrorEvent instances do not have an `error` property, which is why they are not handled before\n  // We still want to try to get a decent message for these cases\n  if (isErrorEvent(exception)) {\n    return `Event \\`ErrorEvent\\` captured as ${captureType} with message \\`${exception.message}\\``;\n  }\n\n  if (isEvent(exception)) {\n    const className = getObjectClassName(exception);\n    return `Event \\`${className}\\` (type=${exception.type}) captured as ${captureType}`;\n  }\n\n  return `Object captured as ${captureType} with keys: ${keys}`;\n}\n\nfunction getObjectClassName(obj: unknown): string | undefined | void {\n  try {\n    const prototype: Prototype | null = Object.getPrototypeOf(obj);\n    return prototype ? prototype.constructor.name : undefined;\n  } catch {\n    // ignore errors here\n  }\n}\n\n/** If a plain object has a property that is an `Error`, return this error. */\nfunction getErrorPropertyFromObject(obj: Record<string, unknown>): Error | undefined {\n  for (const prop in obj) {\n    if (Object.prototype.hasOwnProperty.call(obj, prop)) {\n      const value = obj[prop];\n      if (value instanceof Error) {\n        return value;\n      }\n    }\n  }\n\n  return undefined;\n}\n"], "names": [], "mappings": ";;;;;;;;;;;;;;;AA2BA;;CAEA,GACO,SAAS,kBAAkB,CAAC,WAAW,EAAe,EAAE,EAAoB;IACnF,sFAAA;IACE,MAAM,SAAS,gBAAgB,CAAC,WAAW,EAAE,EAAE,CAAC;IAEhD,MAAM,SAAS,GAAc;QAC3B,IAAI,EAAE,WAAW,CAAC,EAAE,CAAC;QACrB,KAAK,EAAE,cAAc,CAAC,EAAE,CAAC;IAC7B,CAAG;IAED,IAAI,MAAM,CAAC,MAAM,EAAE;QACjB,SAAS,CAAC,UAAA,GAAa;YAAE;QAAA,CAAQ;IACrC;IAEE,IAAI,SAAS,CAAC,IAAA,KAAS,SAAA,IAAa,SAAS,CAAC,KAAA,KAAU,EAAE,EAAE;QAC1D,SAAS,CAAC,KAAA,GAAQ,4BAA4B;IAClD;IAEE,OAAO,SAAS;AAClB;AAEA,SAAS,oBAAoB,CAC3B,WAAW,EACX,SAAS,EACT,kBAAkB,EAClB,oBAAoB;IAEpB,MAAM,MAAA,qKAAS,YAAA,AAAS,EAAE;IAC1B,MAAM,iBAAiB,MAAM,EAAE,UAAU,EAAE,CAAC,cAAc;IAE5D,gEAAA;IACE,MAAM,aAAA,GAAgB,0BAA0B,CAAC,SAAS,CAAC;IAE3D,MAAM,QAAQ;QACZ,cAAc,yKAAE,kBAAA,AAAe,EAAC,SAAS,EAAE,cAAc,CAAC;IAC9D,CAAG;IAED,IAAI,aAAa,EAAE;QACjB,OAAO;YACL,SAAS,EAAE;gBACT,MAAM,EAAE;oBAAC,kBAAkB,CAAC,WAAW,EAAE,aAAa,CAAC;iBAAC;YAChE,CAAO;YACD,KAAK;QACX,CAAK;IACL;IAEE,MAAM,QAAQ;QACZ,SAAS,EAAE;YACT,MAAM,EAAE;gBACN;oBACE,IAAI,kKAAE,UAAA,AAAO,EAAC,SAAS,IAAI,SAAS,CAAC,WAAW,CAAC,IAAA,GAAO,uBAAuB,oBAAA,GAAuB,OAAO;oBAC7G,KAAK,EAAE,+BAA+B,CAAC,SAAS,EAAE;wBAAE,oBAAA;oBAAA,CAAsB,CAAC;gBACrF,CAAQ;aACD;QACP,CAAK;QACD,KAAK;IACT,CAAE;IAEA,IAAI,kBAAkB,EAAE;QACtB,MAAM,SAAS,gBAAgB,CAAC,WAAW,EAAE,kBAAkB,CAAC;QAChE,IAAI,MAAM,CAAC,MAAM,EAAE;YACvB,+CAAA;YACA,oEAAA;YACM,KAAK,CAAC,SAAS,CAAC,MAAM,CAAC,CAAC,CAAC,CAAE,UAAA,GAAa;gBAAE;YAAA,CAAQ;QACxD;IACA;IAEE,OAAO,KAAK;AACd;AAEA,SAAS,cAAc,CAAC,WAAW,EAAe,EAAE,EAAgB;IAClE,OAAO;QACL,SAAS,EAAE;YACT,MAAM,EAAE;gBAAC,kBAAkB,CAAC,WAAW,EAAE,EAAE,CAAC;aAAC;QACnD,CAAK;IACL,CAAG;AACH;AAEA,sCAAA,GACA,SAAS,gBAAgB,CACvB,WAAW,EACX,EAAE;IAEJ,iEAAA;IACA,4DAAA;IACA,mCAAA;IACE,MAAM,UAAA,GAAa,EAAE,CAAC,UAAA,IAAc,EAAE,CAAC,KAAA,IAAS,EAAE;IAElD,MAAM,SAAA,GAAY,4BAA4B,CAAC,EAAE,CAAC;IAClD,MAAM,WAAA,GAAc,oBAAoB,CAAC,EAAE,CAAC;IAE5C,IAAI;QACF,OAAO,WAAW,CAAC,UAAU,EAAE,SAAS,EAAE,WAAW,CAAC;IAC1D,EAAI,OAAM;IACV,WAAA;IACA;IAEE,OAAO,EAAE;AACX;AAEA,2KAAA;AACA,MAAM,mBAAA,GAAsB,6BAA6B;AAEzD;;;;CAIA,GACA,SAAS,4BAA4B,CAAC,EAAE,EAAiB;IACvD,IAAI,EAAA,IAAM,mBAAmB,CAAC,IAAI,CAAC,EAAE,CAAC,OAAO,CAAC,EAAE;QAC9C,OAAO,CAAC;IACZ;IAEE,OAAO,CAAC;AACV;AAEA;;;;;;;CAOA,GACA,SAAS,oBAAoB,CAAC,EAAE,EAA6C;IAC3E,IAAI,OAAO,EAAE,CAAC,WAAA,KAAgB,QAAQ,EAAE;QACtC,OAAO,EAAE,CAAC,WAAW;IACzB;IAEE,OAAO,CAAC;AACV;AAEA,sFAAA;AACA,4DAAA;AACA,SAAS,sBAAsB,CAAC,SAAS,EAA+C;IACxF,oBAAA;IACA,4DAAA;IACE,IAAI,OAAO,WAAA,KAAgB,WAAA,IAAe,OAAO,WAAW,CAAC,SAAA,KAAc,WAAW,EAAE;QAC1F,4DAAA;QACI,OAAO,SAAA,YAAqB,WAAW,CAAC,SAAS;IACrD,OAAS;QACL,OAAO,KAAK;IAChB;AACA;AAEA;;;;CAIA,GACO,SAAS,WAAW,CAAC,EAAE,EAA8D;IAC1F,MAAM,IAAA,GAAO,EAAE,EAAE,IAAI;IAEvB,+EAAA;IACA,uEAAA;IACE,IAAI,CAAC,IAAA,IAAQ,sBAAsB,CAAC,EAAE,CAAC,EAAE;QAC3C,qGAAA;QACI,MAAM,mBAAmB,EAAE,CAAC,OAAA,IAAW,KAAK,CAAC,OAAO,CAAC,EAAE,CAAC,OAAO,KAAK,EAAE,CAAC,OAAO,CAAC,MAAA,IAAU,CAAC;QAC1F,OAAO,gBAAA,GAAmB,EAAE,CAAC,OAAO,CAAC,CAAC,CAAA,GAAI,uBAAuB;IACrE;IAEE,OAAO,IAAI;AACb;AAEA;;;;CAIA,GACO,SAAS,cAAc,CAAC,EAAE,EAAkD;IACjF,MAAM,OAAA,GAAU,EAAE,EAAE,OAAO;IAE3B,IAAI,sBAAsB,CAAC,EAAE,CAAC,EAAE;QAClC,kHAAA;QACI,IAAI,KAAK,CAAC,OAAO,CAAC,EAAE,CAAC,OAAO,CAAA,IAAK,EAAE,CAAC,OAAO,CAAC,MAAA,IAAU,CAAC,EAAE;YACvD,OAAO,EAAE,CAAC,OAAO,CAAC,CAAC,CAAC;QAC1B;QACI,OAAO,gBAAgB;IAC3B;IAEE,IAAI,CAAC,OAAO,EAAE;QACZ,OAAO,kBAAkB;IAC7B;IAEE,IAAI,OAAO,CAAC,KAAA,IAAS,OAAO,OAAO,CAAC,KAAK,CAAC,OAAA,KAAY,QAAQ,EAAE;QAC9D,OAAO,OAAO,CAAC,KAAK,CAAC,OAAO;IAChC;IAEE,OAAO,OAAO;AAChB;AAEA;;;CAGA,GACO,SAAS,kBAAkB,CAChC,WAAW,EACX,SAAS,EACT,IAAI,EACJ,gBAAgB;IAEhB,MAAM,kBAAA,GAAqB,IAAI,EAAE,kBAAA,IAAsB,SAAS;IAChE,MAAM,KAAA,GAAQ,qBAAqB,CAAC,WAAW,EAAE,SAAS,EAAE,kBAAkB,EAAE,gBAAgB,CAAC;sKACjG,wBAAA,AAAqB,EAAC,KAAK,CAAC,CAAA,CAAA,iDAAA;IAC5B,KAAK,CAAC,KAAA,GAAQ,OAAO;IACrB,IAAI,IAAI,EAAE,QAAQ,EAAE;QAClB,KAAK,CAAC,QAAA,GAAW,IAAI,CAAC,QAAQ;IAClC;IACE,gLAAO,sBAAA,AAAmB,EAAC,KAAK,CAAC;AACnC;AAEA;;;CAGA,GACO,SAAS,gBAAgB,CAC9B,WAAW,EACX,OAAO,EACP,KAAK,GAAkB,MAAM,EAC7B,IAAI,EACJ,gBAAgB;IAEhB,MAAM,kBAAA,GAAqB,IAAI,EAAE,kBAAA,IAAsB,SAAS;IAChE,MAAM,KAAA,GAAQ,eAAe,CAAC,WAAW,EAAE,OAAO,EAAE,kBAAkB,EAAE,gBAAgB,CAAC;IACzF,KAAK,CAAC,KAAA,GAAQ,KAAK;IACnB,IAAI,IAAI,EAAE,QAAQ,EAAE;QAClB,KAAK,CAAC,QAAA,GAAW,IAAI,CAAC,QAAQ;IAClC;IACE,gLAAO,sBAAA,AAAmB,EAAC,KAAK,CAAC;AACnC;AAEA;;CAEA,GACO,SAAS,qBAAqB,CACnC,WAAW,EACX,SAAS,EACT,kBAAkB,EAClB,gBAAgB,EAChB,oBAAoB;IAEpB,IAAI,KAAK;IAET,oKAAI,eAAA,AAAY,EAAC,SAAA,EAAU,GAAkB,AAAC,SAAA,CAAyB,KAAK,EAAE;QAChF,+EAAA;QACI,MAAM,UAAA,GAAa,SAAA;QACnB,OAAO,cAAc,CAAC,WAAW,EAAE,UAAU,CAAC,KAAA,EAAe;IACjE;IAEA,oHAAA;IACA,8GAAA;IACA,yGAAA;IACA,EAAA;IACA,4DAAA;IACA,gEAAA;IACA,8DAAA;IACE,IAAI,6KAAA,AAAU,EAAC,SAAS,CAAA,oKAAK,iBAAA,AAAc,EAAC,SAAA,EAA0B,CAAE;QACtE,MAAM,YAAA,GAAe,SAAA;QAErB,IAAI,OAAA,IAAY,SAAA,EAAmB,AAAE;YACnC,QAAQ,cAAc,CAAC,WAAW,EAAE,WAAmB;QAC7D,OAAW;YACL,MAAM,IAAA,GAAO,YAAY,CAAC,IAAA,IAAA,iKAAS,aAAA,AAAU,EAAC,YAAY,CAAA,GAAI,UAAA,GAAa,cAAc,CAAC;YAC1F,MAAM,UAAU,YAAY,CAAC,OAAA,GAAU,CAAC,EAAA,IAAA,CAAA,EAAA,EAAA,YAAA,CAAA,OAAA,CAAA,CAAA,GAAA,IAAA;YACA,KAAA,GAAA,eAAA,CAAA,WAAA,EAAA,OAAA,EAAA,kBAAA,EAAA,gBAAA,CAAA;8KACA,wBAAA,EAAA,KAAA,EAAA,OAAA,CAAA;QACA;QACA,IAAA,MAAA,IAAA,YAAA,EAAA;YACA,mDAAA;YACA,KAAA,CAAA,IAAA,GAAA;gBAAA,GAAA,KAAA,CAAA,IAAA;gBAAA,mBAAA,EAAA,CAAA,EAAA,YAAA,CAAA,IAAA,CAAA,CAAA;YAAA,CAAA;QACA;QAEA,OAAA,KAAA;IACA;IACA,oKAAA,UAAA,EAAA,SAAA,CAAA,EAAA;QACA,0CAAA;QACA,OAAA,cAAA,CAAA,WAAA,EAAA,SAAA,CAAA;IACA;IACA,oKAAA,gBAAA,EAAA,SAAA,CAAA,oKAAA,UAAA,EAAA,SAAA,CAAA,EAAA;QACA,kHAAA;QACA,mHAAA;QACA,iCAAA;QACA,MAAA,eAAA,GAAA,SAAA;QACA,KAAA,GAAA,oBAAA,CAAA,WAAA,EAAA,eAAA,EAAA,kBAAA,EAAA,oBAAA,CAAA;0KACA,wBAAA,EAAA,KAAA,EAAA;YACA,SAAA,EAAA,IAAA;QACA,CAAA,CAAA;QACA,OAAA,KAAA;IACA;IAEA,sEAAA;IACA,4BAAA;IACA,gCAAA;IACA,yBAAA;IACA,yBAAA;IACA,oDAAA;IACA,mBAAA;IACA,EAAA;IACA,kDAAA;IACA,KAAA,GAAA,eAAA,CAAA,WAAA,EAAA,SAAA,EAAA,kBAAA,EAAA,gBAAA,CAAA;sKACA,wBAAA,EAAA,KAAA,EAAA,CAAA,EAAA,SAAA,CAAA,CAAA,EAAA,SAAA,CAAA;KACA,yLAAA,EAAA,KAAA,EAAA;QACA,SAAA,EAAA,IAAA;IACA,CAAA,CAAA;IAEA,OAAA,KAAA;AACA;AAEA,SAAA,eAAA,CACA,WAAA,EACA,OAAA,EACA,kBAAA,EACA,gBAAA;IAEA,MAAA,KAAA,GAAA,CAAA,CAAA;IAEA,IAAA,gBAAA,IAAA,kBAAA,EAAA;QACA,MAAA,MAAA,GAAA,gBAAA,CAAA,WAAA,EAAA,kBAAA,CAAA;QACA,IAAA,MAAA,CAAA,MAAA,EAAA;YACA,KAAA,CAAA,SAAA,GAAA;gBACA,MAAA,EAAA;oBAAA;wBAAA,KAAA,EAAA,OAAA;wBAAA,UAAA,EAAA;4BAAA,MAAA;wBAAA,CAAA;oBAAA,CAAA;iBAAA;YACA,CAAA;QACA;QACA,0LAAA,EAAA,KAAA,EAAA;YAAA,SAAA,EAAA,IAAA;QAAA,CAAA,CAAA;IACA;IAEA,oKAAA,wBAAA,EAAA,OAAA,CAAA,EAAA;QACA,MAAA,EAAA,0BAAA,EAAA,0BAAA,EAAA,GAAA,OAAA;QAEA,KAAA,CAAA,QAAA,GAAA;YACA,OAAA,EAAA,0BAAA;YACA,MAAA,EAAA,0BAAA;QACA,CAAA;QACA,OAAA,KAAA;IACA;IAEA,KAAA,CAAA,OAAA,GAAA,OAAA;IACA,OAAA,KAAA;AACA;AAEA,SAAA,+BAAA,CACA,SAAA,EACA,EAAA,oBAAA,EAAA;IAEA,MAAA,IAAA,GAAA,qMAAA,EAAA,SAAA,CAAA;IACA,MAAA,WAAA,GAAA,oBAAA,GAAA,mBAAA,GAAA,WAAA;IAEA,sGAAA;IACA,+DAAA;IACA,oKAAA,eAAA,EAAA,SAAA,CAAA,EAAA;QACA,OAAA,CAAA,iCAAA,EAAA,WAAA,CAAA,gBAAA,EAAA,SAAA,CAAA,OAAA,CAAA,EAAA,CAAA;IACA;IAEA,oKAAA,UAAA,EAAA,SAAA,CAAA,EAAA;QACA,MAAA,SAAA,GAAA,kBAAA,CAAA,SAAA,CAAA;QACA,OAAA,CAAA,QAAA,EAAA,SAAA,CAAA,SAAA,EAAA,SAAA,CAAA,IAAA,CAAA,cAAA,EAAA,WAAA,CAAA,CAAA;IACA;IAEA,OAAA,CAAA,mBAAA,EAAA,WAAA,CAAA,YAAA,EAAA,IAAA,CAAA,CAAA;AACA;AAEA,SAAA,kBAAA,CAAA,GAAA,EAAA;IACA,IAAA;QACA,MAAA,SAAA,GAAA,MAAA,CAAA,cAAA,CAAA,GAAA,CAAA;QACA,OAAA,SAAA,GAAA,SAAA,CAAA,WAAA,CAAA,IAAA,GAAA,SAAA;IACA,CAAA,CAAA,OAAA;IACA,qBAAA;IACA;AACA;AAEA,4EAAA,GACA,SAAA,0BAAA,CAAA,GAAA,EAAA;IACA,IAAA,MAAA,IAAA,IAAA,GAAA,CAAA;QACA,IAAA,MAAA,CAAA,SAAA,CAAA,cAAA,CAAA,IAAA,CAAA,GAAA,EAAA,IAAA,CAAA,EAAA;YACA,MAAA,KAAA,GAAA,GAAA,CAAA,IAAA,CAAA;YACA,IAAA,KAAA,YAAA,KAAA,EAAA;gBACA,OAAA,KAAA;YACA;QACA;IACA;IAEA,OAAA,SAAA;AACA", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 343, "column": 0}, "map": {"version": 3, "file": "helpers.js", "sources": ["file:///C:/Users/<USER>/Documents/File%20Kerja%20Daffa/GAWEAN/TERANG_LMS/terang-lms-frontend/node_modules/%40sentry/browser/src/helpers.ts"], "sourcesContent": ["import type { Mechanism, WrappedFunction } from '@sentry/core';\nimport {\n  addExceptionMechanism,\n  addExceptionTypeValue,\n  addNonEnumerableProperty,\n  captureException,\n  getLocationHref,\n  getOriginalFunction,\n  GLOBAL_OBJ,\n  markFunctionWrapped,\n  withScope,\n} from '@sentry/core';\n\nexport const WINDOW = GLOBAL_OBJ as typeof GLOBAL_OBJ & Window;\n\nlet ignoreOnError: number = 0;\n\n/**\n * @hidden\n */\nexport function shouldIgnoreOnError(): boolean {\n  return ignoreOnError > 0;\n}\n\n/**\n * @hidden\n */\nexport function ignoreNextOnError(): void {\n  // onerror should trigger before setTimeout\n  ignoreOnError++;\n  setTimeout(() => {\n    ignoreOnError--;\n  });\n}\n\n// eslint-disable-next-line @typescript-eslint/ban-types\ntype WrappableFunction = Function;\n\nexport function wrap<T extends WrappableFunction>(\n  fn: T,\n  options?: {\n    mechanism?: Mechanism;\n  },\n): WrappedFunction<T>;\nexport function wrap<NonFunction>(\n  fn: NonFunction,\n  options?: {\n    mechanism?: Mechanism;\n  },\n): NonFunction;\n/**\n * Instruments the given function and sends an event to Sentry every time the\n * function throws an exception.\n *\n * @param fn A function to wrap. It is generally safe to pass an unbound function, because the returned wrapper always\n * has a correct `this` context.\n * @returns The wrapped function.\n * @hidden\n */\nexport function wrap<T extends WrappableFunction, NonFunction>(\n  fn: T | NonFunction,\n  options: {\n    mechanism?: Mechanism;\n  } = {},\n): NonFunction | WrappedFunction<T> {\n  // for future readers what this does is wrap a function and then create\n  // a bi-directional wrapping between them.\n  //\n  // example: wrapped = wrap(original);\n  //  original.__sentry_wrapped__ -> wrapped\n  //  wrapped.__sentry_original__ -> original\n\n  function isFunction(fn: T | NonFunction): fn is T {\n    return typeof fn === 'function';\n  }\n\n  if (!isFunction(fn)) {\n    return fn;\n  }\n\n  try {\n    // if we're dealing with a function that was previously wrapped, return\n    // the original wrapper.\n    const wrapper = (fn as WrappedFunction<T>).__sentry_wrapped__;\n    if (wrapper) {\n      if (typeof wrapper === 'function') {\n        return wrapper;\n      } else {\n        // If we find that the `__sentry_wrapped__` function is not a function at the time of accessing it, it means\n        // that something messed with it. In that case we want to return the originally passed function.\n        return fn;\n      }\n    }\n\n    // We don't wanna wrap it twice\n    if (getOriginalFunction(fn)) {\n      return fn;\n    }\n  } catch {\n    // Just accessing custom props in some Selenium environments\n    // can cause a \"Permission denied\" exception (see raven-js#495).\n    // Bail on wrapping and return the function as-is (defers to window.onerror).\n    return fn;\n  }\n\n  // Wrap the function itself\n  // It is important that `sentryWrapped` is not an arrow function to preserve the context of `this`\n  const sentryWrapped = function (this: unknown, ...args: unknown[]): unknown {\n    try {\n      // Also wrap arguments that are themselves functions\n      const wrappedArguments = args.map(arg => wrap(arg, options));\n\n      // Attempt to invoke user-land function\n      // NOTE: If you are a Sentry user, and you are seeing this stack frame, it\n      //       means the sentry.javascript SDK caught an error invoking your application code. This\n      //       is expected behavior and NOT indicative of a bug with sentry.javascript.\n      return fn.apply(this, wrappedArguments);\n    } catch (ex) {\n      ignoreNextOnError();\n\n      withScope(scope => {\n        scope.addEventProcessor(event => {\n          if (options.mechanism) {\n            addExceptionTypeValue(event, undefined, undefined);\n            addExceptionMechanism(event, options.mechanism);\n          }\n\n          event.extra = {\n            ...event.extra,\n            arguments: args,\n          };\n\n          return event;\n        });\n\n        captureException(ex);\n      });\n\n      throw ex;\n    }\n  } as unknown as WrappedFunction<T>;\n\n  // Wrap the wrapped function in a proxy, to ensure any other properties of the original function remain available\n  try {\n    for (const property in fn) {\n      if (Object.prototype.hasOwnProperty.call(fn, property)) {\n        sentryWrapped[property as keyof T] = fn[property as keyof T];\n      }\n    }\n  } catch {\n    // Accessing some objects may throw\n    // ref: https://github.com/getsentry/sentry-javascript/issues/1168\n  }\n\n  // Signal that this function has been wrapped/filled already\n  // for both debugging and to prevent it to being wrapped/filled twice\n  markFunctionWrapped(sentryWrapped, fn);\n\n  addNonEnumerableProperty(fn, '__sentry_wrapped__', sentryWrapped);\n\n  // Restore original function name (not all browsers allow that)\n  try {\n    // eslint-disable-next-line @typescript-eslint/no-non-null-assertion\n    const descriptor = Object.getOwnPropertyDescriptor(sentryWrapped, 'name')!;\n    if (descriptor.configurable) {\n      Object.defineProperty(sentryWrapped, 'name', {\n        get(): string {\n          return fn.name;\n        },\n      });\n    }\n  } catch {\n    // This may throw if e.g. the descriptor does not exist, or a browser does not allow redefining `name`.\n    // to save some bytes we simply try-catch this\n  }\n\n  return sentryWrapped;\n}\n\n/**\n * Get HTTP request data from the current page.\n */\nexport function getHttpRequestData(): { url: string; headers: Record<string, string> } {\n  // grab as much info as exists and add it to the event\n  const url = getLocationHref();\n  const { referrer } = WINDOW.document || {};\n  const { userAgent } = WINDOW.navigator || {};\n\n  const headers = {\n    ...(referrer && { Referer: referrer }),\n    ...(userAgent && { 'User-Agent': userAgent }),\n  };\n  const request = {\n    url,\n    headers,\n  };\n\n  return request;\n}\n"], "names": [], "mappings": ";;;;;;;;;;;;;;AAaO,MAAM,MAAA,sKAAS,aAAA;AAEtB,IAAI,aAAa,GAAW,CAAC;AAE7B;;CAEA,GACO,SAAS,mBAAmB,GAAY;IAC7C,OAAO,aAAA,GAAgB,CAAC;AAC1B;AAEA;;CAEA,GACO,SAAS,iBAAiB,GAAS;IAC1C,2CAAA;IACE,aAAa,EAAE;IACf,UAAU,CAAC,MAAM;QACf,aAAa,EAAE;IACnB,CAAG,CAAC;AACJ;AAEA,wDAAA;AAeA;;;;;;;;CAQA,GACO,SAAS,IAAI,CAClB,EAAE,EACF,UAEI,CAAA,CAAE;IAER,uEAAA;IACA,0CAAA;IACA,EAAA;IACA,qCAAA;IACA,0CAAA;IACA,2CAAA;IAEE,SAAS,UAAU,CAAC,EAAE,EAA4B;QAChD,OAAO,OAAO,EAAA,KAAO,UAAU;IACnC;IAEE,IAAI,CAAC,UAAU,CAAC,EAAE,CAAC,EAAE;QACnB,OAAO,EAAE;IACb;IAEE,IAAI;QACN,uEAAA;QACA,wBAAA;QACI,MAAM,OAAA,GAAU,AAAC,EAAA,CAA0B,kBAAkB;QAC7D,IAAI,OAAO,EAAE;YACX,IAAI,OAAO,OAAA,KAAY,UAAU,EAAE;gBACjC,OAAO,OAAO;YACtB,OAAa;gBACb,4GAAA;gBACA,gGAAA;gBACQ,OAAO,EAAE;YACjB;QACA;QAEA,+BAAA;QACI,wKAAI,sBAAA,AAAmB,EAAC,EAAE,CAAC,EAAE;YAC3B,OAAO,EAAE;QACf;IACA,EAAI,OAAM;QACV,4DAAA;QACA,gEAAA;QACA,6EAAA;QACI,OAAO,EAAE;IACb;IAEA,2BAAA;IACA,kGAAA;IACE,MAAM,gBAAgB,SAAyB,GAAG,IAAI,EAAsB;QAC1E,IAAI;YACR,oDAAA;YACM,MAAM,gBAAA,GAAmB,IAAI,CAAC,GAAG,CAAC,GAAA,IAAO,IAAI,CAAC,GAAG,EAAE,OAAO,CAAC,CAAC;YAElE,uCAAA;YACA,0EAAA;YACA,6FAAA;YACA,iFAAA;YACM,OAAO,EAAE,CAAC,KAAK,CAAC,IAAI,EAAE,gBAAgB,CAAC;QAC7C,CAAI,CAAE,OAAO,EAAE,EAAE;YACX,iBAAiB,EAAE;aAEnB,6KAAA,AAAS,GAAC,KAAA,IAAS;gBACjB,KAAK,CAAC,iBAAiB,EAAC,SAAS;oBAC/B,IAAI,OAAO,CAAC,SAAS,EAAE;0LACrB,wBAAA,AAAqB,EAAC,KAAK,EAAE,SAAS,EAAE,SAAS,CAAC;0LAClD,wBAAA,AAAqB,EAAC,KAAK,EAAE,OAAO,CAAC,SAAS,CAAC;oBAC3D;oBAEU,KAAK,CAAC,KAAA,GAAQ;wBACZ,GAAG,KAAK,CAAC,KAAK;wBACd,SAAS,EAAE,IAAI;oBAC3B,CAAW;oBAED,OAAO,KAAK;gBACtB,CAAS,CAAC;4KAEF,mBAAA,AAAgB,EAAC,EAAE,CAAC;YAC5B,CAAO,CAAC;YAEF,MAAM,EAAE;QACd;IACA,CAAE;IAEF,iHAAA;IACE,IAAI;QACF,IAAK,MAAM,QAAA,IAAY,EAAE,CAAE;YACzB,IAAI,MAAM,CAAC,SAAS,CAAC,cAAc,CAAC,IAAI,CAAC,EAAE,EAAE,QAAQ,CAAC,EAAE;gBACtD,aAAa,CAAC,QAAA,CAAS,GAAc,EAAE,CAAC,SAAoB;YACpE;QACA;IACA,EAAI,OAAM;IACV,mCAAA;IACA,kEAAA;IACA;IAEA,4DAAA;IACA,qEAAA;wKACE,sBAAA,AAAmB,EAAC,aAAa,EAAE,EAAE,CAAC;wKAEtC,2BAAA,AAAwB,EAAC,EAAE,EAAE,oBAAoB,EAAE,aAAa,CAAC;IAEnE,+DAAA;IACE,IAAI;QACN,oEAAA;QACI,MAAM,UAAA,GAAa,MAAM,CAAC,wBAAwB,CAAC,aAAa,EAAE,MAAM,CAAC;QACzE,IAAI,UAAU,CAAC,YAAY,EAAE;YAC3B,MAAM,CAAC,cAAc,CAAC,aAAa,EAAE,MAAM,EAAE;gBAC3C,GAAG,GAAW;oBACZ,OAAO,EAAE,CAAC,IAAI;gBACxB,CAAS;YACT,CAAO,CAAC;QACR;IACA,EAAI,OAAM;IACV,uGAAA;IACA,8CAAA;IACA;IAEE,OAAO,aAAa;AACtB;AAEA;;CAEA,GACO,SAAS,kBAAkB,GAAqD;IACvF,sDAAA;IACE,MAAM,GAAA,wKAAM,kBAAA,AAAe,EAAE;IAC7B,MAAM,EAAE,QAAA,EAAS,GAAI,MAAM,CAAC,QAAA,IAAY,CAAA,CAAE;IAC1C,MAAM,EAAE,SAAA,EAAU,GAAI,MAAM,CAAC,SAAA,IAAa,CAAA,CAAE;IAE5C,MAAM,UAAU;QACd,GAAI,QAAA,IAAY;YAAE,OAAO,EAAE,QAAA;QAAA,CAAU,CAAC;QACtC,GAAI,SAAA,IAAa;YAAE,YAAY,EAAE,SAAA;QAAA,CAAW,CAAC;IACjD,CAAG;IACD,MAAM,UAAU;QACd,GAAG;QACH,OAAO;IACX,CAAG;IAED,OAAO,OAAO;AAChB", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 509, "column": 0}, "map": {"version": 3, "file": "client.js", "sources": ["file:///C:/Users/<USER>/Documents/File%20Kerja%20Daffa/GAWEAN/TERANG_LMS/terang-lms-frontend/node_modules/%40sentry/browser/src/client.ts"], "sourcesContent": ["import type {\n  BrowserClientProfilingOptions,\n  BrowserClientReplayOptions,\n  ClientOptions,\n  Event,\n  EventHint,\n  Options,\n  ParameterizedString,\n  Scope,\n  SeverityLevel,\n} from '@sentry/core';\nimport {\n  _INTERNAL_flushLogsBuffer,\n  addAutoIpAddressToSession,\n  addAutoIpAddressToUser,\n  applySdkMetadata,\n  Client,\n  getSDKSource,\n} from '@sentry/core';\nimport { eventFromException, eventFromMessage } from './eventbuilder';\nimport { WINDOW } from './helpers';\nimport type { BrowserTransportOptions } from './transports/types';\n\n/**\n * A magic string that build tooling can leverage in order to inject a release value into the SDK.\n */\ndeclare const __SENTRY_RELEASE__: string | undefined;\n\nconst DEFAULT_FLUSH_INTERVAL = 5000;\n\ntype BrowserSpecificOptions = BrowserClientReplayOptions &\n  BrowserClientProfilingOptions & {\n    /** If configured, this URL will be used as base URL for lazy loading integration. */\n    cdnBaseUrl?: string;\n  };\n/**\n * Configuration options for the Sentry Browser SDK.\n * @see @sentry/core Options for more information.\n */\nexport type BrowserOptions = Options<BrowserTransportOptions> &\n  BrowserSpecificOptions & {\n    /**\n     * Important: Only set this option if you know what you are doing!\n     *\n     * By default, the SDK will check if `Sentry.init` is called in a browser extension.\n     * In case it is, it will stop initialization and log a warning\n     * because browser extensions require a different Sentry initialization process:\n     * https://docs.sentry.io/platforms/javascript/best-practices/shared-environments/\n     *\n     * Setting up the SDK in a browser extension with global error monitoring is not recommended\n     * and will likely flood you with errors from other web sites or extensions. This can heavily\n     * impact your quota and cause interference with your and other Sentry SDKs in shared environments.\n     *\n     * If this check wrongfully flags your setup as a browser extension, you can set this\n     * option to `true` to skip the check.\n     *\n     * @default false\n     */\n    skipBrowserExtensionCheck?: boolean;\n  };\n\n/**\n * Configuration options for the Sentry Browser SDK Client class\n * @see BrowserClient for more information.\n */\nexport type BrowserClientOptions = ClientOptions<BrowserTransportOptions> & BrowserSpecificOptions;\n\n/**\n * The Sentry Browser SDK Client.\n *\n * @see BrowserOptions for documentation on configuration options.\n * @see SentryClient for usage documentation.\n */\nexport class BrowserClient extends Client<BrowserClientOptions> {\n  private _logFlushIdleTimeout: ReturnType<typeof setTimeout> | undefined;\n  /**\n   * Creates a new Browser SDK instance.\n   *\n   * @param options Configuration options for this SDK.\n   */\n  public constructor(options: BrowserClientOptions) {\n    const opts = applyDefaultOptions(options);\n    const sdkSource = WINDOW.SENTRY_SDK_SOURCE || getSDKSource();\n    applySdkMetadata(opts, 'browser', ['browser'], sdkSource);\n\n    super(opts);\n\n    const { sendDefaultPii, sendClientReports, enableLogs, _experiments } = this._options;\n    // eslint-disable-next-line deprecation/deprecation\n    const shouldEnableLogs = enableLogs ?? _experiments?.enableLogs;\n\n    if (WINDOW.document && (sendClientReports || shouldEnableLogs)) {\n      WINDOW.document.addEventListener('visibilitychange', () => {\n        if (WINDOW.document.visibilityState === 'hidden') {\n          if (sendClientReports) {\n            this._flushOutcomes();\n          }\n          if (shouldEnableLogs) {\n            _INTERNAL_flushLogsBuffer(this);\n          }\n        }\n      });\n    }\n\n    if (shouldEnableLogs) {\n      this.on('flush', () => {\n        _INTERNAL_flushLogsBuffer(this);\n      });\n\n      this.on('afterCaptureLog', () => {\n        if (this._logFlushIdleTimeout) {\n          clearTimeout(this._logFlushIdleTimeout);\n        }\n\n        this._logFlushIdleTimeout = setTimeout(() => {\n          _INTERNAL_flushLogsBuffer(this);\n        }, DEFAULT_FLUSH_INTERVAL);\n      });\n    }\n\n    if (sendDefaultPii) {\n      this.on('postprocessEvent', addAutoIpAddressToUser);\n      this.on('beforeSendSession', addAutoIpAddressToSession);\n    }\n  }\n\n  /**\n   * @inheritDoc\n   */\n  public eventFromException(exception: unknown, hint?: EventHint): PromiseLike<Event> {\n    return eventFromException(this._options.stackParser, exception, hint, this._options.attachStacktrace);\n  }\n\n  /**\n   * @inheritDoc\n   */\n  public eventFromMessage(\n    message: ParameterizedString,\n    level: SeverityLevel = 'info',\n    hint?: EventHint,\n  ): PromiseLike<Event> {\n    return eventFromMessage(this._options.stackParser, message, level, hint, this._options.attachStacktrace);\n  }\n\n  /**\n   * @inheritDoc\n   */\n  protected _prepareEvent(\n    event: Event,\n    hint: EventHint,\n    currentScope: Scope,\n    isolationScope: Scope,\n  ): PromiseLike<Event | null> {\n    event.platform = event.platform || 'javascript';\n\n    return super._prepareEvent(event, hint, currentScope, isolationScope);\n  }\n}\n\n/** Exported only for tests. */\nexport function applyDefaultOptions<T extends Partial<BrowserClientOptions>>(optionsArg: T): T {\n  return {\n    release:\n      typeof __SENTRY_RELEASE__ === 'string' // This allows build tooling to find-and-replace __SENTRY_RELEASE__ to inject a release value\n        ? __SENTRY_RELEASE__\n        : WINDOW.SENTRY_RELEASE?.id, // This supports the variable that sentry-webpack-plugin injects\n    sendClientReports: true,\n    // We default this to true, as it is the safer scenario\n    parentSpanIsAlwaysRootSpan: true,\n    ...optionsArg,\n  };\n}\n"], "names": [], "mappings": ";;;;;;;;;;;;;;AAuBA;;CAEA,GAGA,MAAM,sBAAA,GAAyB,IAAI;AAuCnC;;;;;CAKA,GACO,MAAM,aAAA,gKAAsB,SAAM,CAAuB;IAEhE;;;;GAIA,GACS,WAAW,CAAC,OAAO,CAAwB;QAChD,MAAM,IAAA,GAAO,mBAAmB,CAAC,OAAO,CAAC;QACzC,MAAM,8KAAY,SAAM,CAAC,iBAAA,qKAAqB,eAAA,AAAY,EAAE;iLAC5D,mBAAA,AAAgB,EAAC,IAAI,EAAE,SAAS,EAAE;YAAC,SAAS;SAAC,EAAE,SAAS,CAAC;QAEzD,KAAK,CAAC,IAAI,CAAC;QAEX,MAAM,EAAE,cAAc,EAAE,iBAAiB,EAAE,UAAU,EAAE,YAAA,EAAa,GAAI,IAAI,CAAC,QAAQ;QACzF,mDAAA;QACI,MAAM,gBAAA,GAAmB,cAAc,YAAY,EAAE,UAAU;QAE/D,sKAAI,SAAM,CAAC,QAAA,IAAA,CAAa,iBAAA,IAAqB,gBAAgB,CAAC,EAAE;8KAC9D,SAAM,CAAC,QAAQ,CAAC,gBAAgB,CAAC,kBAAkB,EAAE,MAAM;gBACzD,sKAAI,SAAM,CAAC,QAAQ,CAAC,eAAA,KAAoB,QAAQ,EAAE;oBAChD,IAAI,iBAAiB,EAAE;wBACrB,IAAI,CAAC,cAAc,EAAE;oBACjC;oBACU,IAAI,gBAAgB,EAAE;4LACpB,4BAAA,AAAyB,EAAC,IAAI,CAAC;oBAC3C;gBACA;YACA,CAAO,CAAC;QACR;QAEI,IAAI,gBAAgB,EAAE;YACpB,IAAI,CAAC,EAAE,CAAC,OAAO,EAAE,MAAM;oLACrB,4BAAA,AAAyB,EAAC,IAAI,CAAC;YACvC,CAAO,CAAC;YAEF,IAAI,CAAC,EAAE,CAAC,iBAAiB,EAAE,MAAM;gBAC/B,IAAI,IAAI,CAAC,oBAAoB,EAAE;oBAC7B,YAAY,CAAC,IAAI,CAAC,oBAAoB,CAAC;gBACjD;gBAEQ,IAAI,CAAC,oBAAA,GAAuB,UAAU,CAAC,MAAM;wLAC3C,4BAAA,AAAyB,EAAC,IAAI,CAAC;gBACzC,CAAS,EAAE,sBAAsB,CAAC;YAClC,CAAO,CAAC;QACR;QAEI,IAAI,cAAc,EAAE;YAClB,IAAI,CAAC,EAAE,CAAC,kBAAkB,qKAAE,yBAAsB,CAAC;YACnD,IAAI,CAAC,EAAE,CAAC,mBAAmB,EAAE,+LAAyB,CAAC;QAC7D;IACA;IAEA;;GAEA,GACS,kBAAkB,CAAC,SAAS,EAAW,IAAI,EAAkC;QAClF,kLAAO,qBAAA,AAAkB,EAAC,IAAI,CAAC,QAAQ,CAAC,WAAW,EAAE,SAAS,EAAE,IAAI,EAAE,IAAI,CAAC,QAAQ,CAAC,gBAAgB,CAAC;IACzG;IAEA;;GAEA,GACS,gBAAgB,CACrB,OAAO,EACP,KAAK,GAAkB,MAAM,EAC7B,IAAI,EACgB;QACpB,kLAAO,mBAAA,AAAgB,EAAC,IAAI,CAAC,QAAQ,CAAC,WAAW,EAAE,OAAO,EAAE,KAAK,EAAE,IAAI,EAAE,IAAI,CAAC,QAAQ,CAAC,gBAAgB,CAAC;IAC5G;IAEA;;GAEA,GACY,aAAa,CACrB,KAAK,EACL,IAAI,EACJ,YAAY,EACZ,cAAc,EACa;QAC3B,KAAK,CAAC,QAAA,GAAW,KAAK,CAAC,QAAA,IAAY,YAAY;QAE/C,OAAO,KAAK,CAAC,aAAa,CAAC,KAAK,EAAE,IAAI,EAAE,YAAY,EAAE,cAAc,CAAC;IACzE;AACA;AAEA,6BAAA,GACO,SAAS,mBAAmB,CAA0C,UAAU,EAAQ;IAC7F,OAAO;QACL,OAAO,EACL,OAAO,uBAAuB,QAAA,CAAA,6FAAA;WAC1B,qBACA,2KAAM,CAAC,cAAc,EAAE,EAAE;QAC/B,iBAAiB,EAAE,IAAI;QAC3B,uDAAA;QACI,0BAA0B,EAAE,IAAI;QAChC,GAAG,UAAU;IACjB,CAAG;AACH", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 611, "column": 0}, "map": {"version": 3, "file": "debug-build.js", "sources": ["file:///C:/Users/<USER>/Documents/File%20Kerja%20Daffa/GAWEAN/TERANG_LMS/terang-lms-frontend/node_modules/%40sentry/browser/src/debug-build.ts"], "sourcesContent": ["declare const __DEBUG_BUILD__: boolean;\n\n/**\n * This serves as a build time flag that will be true by default, but false in non-debug builds or if users replace `__SENTRY_DEBUG__` in their generated code.\n *\n * ATTENTION: This constant must never cross package boundaries (i.e. be exported) to guarantee that it can be used for tree shaking.\n */\nexport const DEBUG_BUILD = __DEBUG_BUILD__;\n"], "names": [], "mappings": "AAEA;;;;CAIA;;;AACO,MAAM,WAAA,GAAc,OAAA,gBAAA,KAAA,WAAA,IAAA,gBAAA", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 627, "column": 0}, "map": {"version": 3, "file": "breadcrumbs.js", "sources": ["file:///C:/Users/<USER>/Documents/File%20Kerja%20Daffa/GAWEAN/TERANG_LMS/terang-lms-frontend/node_modules/%40sentry/browser/src/integrations/breadcrumbs.ts"], "sourcesContent": ["/* eslint-disable max-lines */\n\nimport type {\n  Breadcrumb,\n  Client,\n  Event as SentryEvent,\n  FetchBreadcrumbData,\n  FetchBreadcrumbHint,\n  HandlerDataConsole,\n  HandlerDataDom,\n  HandlerDataFetch,\n  HandlerDataHistory,\n  HandlerDataXhr,\n  IntegrationFn,\n  XhrBreadcrumbData,\n  XhrBreadcrumbHint,\n} from '@sentry/core';\nimport {\n  addBreadcrumb,\n  addConsoleInstrumentationHandler,\n  addFetchInstrumentationHandler,\n  debug,\n  defineIntegration,\n  getBreadcrumbLogLevelFromHttpStatusCode,\n  getClient,\n  getComponentName,\n  getEventDescription,\n  htmlTreeAsString,\n  parseUrl,\n  safeJoin,\n  severityLevelFromString,\n} from '@sentry/core';\nimport type { FetchHint, XhrHint } from '@sentry-internal/browser-utils';\nimport {\n  addClickKeypressInstrumentationHandler,\n  addHistoryInstrumentationHandler,\n  addXhrInstrumentationHand<PERSON>,\n  SENTRY_XHR_DATA_KEY,\n} from '@sentry-internal/browser-utils';\nimport { DEBUG_BUILD } from '../debug-build';\nimport { WINDOW } from '../helpers';\n\ninterface BreadcrumbsOptions {\n  console: boolean;\n  dom:\n    | boolean\n    | {\n        serializeAttribute?: string | string[];\n        maxStringLength?: number;\n      };\n  fetch: boolean;\n  history: boolean;\n  sentry: boolean;\n  xhr: boolean;\n}\n\n/** maxStringLength gets capped to prevent 100 breadcrumbs exceeding 1MB event payload size */\nconst MAX_ALLOWED_STRING_LENGTH = 1024;\n\nconst INTEGRATION_NAME = 'Breadcrumbs';\n\nconst _breadcrumbsIntegration = ((options: Partial<BreadcrumbsOptions> = {}) => {\n  const _options = {\n    console: true,\n    dom: true,\n    fetch: true,\n    history: true,\n    sentry: true,\n    xhr: true,\n    ...options,\n  };\n\n  return {\n    name: INTEGRATION_NAME,\n    setup(client) {\n      // TODO(v10): Remove this functionality and use `consoleIntegration` from @sentry/core instead.\n      if (_options.console) {\n        addConsoleInstrumentationHandler(_getConsoleBreadcrumbHandler(client));\n      }\n      if (_options.dom) {\n        addClickKeypressInstrumentationHandler(_getDomBreadcrumbHandler(client, _options.dom));\n      }\n      if (_options.xhr) {\n        addXhrInstrumentationHandler(_getXhrBreadcrumbHandler(client));\n      }\n      if (_options.fetch) {\n        addFetchInstrumentationHandler(_getFetchBreadcrumbHandler(client));\n      }\n      if (_options.history) {\n        addHistoryInstrumentationHandler(_getHistoryBreadcrumbHandler(client));\n      }\n      if (_options.sentry) {\n        client.on('beforeSendEvent', _getSentryBreadcrumbHandler(client));\n      }\n    },\n  };\n}) satisfies IntegrationFn;\n\nexport const breadcrumbsIntegration = defineIntegration(_breadcrumbsIntegration);\n\n/**\n * Adds a breadcrumb for Sentry events or transactions if this option is enabled.\n */\nfunction _getSentryBreadcrumbHandler(client: Client): (event: SentryEvent) => void {\n  return function addSentryBreadcrumb(event: SentryEvent): void {\n    if (getClient() !== client) {\n      return;\n    }\n\n    addBreadcrumb(\n      {\n        category: `sentry.${event.type === 'transaction' ? 'transaction' : 'event'}`,\n        event_id: event.event_id,\n        level: event.level,\n        message: getEventDescription(event),\n      },\n      {\n        event,\n      },\n    );\n  };\n}\n\n/**\n * A HOC that creates a function that creates breadcrumbs from DOM API calls.\n * This is a HOC so that we get access to dom options in the closure.\n */\nfunction _getDomBreadcrumbHandler(\n  client: Client,\n  dom: BreadcrumbsOptions['dom'],\n): (handlerData: HandlerDataDom) => void {\n  return function _innerDomBreadcrumb(handlerData: HandlerDataDom): void {\n    if (getClient() !== client) {\n      return;\n    }\n\n    let target;\n    let componentName;\n    let keyAttrs = typeof dom === 'object' ? dom.serializeAttribute : undefined;\n\n    let maxStringLength =\n      typeof dom === 'object' && typeof dom.maxStringLength === 'number' ? dom.maxStringLength : undefined;\n    if (maxStringLength && maxStringLength > MAX_ALLOWED_STRING_LENGTH) {\n      DEBUG_BUILD &&\n        debug.warn(\n          `\\`dom.maxStringLength\\` cannot exceed ${MAX_ALLOWED_STRING_LENGTH}, but a value of ${maxStringLength} was configured. Sentry will use ${MAX_ALLOWED_STRING_LENGTH} instead.`,\n        );\n      maxStringLength = MAX_ALLOWED_STRING_LENGTH;\n    }\n\n    if (typeof keyAttrs === 'string') {\n      keyAttrs = [keyAttrs];\n    }\n\n    // Accessing event.target can throw (see getsentry/raven-js#838, #768)\n    try {\n      const event = handlerData.event as Event | Node;\n      const element = _isEvent(event) ? event.target : event;\n\n      target = htmlTreeAsString(element, { keyAttrs, maxStringLength });\n      componentName = getComponentName(element);\n    } catch {\n      target = '<unknown>';\n    }\n\n    if (target.length === 0) {\n      return;\n    }\n\n    const breadcrumb: Breadcrumb = {\n      category: `ui.${handlerData.name}`,\n      message: target,\n    };\n\n    if (componentName) {\n      breadcrumb.data = { 'ui.component_name': componentName };\n    }\n\n    addBreadcrumb(breadcrumb, {\n      event: handlerData.event,\n      name: handlerData.name,\n      global: handlerData.global,\n    });\n  };\n}\n\n/**\n * Creates breadcrumbs from console API calls\n */\nfunction _getConsoleBreadcrumbHandler(client: Client): (handlerData: HandlerDataConsole) => void {\n  return function _consoleBreadcrumb(handlerData: HandlerDataConsole): void {\n    if (getClient() !== client) {\n      return;\n    }\n\n    const breadcrumb = {\n      category: 'console',\n      data: {\n        arguments: handlerData.args,\n        logger: 'console',\n      },\n      level: severityLevelFromString(handlerData.level),\n      message: safeJoin(handlerData.args, ' '),\n    };\n\n    if (handlerData.level === 'assert') {\n      if (handlerData.args[0] === false) {\n        breadcrumb.message = `Assertion failed: ${safeJoin(handlerData.args.slice(1), ' ') || 'console.assert'}`;\n        breadcrumb.data.arguments = handlerData.args.slice(1);\n      } else {\n        // Don't capture a breadcrumb for passed assertions\n        return;\n      }\n    }\n\n    addBreadcrumb(breadcrumb, {\n      input: handlerData.args,\n      level: handlerData.level,\n    });\n  };\n}\n\n/**\n * Creates breadcrumbs from XHR API calls\n */\nfunction _getXhrBreadcrumbHandler(client: Client): (handlerData: HandlerDataXhr) => void {\n  return function _xhrBreadcrumb(handlerData: HandlerDataXhr): void {\n    if (getClient() !== client) {\n      return;\n    }\n\n    const { startTimestamp, endTimestamp } = handlerData;\n\n    const sentryXhrData = handlerData.xhr[SENTRY_XHR_DATA_KEY];\n\n    // We only capture complete, non-sentry requests\n    if (!startTimestamp || !endTimestamp || !sentryXhrData) {\n      return;\n    }\n\n    const { method, url, status_code, body } = sentryXhrData;\n\n    const data: XhrBreadcrumbData = {\n      method,\n      url,\n      status_code,\n    };\n\n    const hint: XhrBreadcrumbHint = {\n      xhr: handlerData.xhr,\n      input: body,\n      startTimestamp,\n      endTimestamp,\n    };\n\n    const breadcrumb = {\n      category: 'xhr',\n      data,\n      type: 'http',\n      level: getBreadcrumbLogLevelFromHttpStatusCode(status_code),\n    };\n\n    client.emit('beforeOutgoingRequestBreadcrumb', breadcrumb, hint as XhrHint);\n\n    addBreadcrumb(breadcrumb, hint);\n  };\n}\n\n/**\n * Creates breadcrumbs from fetch API calls\n */\nfunction _getFetchBreadcrumbHandler(client: Client): (handlerData: HandlerDataFetch) => void {\n  return function _fetchBreadcrumb(handlerData: HandlerDataFetch): void {\n    if (getClient() !== client) {\n      return;\n    }\n\n    const { startTimestamp, endTimestamp } = handlerData;\n\n    // We only capture complete fetch requests\n    if (!endTimestamp) {\n      return;\n    }\n\n    if (handlerData.fetchData.url.match(/sentry_key/) && handlerData.fetchData.method === 'POST') {\n      // We will not create breadcrumbs for fetch requests that contain `sentry_key` (internal sentry requests)\n      return;\n    }\n\n    const breadcrumbData: FetchBreadcrumbData = {\n      method: handlerData.fetchData.method,\n      url: handlerData.fetchData.url,\n    };\n\n    if (handlerData.error) {\n      const data: FetchBreadcrumbData = handlerData.fetchData;\n      const hint: FetchBreadcrumbHint = {\n        data: handlerData.error,\n        input: handlerData.args,\n        startTimestamp,\n        endTimestamp,\n      };\n\n      const breadcrumb = {\n        category: 'fetch',\n        data,\n        level: 'error',\n        type: 'http',\n      } satisfies Breadcrumb;\n\n      client.emit('beforeOutgoingRequestBreadcrumb', breadcrumb, hint as FetchHint);\n\n      addBreadcrumb(breadcrumb, hint);\n    } else {\n      const response = handlerData.response as Response | undefined;\n      const data: FetchBreadcrumbData = {\n        ...handlerData.fetchData,\n        status_code: response?.status,\n      };\n\n      breadcrumbData.request_body_size = handlerData.fetchData.request_body_size;\n      breadcrumbData.response_body_size = handlerData.fetchData.response_body_size;\n      breadcrumbData.status_code = response?.status;\n\n      const hint: FetchBreadcrumbHint = {\n        input: handlerData.args,\n        response,\n        startTimestamp,\n        endTimestamp,\n      };\n\n      const breadcrumb = {\n        category: 'fetch',\n        data,\n        type: 'http',\n        level: getBreadcrumbLogLevelFromHttpStatusCode(data.status_code),\n      };\n\n      client.emit('beforeOutgoingRequestBreadcrumb', breadcrumb, hint as FetchHint);\n\n      addBreadcrumb(breadcrumb, hint);\n    }\n  };\n}\n\n/**\n * Creates breadcrumbs from history API calls\n */\nfunction _getHistoryBreadcrumbHandler(client: Client): (handlerData: HandlerDataHistory) => void {\n  return function _historyBreadcrumb(handlerData: HandlerDataHistory): void {\n    if (getClient() !== client) {\n      return;\n    }\n\n    let from: string | undefined = handlerData.from;\n    let to: string | undefined = handlerData.to;\n    const parsedLoc = parseUrl(WINDOW.location.href);\n    let parsedFrom = from ? parseUrl(from) : undefined;\n    const parsedTo = parseUrl(to);\n\n    // Initial pushState doesn't provide `from` information\n    if (!parsedFrom?.path) {\n      parsedFrom = parsedLoc;\n    }\n\n    // Use only the path component of the URL if the URL matches the current\n    // document (almost all the time when using pushState)\n    if (parsedLoc.protocol === parsedTo.protocol && parsedLoc.host === parsedTo.host) {\n      to = parsedTo.relative;\n    }\n    if (parsedLoc.protocol === parsedFrom.protocol && parsedLoc.host === parsedFrom.host) {\n      from = parsedFrom.relative;\n    }\n\n    addBreadcrumb({\n      category: 'navigation',\n      data: {\n        from,\n        to,\n      },\n    });\n  };\n}\n\nfunction _isEvent(event: unknown): event is Event {\n  return !!event && !!(event as Record<string, unknown>).target;\n}\n"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;AAwDA,4FAAA,GACA,MAAM,yBAAA,GAA4B,IAAI;AAEtC,MAAM,gBAAA,GAAmB,aAAa;AAEtC,MAAM,uBAAA,GAA2B,CAAC,OAAO,GAAgC,CAAA,CAAE,KAAK;IAC9E,MAAM,WAAW;QACf,OAAO,EAAE,IAAI;QACb,GAAG,EAAE,IAAI;QACT,KAAK,EAAE,IAAI;QACX,OAAO,EAAE,IAAI;QACb,MAAM,EAAE,IAAI;QACZ,GAAG,EAAE,IAAI;QACT,GAAG,OAAO;IACd,CAAG;IAED,OAAO;QACL,IAAI,EAAE,gBAAgB;QACtB,KAAK,EAAC,MAAM,EAAE;YAClB,+FAAA;YACM,IAAI,QAAQ,CAAC,OAAO,EAAE;gBACpB,6MAAA,AAAgC,EAAC,4BAA4B,CAAC,MAAM,CAAC,CAAC;YAC9E;YACM,IAAI,QAAQ,CAAC,GAAG,EAAE;8MAChB,yCAAA,AAAsC,EAAC,wBAAwB,CAAC,MAAM,EAAE,QAAQ,CAAC,GAAG,CAAC,CAAC;YAC9F;YACM,IAAI,QAAQ,CAAC,GAAG,EAAE;8MAChB,+BAAA,AAA4B,EAAC,wBAAwB,CAAC,MAAM,CAAC,CAAC;YACtE;YACM,IAAI,QAAQ,CAAC,KAAK,EAAE;wLAClB,iCAAA,AAA8B,EAAC,0BAA0B,CAAC,MAAM,CAAC,CAAC;YAC1E;YACM,IAAI,QAAQ,CAAC,OAAO,EAAE;kNACpB,mCAAA,AAAgC,EAAC,4BAA4B,CAAC,MAAM,CAAC,CAAC;YAC9E;YACM,IAAI,QAAQ,CAAC,MAAM,EAAE;gBACnB,MAAM,CAAC,EAAE,CAAC,iBAAiB,EAAE,2BAA2B,CAAC,MAAM,CAAC,CAAC;YACzE;QACA,CAAK;IACL,CAAG;AACH,CAAC,CAAA;MAEY,sBAAA,GAAyB,oLAAA,AAAiB,EAAC,uBAAuB;AAE/E;;CAEA,GACA,SAAS,2BAA2B,CAAC,MAAM,EAAwC;IACjF,OAAO,SAAS,mBAAmB,CAAC,KAAK,EAAqB;QAC5D,IAAI,8KAAA,AAAS,EAAC,MAAM,MAAM,EAAE;YAC1B;QACN;YAEI,4KAAA,AAAa,EACX;YACE,QAAQ,EAAE,CAAC,OAAO,EAAE,KAAK,CAAC,IAAA,KAAS,gBAAgB,aAAA,GAAgB,OAAO,CAAC,CAAA;YACA,QAAA,EAAA,KAAA,CAAA,QAAA;YACA,KAAA,EAAA,KAAA,CAAA,KAAA;YACA,OAAA,GAAA,uLAAA,EAAA,KAAA,CAAA;QACA,CAAA,EACA;YACA,KAAA;QACA,CAAA;IAEA,CAAA;AACA;AAEA;;;CAGA,GACA,SAAA,wBAAA,CACA,MAAA,EACA,GAAA;IAEA,OAAA,SAAA,mBAAA,CAAA,WAAA,EAAA;QACA,sKAAA,YAAA,EAAA,MAAA,MAAA,EAAA;YACA;QACA;QAEA,IAAA,MAAA;QACA,IAAA,aAAA;QACA,IAAA,QAAA,GAAA,OAAA,GAAA,KAAA,QAAA,GAAA,GAAA,CAAA,kBAAA,GAAA,SAAA;QAEA,IAAA,eAAA,GACA,OAAA,GAAA,KAAA,QAAA,IAAA,OAAA,GAAA,CAAA,eAAA,KAAA,QAAA,GAAA,GAAA,CAAA,eAAA,GAAA,SAAA;QACA,IAAA,eAAA,IAAA,eAAA,GAAA,yBAAA,EAAA;qLACA,cAAA,6KACA,QAAA,CAAA,IAAA,CACA,CAAA,sCAAA,EAAA,yBAAA,CAAA,iBAAA,EAAA,eAAA,CAAA,iCAAA,EAAA,yBAAA,CAAA,SAAA,CAAA;YAEA,eAAA,GAAA,yBAAA;QACA;QAEA,IAAA,OAAA,QAAA,KAAA,QAAA,EAAA;YACA,QAAA,GAAA;gBAAA,QAAA;aAAA;QACA;QAEA,sEAAA;QACA,IAAA;YACA,MAAA,KAAA,GAAA,WAAA,CAAA,KAAA;YACA,MAAA,OAAA,GAAA,QAAA,CAAA,KAAA,CAAA,GAAA,KAAA,CAAA,MAAA,GAAA,KAAA;YAEA,MAAA,GAAA,wLAAA,EAAA,OAAA,EAAA;gBAAA,QAAA;gBAAA,eAAA;YAAA,CAAA,CAAA;YACA,aAAA,wKAAA,mBAAA,EAAA,OAAA,CAAA;QACA,CAAA,CAAA,OAAA;YACA,MAAA,GAAA,WAAA;QACA;QAEA,IAAA,MAAA,CAAA,MAAA,KAAA,CAAA,EAAA;YACA;QACA;QAEA,MAAA,UAAA,GAAA;YACA,QAAA,EAAA,CAAA,GAAA,EAAA,WAAA,CAAA,IAAA,CAAA,CAAA;YACA,OAAA,EAAA,MAAA;QACA,CAAA;QAEA,IAAA,aAAA,EAAA;YACA,UAAA,CAAA,IAAA,GAAA;gBAAA,mBAAA,EAAA,aAAA;YAAA,CAAA;QACA;wKAEA,gBAAA,EAAA,UAAA,EAAA;YACA,KAAA,EAAA,WAAA,CAAA,KAAA;YACA,IAAA,EAAA,WAAA,CAAA,IAAA;YACA,MAAA,EAAA,WAAA,CAAA,MAAA;QACA,CAAA,CAAA;IACA,CAAA;AACA;AAEA;;CAEA,GACA,SAAA,4BAAA,CAAA,MAAA,EAAA;IACA,OAAA,SAAA,kBAAA,CAAA,WAAA,EAAA;QACA,sKAAA,YAAA,EAAA,MAAA,MAAA,EAAA;YACA;QACA;QAEA,MAAA,UAAA,GAAA;YACA,QAAA,EAAA,SAAA;YACA,IAAA,EAAA;gBACA,SAAA,EAAA,WAAA,CAAA,IAAA;gBACA,MAAA,EAAA,SAAA;YACA,CAAA;YACA,KAAA,wKAAA,0BAAA,EAAA,WAAA,CAAA,KAAA,CAAA;YACA,OAAA,GAAA,8KAAA,EAAA,WAAA,CAAA,IAAA,EAAA,GAAA,CAAA;QACA,CAAA;QAEA,IAAA,WAAA,CAAA,KAAA,KAAA,QAAA,EAAA;YACA,IAAA,WAAA,CAAA,IAAA,CAAA,CAAA,CAAA,KAAA,KAAA,EAAA;gBACA,UAAA,CAAA,OAAA,GAAA,CAAA,kBAAA,GAAA,8KAAA,EAAA,WAAA,CAAA,IAAA,CAAA,KAAA,CAAA,CAAA,CAAA,EAAA,GAAA,CAAA,IAAA,gBAAA,CAAA,CAAA;gBACA,UAAA,CAAA,IAAA,CAAA,SAAA,GAAA,WAAA,CAAA,IAAA,CAAA,KAAA,CAAA,CAAA,CAAA;YACA,CAAA,MAAA;gBACA,mDAAA;gBACA;YACA;QACA;wKAEA,gBAAA,EAAA,UAAA,EAAA;YACA,KAAA,EAAA,WAAA,CAAA,IAAA;YACA,KAAA,EAAA,WAAA,CAAA,KAAA;QACA,CAAA,CAAA;IACA,CAAA;AACA;AAEA;;CAEA,GACA,SAAA,wBAAA,CAAA,MAAA,EAAA;IACA,OAAA,SAAA,cAAA,CAAA,WAAA,EAAA;QACA,sKAAA,YAAA,EAAA,MAAA,MAAA,EAAA;YACA;QACA;QAEA,MAAA,EAAA,cAAA,EAAA,YAAA,EAAA,GAAA,WAAA;QAEA,MAAA,aAAA,GAAA,WAAA,CAAA,GAAA,CAAA,gNAAA,CAAA;QAEA,gDAAA;QACA,IAAA,CAAA,cAAA,IAAA,CAAA,YAAA,IAAA,CAAA,aAAA,EAAA;YACA;QACA;QAEA,MAAA,EAAA,MAAA,EAAA,GAAA,EAAA,WAAA,EAAA,IAAA,EAAA,GAAA,aAAA;QAEA,MAAA,IAAA,GAAA;YACA,MAAA;YACA,GAAA;YACA,WAAA;QACA,CAAA;QAEA,MAAA,IAAA,GAAA;YACA,GAAA,EAAA,WAAA,CAAA,GAAA;YACA,KAAA,EAAA,IAAA;YACA,cAAA;YACA,YAAA;QACA,CAAA;QAEA,MAAA,UAAA,GAAA;YACA,QAAA,EAAA,KAAA;YACA,IAAA;YACA,IAAA,EAAA,MAAA;YACA,KAAA,0LAAA,0CAAA,EAAA,WAAA,CAAA;QACA,CAAA;QAEA,MAAA,CAAA,IAAA,CAAA,iCAAA,EAAA,UAAA,EAAA,IAAA,EAAA;YAEA,4KAAA,EAAA,UAAA,EAAA,IAAA,CAAA;IACA,CAAA;AACA;AAEA;;CAEA,GACA,SAAA,0BAAA,CAAA,MAAA,EAAA;IACA,OAAA,SAAA,gBAAA,CAAA,WAAA,EAAA;QACA,sKAAA,YAAA,EAAA,MAAA,MAAA,EAAA;YACA;QACA;QAEA,MAAA,EAAA,cAAA,EAAA,YAAA,EAAA,GAAA,WAAA;QAEA,0CAAA;QACA,IAAA,CAAA,YAAA,EAAA;YACA;QACA;QAEA,IAAA,WAAA,CAAA,SAAA,CAAA,GAAA,CAAA,KAAA,CAAA,YAAA,CAAA,IAAA,WAAA,CAAA,SAAA,CAAA,MAAA,KAAA,MAAA,EAAA;YACA,yGAAA;YACA;QACA;QAEA,CAAA;YACA,MAAA,EAAA,WAAA,CAAA,SAAA,CAAA,MAAA;YACA,GAAA,EAAA,WAAA,CAAA,SAAA,CAAA,GAAA;QACA,CAAA;QAEA,IAAA,WAAA,CAAA,KAAA,EAAA;YACA,MAAA,IAAA,GAAA,WAAA,CAAA,SAAA;YACA,MAAA,IAAA,GAAA;gBACA,IAAA,EAAA,WAAA,CAAA,KAAA;gBACA,KAAA,EAAA,WAAA,CAAA,IAAA;gBACA,cAAA;gBACA,YAAA;YACA,CAAA;YAEA,MAAA,UAAA,GAAA;gBACA,QAAA,EAAA,OAAA;gBACA,IAAA;gBACA,KAAA,EAAA,OAAA;gBACA,IAAA,EAAA,MAAA;YACA,CAAA;YAEA,MAAA,CAAA,IAAA,CAAA,iCAAA,EAAA,UAAA,EAAA,IAAA,EAAA;4KAEA,gBAAA,EAAA,UAAA,EAAA,IAAA,CAAA;QACA,CAAA,MAAA;YACA,MAAA,QAAA,GAAA,WAAA,CAAA,QAAA;YACA,MAAA,IAAA,GAAA;gBACA,GAAA,WAAA,CAAA,SAAA;gBACA,WAAA,EAAA,QAAA,EAAA,MAAA;YACA,CAAA;YAEA,WAAA,CAAA,SAAA,CAAA,iBAAA;YACA,WAAA,CAAA,SAAA,CAAA,kBAAA;YACA,QAAA,EAAA,MAAA;YAEA,MAAA,IAAA,GAAA;gBACA,KAAA,EAAA,WAAA,CAAA,IAAA;gBACA,QAAA;gBACA,cAAA;gBACA,YAAA;YACA,CAAA;YAEA,MAAA,UAAA,GAAA;gBACA,QAAA,EAAA,OAAA;gBACA,IAAA;gBACA,IAAA,EAAA,MAAA;gBACA,KAAA,0LAAA,0CAAA,EAAA,IAAA,CAAA,WAAA,CAAA;YACA,CAAA;YAEA,MAAA,CAAA,IAAA,CAAA,iCAAA,EAAA,UAAA,EAAA,IAAA,EAAA;aAEA,+KAAA,EAAA,UAAA,EAAA,IAAA,CAAA;QACA;IACA,CAAA;AACA;AAEA;;CAEA,GACA,SAAA,4BAAA,CAAA,MAAA,EAAA;IACA,OAAA,SAAA,kBAAA,CAAA,WAAA,EAAA;QACA,sKAAA,YAAA,EAAA,MAAA,MAAA,EAAA;YACA;QACA;QAEA,IAAA,IAAA,GAAA,WAAA,CAAA,IAAA;QACA,IAAA,EAAA,GAAA,WAAA,CAAA,EAAA;QACA,MAAA,SAAA,oKAAA,WAAA,EAAA,2KAAA,CAAA,QAAA,CAAA,IAAA,CAAA;QACA,IAAA,UAAA,GAAA,IAAA,oKAAA,WAAA,EAAA,IAAA,CAAA,GAAA,SAAA;QACA,MAAA,QAAA,oKAAA,WAAA,EAAA,EAAA,CAAA;QAEA,uDAAA;QACA,IAAA,CAAA,UAAA,EAAA,IAAA,EAAA;YACA,UAAA,GAAA,SAAA;QACA;QAEA,wEAAA;QACA,sDAAA;QACA,IAAA,SAAA,CAAA,QAAA,KAAA,QAAA,CAAA,QAAA,IAAA,SAAA,CAAA,IAAA,KAAA,QAAA,CAAA,IAAA,EAAA;YACA,EAAA,GAAA,QAAA,CAAA,QAAA;QACA;QACA,IAAA,SAAA,CAAA,QAAA,KAAA,UAAA,CAAA,QAAA,IAAA,SAAA,CAAA,IAAA,KAAA,UAAA,CAAA,IAAA,EAAA;YACA,IAAA,GAAA,UAAA,CAAA,QAAA;QACA;wKAEA,gBAAA,EAAA;YACA,QAAA,EAAA,YAAA;YACA,IAAA,EAAA;gBACA,IAAA;gBACA,EAAA;YACA,CAAA;QACA,CAAA,CAAA;IACA,CAAA;AACA;AAEA,SAAA,QAAA,CAAA,KAAA,EAAA;IACA,OAAA,CAAA,CAAA,KAAA,IAAA,CAAA,CAAA,KAAA,CAAA,MAAA;AACA", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 930, "column": 0}, "map": {"version": 3, "file": "browserapierrors.js", "sources": ["file:///C:/Users/<USER>/Documents/File%20Kerja%20Daffa/GAWEAN/TERANG_LMS/terang-lms-frontend/node_modules/%40sentry/browser/src/integrations/browserapierrors.ts"], "sourcesContent": ["import type { IntegrationFn, WrappedFunction } from '@sentry/core';\nimport { defineIntegration, fill, getFunctionName, getOriginalFunction } from '@sentry/core';\nimport { WINDOW, wrap } from '../helpers';\n\nconst DEFAULT_EVENT_TARGET = [\n  'EventTarget',\n  'Window',\n  'Node',\n  'ApplicationCache',\n  'AudioTrackList',\n  'BroadcastChannel',\n  'ChannelMergerNode',\n  'CryptoOperation',\n  'EventSource',\n  'FileReader',\n  'HTMLUnknownElement',\n  'IDBDatabase',\n  'IDBRequest',\n  'IDBTransaction',\n  'KeyOperation',\n  'MediaController',\n  'MessagePort',\n  'ModalWindow',\n  'Notification',\n  'SVGElementInstance',\n  'Screen',\n  'SharedWorker',\n  'TextTrack',\n  'TextTrackCue',\n  'TextTrackList',\n  'WebSocket',\n  'WebSocketWorker',\n  'Worker',\n  'XMLHttpRequest',\n  'XMLHttpRequestEventTarget',\n  'XMLHttpRequestUpload',\n];\n\nconst INTEGRATION_NAME = 'BrowserApiErrors';\n\ntype XMLHttpRequestProp = 'onload' | 'onerror' | 'onprogress' | 'onreadystatechange';\n\ninterface BrowserApiErrorsOptions {\n  setTimeout: boolean;\n  setInterval: boolean;\n  requestAnimationFrame: boolean;\n  XMLHttpRequest: boolean;\n  eventTarget: boolean | string[];\n\n  /**\n   * If you experience issues with this integration causing double-invocations of event listeners,\n   * try setting this option to `true`. It will unregister the original callbacks from the event targets\n   * before adding the instrumented callback.\n   *\n   * @default false\n   */\n  unregisterOriginalCallbacks: boolean;\n}\n\nconst _browserApiErrorsIntegration = ((options: Partial<BrowserApiErrorsOptions> = {}) => {\n  const _options = {\n    XMLHttpRequest: true,\n    eventTarget: true,\n    requestAnimationFrame: true,\n    setInterval: true,\n    setTimeout: true,\n    unregisterOriginalCallbacks: false,\n    ...options,\n  };\n\n  return {\n    name: INTEGRATION_NAME,\n    // TODO: This currently only works for the first client this is setup\n    // We may want to adjust this to check for client etc.\n    setupOnce() {\n      if (_options.setTimeout) {\n        fill(WINDOW, 'setTimeout', _wrapTimeFunction);\n      }\n\n      if (_options.setInterval) {\n        fill(WINDOW, 'setInterval', _wrapTimeFunction);\n      }\n\n      if (_options.requestAnimationFrame) {\n        fill(WINDOW, 'requestAnimationFrame', _wrapRAF);\n      }\n\n      if (_options.XMLHttpRequest && 'XMLHttpRequest' in WINDOW) {\n        fill(XMLHttpRequest.prototype, 'send', _wrapXHR);\n      }\n\n      const eventTargetOption = _options.eventTarget;\n      if (eventTargetOption) {\n        const eventTarget = Array.isArray(eventTargetOption) ? eventTargetOption : DEFAULT_EVENT_TARGET;\n        eventTarget.forEach(target => _wrapEventTarget(target, _options));\n      }\n    },\n  };\n}) satisfies IntegrationFn;\n\n/**\n * Wrap timer functions and event targets to catch errors and provide better meta data.\n */\nexport const browserApiErrorsIntegration = defineIntegration(_browserApiErrorsIntegration);\n\nfunction _wrapTimeFunction(original: () => void): () => number {\n  return function (this: unknown, ...args: unknown[]): number {\n    const originalCallback = args[0];\n    args[0] = wrap(originalCallback, {\n      mechanism: {\n        data: { function: getFunctionName(original) },\n        handled: false,\n        type: 'instrument',\n      },\n    });\n    return original.apply(this, args);\n  };\n}\n\nfunction _wrapRAF(original: () => void): (callback: () => void) => unknown {\n  return function (this: unknown, callback: () => void): () => void {\n    return original.apply(this, [\n      wrap(callback, {\n        mechanism: {\n          data: {\n            function: 'requestAnimationFrame',\n            handler: getFunctionName(original),\n          },\n          handled: false,\n          type: 'instrument',\n        },\n      }),\n    ]);\n  };\n}\n\nfunction _wrapXHR(originalSend: () => void): () => void {\n  return function (this: XMLHttpRequest, ...args: unknown[]): void {\n    // eslint-disable-next-line @typescript-eslint/no-this-alias\n    const xhr = this;\n    const xmlHttpRequestProps: XMLHttpRequestProp[] = ['onload', 'onerror', 'onprogress', 'onreadystatechange'];\n\n    xmlHttpRequestProps.forEach(prop => {\n      if (prop in xhr && typeof xhr[prop] === 'function') {\n        fill(xhr, prop, function (original) {\n          const wrapOptions = {\n            mechanism: {\n              data: {\n                function: prop,\n                handler: getFunctionName(original),\n              },\n              handled: false,\n              type: 'instrument',\n            },\n          };\n\n          // If Instrument integration has been called before BrowserApiErrors, get the name of original function\n          const originalFunction = getOriginalFunction(original);\n          if (originalFunction) {\n            wrapOptions.mechanism.data.handler = getFunctionName(originalFunction);\n          }\n\n          // Otherwise wrap directly\n          return wrap(original, wrapOptions);\n        });\n      }\n    });\n\n    return originalSend.apply(this, args);\n  };\n}\n\nfunction _wrapEventTarget(target: string, integrationOptions: BrowserApiErrorsOptions): void {\n  const globalObject = WINDOW as unknown as Record<string, { prototype?: object }>;\n  const proto = globalObject[target]?.prototype;\n\n  // eslint-disable-next-line no-prototype-builtins\n  if (!proto?.hasOwnProperty?.('addEventListener')) {\n    return;\n  }\n\n  fill(proto, 'addEventListener', function (original: VoidFunction): (\n    ...args: Parameters<typeof WINDOW.addEventListener>\n  ) => ReturnType<typeof WINDOW.addEventListener> {\n    return function (this: unknown, eventName, fn, options): VoidFunction {\n      try {\n        if (isEventListenerObject(fn)) {\n          // ESlint disable explanation:\n          //  First, it is generally safe to call `wrap` with an unbound function. Furthermore, using `.bind()` would\n          //  introduce a bug here, because bind returns a new function that doesn't have our\n          //  flags(like __sentry_original__) attached. `wrap` checks for those flags to avoid unnecessary wrapping.\n          //  Without those flags, every call to addEventListener wraps the function again, causing a memory leak.\n          // eslint-disable-next-line @typescript-eslint/unbound-method\n          fn.handleEvent = wrap(fn.handleEvent, {\n            mechanism: {\n              data: {\n                function: 'handleEvent',\n                handler: getFunctionName(fn),\n                target,\n              },\n              handled: false,\n              type: 'instrument',\n            },\n          });\n        }\n      } catch {\n        // can sometimes get 'Permission denied to access property \"handle Event'\n      }\n\n      if (integrationOptions.unregisterOriginalCallbacks) {\n        unregisterOriginalCallback(this, eventName, fn);\n      }\n\n      return original.apply(this, [\n        eventName,\n        wrap(fn, {\n          mechanism: {\n            data: {\n              function: 'addEventListener',\n              handler: getFunctionName(fn),\n              target,\n            },\n            handled: false,\n            type: 'instrument',\n          },\n        }),\n        options,\n      ]);\n    };\n  });\n\n  fill(proto, 'removeEventListener', function (originalRemoveEventListener: VoidFunction): (\n    this: unknown,\n    ...args: Parameters<typeof WINDOW.removeEventListener>\n  ) => ReturnType<typeof WINDOW.removeEventListener> {\n    return function (this: unknown, eventName, fn, options): VoidFunction {\n      /**\n       * There are 2 possible scenarios here:\n       *\n       * 1. Someone passes a callback, which was attached prior to Sentry initialization, or by using unmodified\n       * method, eg. `document.addEventListener.call(el, name, handler). In this case, we treat this function\n       * as a pass-through, and call original `removeEventListener` with it.\n       *\n       * 2. Someone passes a callback, which was attached after Sentry was initialized, which means that it was using\n       * our wrapped version of `addEventListener`, which internally calls `wrap` helper.\n       * This helper \"wraps\" whole callback inside a try/catch statement, and attached appropriate metadata to it,\n       * in order for us to make a distinction between wrapped/non-wrapped functions possible.\n       * If a function was wrapped, it has additional property of `__sentry_wrapped__`, holding the handler.\n       *\n       * When someone adds a handler prior to initialization, and then do it again, but after,\n       * then we have to detach both of them. Otherwise, if we'd detach only wrapped one, it'd be impossible\n       * to get rid of the initial handler and it'd stick there forever.\n       */\n      try {\n        const originalEventHandler = (fn as WrappedFunction).__sentry_wrapped__;\n        if (originalEventHandler) {\n          originalRemoveEventListener.call(this, eventName, originalEventHandler, options);\n        }\n      } catch {\n        // ignore, accessing __sentry_wrapped__ will throw in some Selenium environments\n      }\n      return originalRemoveEventListener.call(this, eventName, fn, options);\n    };\n  });\n}\n\nfunction isEventListenerObject(obj: unknown): obj is EventListenerObject {\n  return typeof (obj as EventListenerObject).handleEvent === 'function';\n}\n\nfunction unregisterOriginalCallback(target: unknown, eventName: string, fn: EventListenerOrEventListenerObject): void {\n  if (\n    target &&\n    typeof target === 'object' &&\n    'removeEventListener' in target &&\n    typeof target.removeEventListener === 'function'\n  ) {\n    target.removeEventListener(eventName, fn);\n  }\n}\n"], "names": [], "mappings": ";;;;;;;;;AAIA,MAAM,uBAAuB;IAC3B,aAAa;IACb,QAAQ;IACR,MAAM;IACN,kBAAkB;IAClB,gBAAgB;IAChB,kBAAkB;IAClB,mBAAmB;IACnB,iBAAiB;IACjB,aAAa;IACb,YAAY;IACZ,oBAAoB;IACpB,aAAa;IACb,YAAY;IACZ,gBAAgB;IAChB,cAAc;IACd,iBAAiB;IACjB,aAAa;IACb,aAAa;IACb,cAAc;IACd,oBAAoB;IACpB,QAAQ;IACR,cAAc;IACd,WAAW;IACX,cAAc;IACd,eAAe;IACf,WAAW;IACX,iBAAiB;IACjB,QAAQ;IACR,gBAAgB;IAChB,2BAA2B;IAC3B,sBAAsB;CACvB;AAED,MAAM,gBAAA,GAAmB,kBAAkB;AAqB3C,MAAM,4BAAA,GAAgC,CAAC,OAAO,GAAqC,CAAA,CAAE,KAAK;IACxF,MAAM,WAAW;QACf,cAAc,EAAE,IAAI;QACpB,WAAW,EAAE,IAAI;QACjB,qBAAqB,EAAE,IAAI;QAC3B,WAAW,EAAE,IAAI;QACjB,UAAU,EAAE,IAAI;QAChB,2BAA2B,EAAE,KAAK;QAClC,GAAG,OAAO;IACd,CAAG;IAED,OAAO;QACL,IAAI,EAAE,gBAAgB;QAC1B,qEAAA;QACA,sDAAA;QACI,SAAS,GAAG;YACV,IAAI,QAAQ,CAAC,UAAU,EAAE;oLACvB,OAAA,AAAI,oKAAC,SAAM,EAAE,YAAY,EAAE,iBAAiB,CAAC;YACrD;YAEM,IAAI,QAAQ,CAAC,WAAW,EAAE;oLACxB,OAAA,AAAI,oKAAC,SAAM,EAAE,aAAa,EAAE,iBAAiB,CAAC;YACtD;YAEM,IAAI,QAAQ,CAAC,qBAAqB,EAAE;gBAClC,2KAAA,AAAI,oKAAC,SAAM,EAAE,uBAAuB,EAAE,QAAQ,CAAC;YACvD;YAEM,IAAI,QAAQ,CAAC,cAAA,IAAkB,gBAAA,sKAAoB,SAAM,EAAE;mLACzD,QAAA,AAAI,EAAC,cAAc,CAAC,SAAS,EAAE,MAAM,EAAE,QAAQ,CAAC;YACxD;YAEM,MAAM,iBAAA,GAAoB,QAAQ,CAAC,WAAW;YAC9C,IAAI,iBAAiB,EAAE;gBACrB,MAAM,WAAA,GAAc,KAAK,CAAC,OAAO,CAAC,iBAAiB,CAAA,GAAI,iBAAA,GAAoB,oBAAoB;gBAC/F,WAAW,CAAC,OAAO,EAAC,MAAA,GAAU,gBAAgB,CAAC,MAAM,EAAE,QAAQ,CAAC,CAAC;YACzE;QACA,CAAK;IACL,CAAG;AACH,CAAC,CAAA;AAED;;CAEA,SACa,2BAAA,mKAA8B,oBAAA,AAAiB,EAAC,4BAA4B;AAEzF,SAAS,iBAAiB,CAAC,QAAQ,EAA4B;IAC7D,OAAO,SAAyB,GAAG,IAAI,EAAqB;QAC1D,MAAM,gBAAA,GAAmB,IAAI,CAAC,CAAC,CAAC;QAChC,IAAI,CAAC,CAAC,CAAA,yKAAI,OAAA,AAAI,EAAC,gBAAgB,EAAE;YAC/B,SAAS,EAAE;gBACT,IAAI,EAAE;oBAAE,QAAQ,0KAAE,kBAAA,AAAe,EAAC,QAAQ,CAAA;gBAAA,CAAG;gBAC7C,OAAO,EAAE,KAAK;gBACd,IAAI,EAAE,YAAY;YAC1B,CAAO;QACP,CAAK,CAAC;QACF,OAAO,QAAQ,CAAC,KAAK,CAAC,IAAI,EAAE,IAAI,CAAC;IACrC,CAAG;AACH;AAEA,SAAS,QAAQ,CAAC,QAAQ,EAAiD;IACzE,OAAO,SAAyB,QAAQ,EAA0B;QAChE,OAAO,QAAQ,CAAC,KAAK,CAAC,IAAI,EAAE;gBAC1B,yKAAA,AAAI,EAAC,QAAQ,EAAE;gBACb,SAAS,EAAE;oBACT,IAAI,EAAE;wBACJ,QAAQ,EAAE,uBAAuB;wBACjC,OAAO,0KAAE,kBAAA,AAAe,EAAC,QAAQ,CAAC;oBAC9C,CAAW;oBACD,OAAO,EAAE,KAAK;oBACd,IAAI,EAAE,YAAY;gBAC5B,CAAS;YACT,CAAO,CAAC;SACH,CAAC;IACN,CAAG;AACH;AAEA,SAAS,QAAQ,CAAC,YAAY,EAA0B;IACtD,OAAO,SAAgC,GAAG,IAAI,EAAmB;QACnE,4DAAA;QACI,MAAM,GAAA,GAAM,IAAI;QAChB,MAAM,mBAAmB,GAAyB;YAAC,QAAQ;YAAE,SAAS;YAAE,YAAY;YAAE,oBAAoB;SAAC;QAE3G,mBAAmB,CAAC,OAAO,EAAC,QAAQ;YAClC,IAAI,IAAA,IAAQ,GAAA,IAAO,OAAO,GAAG,CAAC,IAAI,CAAA,KAAM,UAAU,EAAE;gBAClD,2KAAA,AAAI,EAAC,GAAG,EAAE,IAAI,EAAE,SAAU,QAAQ,EAAE;oBAClC,MAAM,cAAc;wBAClB,SAAS,EAAE;4BACT,IAAI,EAAE;gCACJ,QAAQ,EAAE,IAAI;gCACd,OAAO,0KAAE,kBAAA,AAAe,EAAC,QAAQ,CAAC;4BAClD,CAAe;4BACD,OAAO,EAAE,KAAK;4BACd,IAAI,EAAE,YAAY;wBAChC,CAAa;oBACb,CAAW;oBAEX,uGAAA;oBACU,MAAM,gBAAA,uKAAmB,sBAAA,AAAmB,EAAC,QAAQ,CAAC;oBACtD,IAAI,gBAAgB,EAAE;wBACpB,WAAW,CAAC,SAAS,CAAC,IAAI,CAAC,OAAA,2KAAU,kBAAA,AAAe,EAAC,gBAAgB,CAAC;oBAClF;oBAEA,0BAAA;oBACU,6KAAO,OAAA,AAAI,EAAC,QAAQ,EAAE,WAAW,CAAC;gBAC5C,CAAS,CAAC;YACV;QACA,CAAK,CAAC;QAEF,OAAO,YAAY,CAAC,KAAK,CAAC,IAAI,EAAE,IAAI,CAAC;IACzC,CAAG;AACH;AAEA,SAAS,gBAAgB,CAAC,MAAM,EAAU,kBAAkB,EAAiC;IAC3F,MAAM,YAAA,GAAe,2KAAA;IACrB,MAAM,QAAQ,YAAY,CAAC,MAAM,CAAC,EAAE,SAAS;IAE/C,iDAAA;IACE,IAAI,CAAC,KAAK,EAAE,cAAc,GAAG,kBAAkB,CAAC,EAAE;QAChD;IACJ;IAEE,2KAAA,AAAI,EAAC,KAAK,EAAE,kBAAkB,EAAE,SAAU,QAAQ;QAGhD,OAAO,SAAyB,SAAS,EAAE,EAAE,EAAE,OAAO,EAAgB;YACpE,IAAI;gBACF,IAAI,qBAAqB,CAAC,EAAE,CAAC,EAAE;oBACvC,8BAAA;oBACA,2GAAA;oBACA,mFAAA;oBACA,0GAAA;oBACA,wGAAA;oBACA,6DAAA;oBACU,EAAE,CAAC,WAAA,yKAAc,OAAA,AAAI,EAAC,EAAE,CAAC,WAAW,EAAE;wBACpC,SAAS,EAAE;4BACT,IAAI,EAAE;gCACJ,QAAQ,EAAE,aAAa;gCACvB,OAAO,0KAAE,kBAAA,AAAe,EAAC,EAAE,CAAC;gCAC5B,MAAM;4BACtB,CAAe;4BACD,OAAO,EAAE,KAAK;4BACd,IAAI,EAAE,YAAY;wBAChC,CAAa;oBACb,CAAW,CAAC;gBACZ;YACA,EAAQ,OAAM;YACd,yEAAA;YACA;YAEM,IAAI,kBAAkB,CAAC,2BAA2B,EAAE;gBAClD,0BAA0B,CAAC,IAAI,EAAE,SAAS,EAAE,EAAE,CAAC;YACvD;YAEM,OAAO,QAAQ,CAAC,KAAK,CAAC,IAAI,EAAE;gBAC1B,SAAS;gBACT,6KAAA,AAAI,EAAC,EAAE,EAAE;oBACP,SAAS,EAAE;wBACT,IAAI,EAAE;4BACJ,QAAQ,EAAE,kBAAkB;4BAC5B,OAAO,MAAE,sLAAA,AAAe,EAAC,EAAE,CAAC;4BAC5B,MAAM;wBACpB,CAAa;wBACD,OAAO,EAAE,KAAK;wBACd,IAAI,EAAE,YAAY;oBAC9B,CAAW;gBACX,CAAS,CAAC;gBACF,OAAO;aACR,CAAC;QACR,CAAK;IACL,CAAG,CAAC;KAEF,0KAAA,AAAI,EAAC,KAAK,EAAE,qBAAqB,EAAE,SAAU,2BAA2B;QAItE,OAAO,SAAyB,SAAS,EAAE,EAAE,EAAE,OAAO,EAAgB;YAC1E;;;;;;;;;;;;;;;;OAgBA,GACM,IAAI;gBACF,MAAM,oBAAA,GAAuB,AAAC,EAAA,CAAuB,kBAAkB;gBACvE,IAAI,oBAAoB,EAAE;oBACxB,2BAA2B,CAAC,IAAI,CAAC,IAAI,EAAE,SAAS,EAAE,oBAAoB,EAAE,OAAO,CAAC;gBAC1F;YACA,EAAQ,OAAM;YACd,gFAAA;YACA;YACM,OAAO,2BAA2B,CAAC,IAAI,CAAC,IAAI,EAAE,SAAS,EAAE,EAAE,EAAE,OAAO,CAAC;QAC3E,CAAK;IACL,CAAG,CAAC;AACJ;AAEA,SAAS,qBAAqB,CAAC,GAAG,EAAuC;IACvE,OAAO,OAAO,AAAC,GAAA,CAA4B,WAAA,KAAgB,UAAU;AACvE;AAEA,SAAS,0BAA0B,CAAC,MAAM,EAAW,SAAS,EAAU,EAAE,EAA4C;IACpH,IACE,MAAA,IACA,OAAO,MAAA,KAAW,QAAA,IAClB,qBAAA,IAAyB,MAAA,IACzB,OAAO,MAAM,CAAC,mBAAA,KAAwB,YACtC;QACA,MAAM,CAAC,mBAAmB,CAAC,SAAS,EAAE,EAAE,CAAC;IAC7C;AACA", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1176, "column": 0}, "map": {"version": 3, "file": "browsersession.js", "sources": ["file:///C:/Users/<USER>/Documents/File%20Kerja%20Daffa/GAWEAN/TERANG_LMS/terang-lms-frontend/node_modules/%40sentry/browser/src/integrations/browsersession.ts"], "sourcesContent": ["import { captureSession, debug, defineIntegration, startSession } from '@sentry/core';\nimport { addHistoryInstrumentationHandler } from '@sentry-internal/browser-utils';\nimport { DEBUG_BUILD } from '../debug-build';\nimport { WINDOW } from '../helpers';\n\n/**\n * When added, automatically creates sessions which allow you to track adoption and crashes (crash free rate) in your Releases in Sentry.\n * More information: https://docs.sentry.io/product/releases/health/\n *\n * Note: In order for session tracking to work, you need to set up Releases: https://docs.sentry.io/product/releases/\n */\nexport const browserSessionIntegration = defineIntegration(() => {\n  return {\n    name: 'BrowserSession',\n    setupOnce() {\n      if (typeof WINDOW.document === 'undefined') {\n        DEBUG_BUILD &&\n          debug.warn('Using the `browserSessionIntegration` in non-browser environments is not supported.');\n        return;\n      }\n\n      // The session duration for browser sessions does not track a meaningful\n      // concept that can be used as a metric.\n      // Automatically captured sessions are akin to page views, and thus we\n      // discard their duration.\n      startSession({ ignoreDuration: true });\n      captureSession();\n\n      // We want to create a session for every navigation as well\n      addHistoryInstrumentationHandler(({ from, to }) => {\n        // Don't create an additional session for the initial route or if the location did not change\n        if (from !== undefined && from !== to) {\n          startSession({ ignoreDuration: true });\n          captureSession();\n        }\n      });\n    },\n  };\n});\n"], "names": [], "mappings": ";;;;;;;;;;;;;AAKA;;;;;CAKA,SACa,yBAAA,mKAA4B,oBAAA,AAAiB,EAAC,MAAM;IAC/D,OAAO;QACL,IAAI,EAAE,gBAAgB;QACtB,SAAS,GAAG;YACV,IAAI,yKAAO,SAAM,CAAC,QAAA,KAAa,WAAW,EAAE;yLAC1C,cAAA,4KACE,SAAK,CAAC,IAAI,CAAC,qFAAqF,CAAC;gBACnG;YACR;YAEA,wEAAA;YACA,wCAAA;YACA,sEAAA;YACA,0BAAA;wKACM,eAAA,AAAY,EAAC;gBAAE,cAAc,EAAE,IAAA;YAAA,CAAM,CAAC;YACtC,6KAAA,AAAc,EAAE;YAEtB,2DAAA;8MACM,mCAAA,AAAgC,EAAC,CAAC,EAAE,IAAI,EAAE,EAAA,EAAI,KAAK;gBACzD,6FAAA;gBACQ,IAAI,IAAA,KAAS,aAAa,IAAA,KAAS,EAAE,EAAE;gLACrC,eAAA,AAAY,EAAC;wBAAE,cAAc,EAAE,IAAA;oBAAA,CAAM,CAAC;gLACtC,iBAAA,AAAc,EAAE;gBAC1B;YACA,CAAO,CAAC;QACR,CAAK;IACL,CAAG;AACH,CAAC", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1231, "column": 0}, "map": {"version": 3, "file": "globalhandlers.js", "sources": ["file:///C:/Users/<USER>/Documents/File%20Kerja%20Daffa/GAWEAN/TERANG_LMS/terang-lms-frontend/node_modules/%40sentry/browser/src/integrations/globalhandlers.ts"], "sourcesContent": ["import type { Client, Event, IntegrationFn, Primitive, StackParser } from '@sentry/core';\nimport {\n  addGlobalErrorInstrumentationHandler,\n  addGlobalUnhandledRejectionInstrumentationHandler,\n  captureEvent,\n  debug,\n  defineIntegration,\n  getClient,\n  getLocationHref,\n  isPrimitive,\n  isString,\n  UNKNOWN_FUNCTION,\n} from '@sentry/core';\nimport type { BrowserClient } from '../client';\nimport { DEBUG_BUILD } from '../debug-build';\nimport { eventFromUnknownInput } from '../eventbuilder';\nimport { shouldIgnoreOnError } from '../helpers';\n\ntype GlobalHandlersIntegrationsOptionKeys = 'onerror' | 'onunhandledrejection';\n\ntype GlobalHandlersIntegrations = Record<GlobalHandlersIntegrationsOptionKeys, boolean>;\n\nconst INTEGRATION_NAME = 'GlobalHandlers';\n\nconst _globalHandlersIntegration = ((options: Partial<GlobalHandlersIntegrations> = {}) => {\n  const _options = {\n    onerror: true,\n    onunhandledrejection: true,\n    ...options,\n  };\n\n  return {\n    name: INTEGRATION_NAME,\n    setupOnce() {\n      Error.stackTraceLimit = 50;\n    },\n    setup(client) {\n      if (_options.onerror) {\n        _installGlobalOnErrorHandler(client);\n        globalHandlerLog('onerror');\n      }\n      if (_options.onunhandledrejection) {\n        _installGlobalOnUnhandledRejectionHandler(client);\n        globalHandlerLog('onunhandledrejection');\n      }\n    },\n  };\n}) satisfies IntegrationFn;\n\nexport const globalHandlersIntegration = defineIntegration(_globalHandlersIntegration);\n\nfunction _installGlobalOnErrorHandler(client: Client): void {\n  addGlobalErrorInstrumentationHandler(data => {\n    const { stackParser, attachStacktrace } = getOptions();\n\n    if (getClient() !== client || shouldIgnoreOnError()) {\n      return;\n    }\n\n    const { msg, url, line, column, error } = data;\n\n    const event = _enhanceEventWithInitialFrame(\n      eventFromUnknownInput(stackParser, error || msg, undefined, attachStacktrace, false),\n      url,\n      line,\n      column,\n    );\n\n    event.level = 'error';\n\n    captureEvent(event, {\n      originalException: error,\n      mechanism: {\n        handled: false,\n        type: 'onerror',\n      },\n    });\n  });\n}\n\nfunction _installGlobalOnUnhandledRejectionHandler(client: Client): void {\n  addGlobalUnhandledRejectionInstrumentationHandler(e => {\n    const { stackParser, attachStacktrace } = getOptions();\n\n    if (getClient() !== client || shouldIgnoreOnError()) {\n      return;\n    }\n\n    const error = _getUnhandledRejectionError(e as unknown);\n\n    const event = isPrimitive(error)\n      ? _eventFromRejectionWithPrimitive(error)\n      : eventFromUnknownInput(stackParser, error, undefined, attachStacktrace, true);\n\n    event.level = 'error';\n\n    captureEvent(event, {\n      originalException: error,\n      mechanism: {\n        handled: false,\n        type: 'onunhandledrejection',\n      },\n    });\n  });\n}\n\nfunction _getUnhandledRejectionError(error: unknown): unknown {\n  if (isPrimitive(error)) {\n    return error;\n  }\n\n  // dig the object of the rejection out of known event types\n  try {\n    type ErrorWithReason = { reason: unknown };\n    // PromiseRejectionEvents store the object of the rejection under 'reason'\n    // see https://developer.mozilla.org/en-US/docs/Web/API/PromiseRejectionEvent\n    if ('reason' in (error as ErrorWithReason)) {\n      return (error as ErrorWithReason).reason;\n    }\n\n    type CustomEventWithDetail = { detail: { reason: unknown } };\n    // something, somewhere, (likely a browser extension) effectively casts PromiseRejectionEvents\n    // to CustomEvents, moving the `promise` and `reason` attributes of the PRE into\n    // the CustomEvent's `detail` attribute, since they're not part of CustomEvent's spec\n    // see https://developer.mozilla.org/en-US/docs/Web/API/CustomEvent and\n    // https://github.com/getsentry/sentry-javascript/issues/2380\n    if ('detail' in (error as CustomEventWithDetail) && 'reason' in (error as CustomEventWithDetail).detail) {\n      return (error as CustomEventWithDetail).detail.reason;\n    }\n  } catch {} // eslint-disable-line no-empty\n\n  return error;\n}\n\n/**\n * Create an event from a promise rejection where the `reason` is a primitive.\n *\n * @param reason: The `reason` property of the promise rejection\n * @returns An Event object with an appropriate `exception` value\n */\nfunction _eventFromRejectionWithPrimitive(reason: Primitive): Event {\n  return {\n    exception: {\n      values: [\n        {\n          type: 'UnhandledRejection',\n          // String() is needed because the Primitive type includes symbols (which can't be automatically stringified)\n          value: `Non-Error promise rejection captured with value: ${String(reason)}`,\n        },\n      ],\n    },\n  };\n}\n\nfunction _enhanceEventWithInitialFrame(\n  event: Event,\n  url: string | undefined,\n  line: number | undefined,\n  column: number | undefined,\n): Event {\n  // event.exception\n  const e = (event.exception = event.exception || {});\n  // event.exception.values\n  const ev = (e.values = e.values || []);\n  // event.exception.values[0]\n  const ev0 = (ev[0] = ev[0] || {});\n  // event.exception.values[0].stacktrace\n  const ev0s = (ev0.stacktrace = ev0.stacktrace || {});\n  // event.exception.values[0].stacktrace.frames\n  const ev0sf = (ev0s.frames = ev0s.frames || []);\n\n  const colno = column;\n  const lineno = line;\n  const filename = isString(url) && url.length > 0 ? url : getLocationHref();\n\n  // event.exception.values[0].stacktrace.frames\n  if (ev0sf.length === 0) {\n    ev0sf.push({\n      colno,\n      filename,\n      function: UNKNOWN_FUNCTION,\n      in_app: true,\n      lineno,\n    });\n  }\n\n  return event;\n}\n\nfunction globalHandlerLog(type: string): void {\n  DEBUG_BUILD && debug.log(`Global Handler attached: ${type}`);\n}\n\nfunction getOptions(): { stackParser: StackParser; attachStacktrace?: boolean } {\n  const client = getClient<BrowserClient>();\n  const options = client?.getOptions() || {\n    stackParser: () => [],\n    attachStacktrace: false,\n  };\n  return options;\n}\n"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;AAsBA,MAAM,gBAAA,GAAmB,gBAAgB;AAEzC,MAAM,0BAAA,GAA8B,CAAC,OAAO,GAAwC,CAAA,CAAE,KAAK;IACzF,MAAM,WAAW;QACf,OAAO,EAAE,IAAI;QACb,oBAAoB,EAAE,IAAI;QAC1B,GAAG,OAAO;IACd,CAAG;IAED,OAAO;QACL,IAAI,EAAE,gBAAgB;QACtB,SAAS,GAAG;YACV,KAAK,CAAC,eAAA,GAAkB,EAAE;QAChC,CAAK;QACD,KAAK,EAAC,MAAM,EAAE;YACZ,IAAI,QAAQ,CAAC,OAAO,EAAE;gBACpB,4BAA4B,CAAC,MAAM,CAAC;gBACpC,gBAAgB,CAAC,SAAS,CAAC;YACnC;YACM,IAAI,QAAQ,CAAC,oBAAoB,EAAE;gBACjC,yCAAyC,CAAC,MAAM,CAAC;gBACjD,gBAAgB,CAAC,sBAAsB,CAAC;YAChD;QACA,CAAK;IACL,CAAG;AACH,CAAC,CAAA;MAEY,yBAAA,kKAA4B,qBAAA,AAAiB,EAAC,0BAA0B;AAErF,SAAS,4BAA4B,CAAC,MAAM,EAAgB;kLAC1D,uCAAA,AAAoC,GAAC,IAAA,IAAQ;QAC3C,MAAM,EAAE,WAAW,EAAE,gBAAA,EAAiB,GAAI,UAAU,EAAE;QAEtD,sKAAI,YAAA,AAAS,EAAC,MAAM,MAAA,0KAAU,sBAAA,AAAmB,EAAE,GAAE;YACnD;QACN;QAEI,MAAM,EAAE,GAAG,EAAE,GAAG,EAAE,IAAI,EAAE,MAAM,EAAE,KAAA,EAAM,GAAI,IAAI;QAE9C,MAAM,KAAA,GAAQ,6BAA6B,KACzC,+LAAA,AAAqB,EAAC,WAAW,EAAE,KAAA,IAAS,GAAG,EAAE,SAAS,EAAE,gBAAgB,EAAE,KAAK,CAAC,EACpF,GAAG,EACH,IAAI,EACJ,MAAM;QAGR,KAAK,CAAC,KAAA,GAAQ,OAAO;oKAErB,eAAA,AAAY,EAAC,KAAK,EAAE;YAClB,iBAAiB,EAAE,KAAK;YACxB,SAAS,EAAE;gBACT,OAAO,EAAE,KAAK;gBACd,IAAI,EAAE,SAAS;YACvB,CAAO;QACP,CAAK,CAAC;IACN,CAAG,CAAC;AACJ;AAEA,SAAS,yCAAyC,CAAC,MAAM,EAAgB;+LACvE,oDAAA,AAAiD,GAAC,CAAA,IAAK;QACrD,MAAM,EAAE,WAAW,EAAE,gBAAA,EAAiB,GAAI,UAAU,EAAE;QAEtD,sKAAI,YAAA,AAAS,EAAC,MAAM,MAAA,0KAAU,sBAAA,AAAmB,EAAE,GAAE;YACnD;QACN;QAEI,MAAM,KAAA,GAAQ,2BAA2B,CAAC,GAAa;QAEvD,MAAM,KAAA,mKAAQ,cAAA,AAAW,EAAC,KAAK,IAC3B,gCAAgC,CAAC,KAAK,KACtC,kMAAA,AAAqB,EAAC,WAAW,EAAE,KAAK,EAAE,SAAS,EAAE,gBAAgB,EAAE,IAAI,CAAC;QAEhF,KAAK,CAAC,KAAA,GAAQ,OAAO;QAErB,2KAAA,AAAY,EAAC,KAAK,EAAE;YAClB,iBAAiB,EAAE,KAAK;YACxB,SAAS,EAAE;gBACT,OAAO,EAAE,KAAK;gBACd,IAAI,EAAE,sBAAsB;YACpC,CAAO;QACP,CAAK,CAAC;IACN,CAAG,CAAC;AACJ;AAEA,SAAS,2BAA2B,CAAC,KAAK,EAAoB;IAC5D,KAAI,6KAAA,AAAW,EAAC,KAAK,CAAC,EAAE;QACtB,OAAO,KAAK;IAChB;IAEA,2DAAA;IACE,IAAI;QAEN,0EAAA;QACA,6EAAA;QACI,IAAI,QAAA,IAAa,KAAA,EAAyB,AAAE;YAC1C,OAAO,AAAC,KAAA,CAA0B,MAAM;QAC9C;QAGA,8FAAA;QACA,gFAAA;QACA,qFAAA;QACA,uEAAA;QACA,6DAAA;QACI,IAAI,QAAA,IAAa,KAAA,EAAM,EAA6B,QAAA,IAAY,AAAC,KAAA,CAAgC,MAAM,EAAE;YACvG,OAAO,AAAC,KAAA,CAAgC,MAAM,CAAC,MAAM;QAC3D;IACA,CAAE,CAAE,OAAM,CAAA,CAAC,CAAA,+BAAA;IAET,OAAO,KAAK;AACd;AAEA;;;;;CAKA,GACA,SAAS,gCAAgC,CAAC,MAAM,EAAoB;IAClE,OAAO;QACL,SAAS,EAAE;YACT,MAAM,EAAE;gBACN;oBACE,IAAI,EAAE,oBAAoB;oBACpC,4GAAA;oBACU,KAAK,EAAE,CAAC,iDAAiD,EAAE,MAAM,CAAC,MAAM,CAAC,CAAC,CAAA;gBACA,CAAA;aACA;QACA,CAAA;IACA,CAAA;AACA;AAEA,SAAA,6BAAA,CACA,KAAA,EACA,GAAA,EACA,IAAA,EACA,MAAA;IAEA,kBAAA;IACA,MAAA,CAAA,GAAA,KAAA,CAAA,SAAA,GAAA,KAAA,CAAA,SAAA,IAAA,CAAA,CAAA,CAAA;IACA,yBAAA;IACA,MAAA,EAAA,GAAA,CAAA,CAAA,MAAA,GAAA,CAAA,CAAA,MAAA,IAAA,EAAA,CAAA;IACA,4BAAA;IACA,MAAA,GAAA,GAAA,EAAA,CAAA,CAAA,CAAA,GAAA,EAAA,CAAA,CAAA,CAAA,IAAA,CAAA,CAAA,CAAA;IACA,uCAAA;IACA,MAAA,IAAA,GAAA,GAAA,CAAA,UAAA,GAAA,GAAA,CAAA,UAAA,IAAA,CAAA,CAAA,CAAA;IACA,8CAAA;IACA,MAAA,KAAA,GAAA,IAAA,CAAA,MAAA,GAAA,IAAA,CAAA,MAAA,IAAA,EAAA,CAAA;IAEA,MAAA,KAAA,GAAA,MAAA;IACA,MAAA,MAAA,GAAA,IAAA;IACA,MAAA,QAAA,mKAAA,WAAA,EAAA,GAAA,CAAA,IAAA,GAAA,CAAA,MAAA,GAAA,CAAA,GAAA,GAAA,IAAA,sLAAA,EAAA;IAEA,8CAAA;IACA,IAAA,KAAA,CAAA,MAAA,KAAA,CAAA,EAAA;QACA,KAAA,CAAA,IAAA,CAAA;YACA,KAAA;YACA,QAAA;YACA,QAAA,sKAAA,mBAAA;YACA,MAAA,EAAA,IAAA;YACA,MAAA;QACA,CAAA,CAAA;IACA;IAEA,OAAA,KAAA;AACA;AAEA,SAAA,gBAAA,CAAA,IAAA,EAAA;6KACA,cAAA,6KAAA,QAAA,CAAA,GAAA,CAAA,CAAA,yBAAA,EAAA,IAAA,CAAA,CAAA,CAAA;AACA;AAEA,SAAA,UAAA,GAAA;IACA,MAAA,MAAA,qKAAA,YAAA,EAAA;IACA,MAAA,OAAA,GAAA,MAAA,EAAA,UAAA,EAAA,IAAA;QACA,WAAA,EAAA,IAAA,EAAA;QACA,gBAAA,EAAA,KAAA;IACA,CAAA;IACA,OAAA,OAAA;AACA", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1396, "column": 0}, "map": {"version": 3, "file": "httpcontext.js", "sources": ["file:///C:/Users/<USER>/Documents/File%20Kerja%20Daffa/GAWEAN/TERANG_LMS/terang-lms-frontend/node_modules/%40sentry/browser/src/integrations/httpcontext.ts"], "sourcesContent": ["import { defineIntegration } from '@sentry/core';\nimport { getHttpRequestData, WINDOW } from '../helpers';\n\n/**\n * Collects information about HTTP request headers and\n * attaches them to the event.\n */\nexport const httpContextIntegration = defineIntegration(() => {\n  return {\n    name: 'HttpContext',\n    preprocessEvent(event) {\n      // if none of the information we want exists, don't bother\n      if (!WINDOW.navigator && !WINDOW.location && !WINDOW.document) {\n        return;\n      }\n\n      const reqData = getHttpRequestData();\n      const headers = {\n        ...reqData.headers,\n        ...event.request?.headers,\n      };\n\n      event.request = {\n        ...reqData,\n        ...event.request,\n        headers,\n      };\n    },\n  };\n});\n"], "names": [], "mappings": ";;;;;;;AAGA;;;CAGA,SACa,sBAAA,mKAAyB,oBAAA,AAAiB,EAAC,MAAM;IAC5D,OAAO;QACL,IAAI,EAAE,aAAa;QACnB,eAAe,EAAC,KAAK,EAAE;YAC3B,0DAAA;YACM,IAAI,kKAAC,UAAM,CAAC,SAAA,IAAa,mKAAC,SAAM,CAAC,QAAA,IAAY,mKAAC,SAAM,CAAC,QAAQ,EAAE;gBAC7D;YACR;YAEM,MAAM,OAAA,yKAAU,qBAAA,AAAkB,EAAE;YACpC,MAAM,UAAU;gBACd,GAAG,OAAO,CAAC,OAAO;gBAClB,GAAG,KAAK,CAAC,OAAO,EAAE,OAAO;YACjC,CAAO;YAED,KAAK,CAAC,OAAA,GAAU;gBACd,GAAG,OAAO;gBACV,GAAG,KAAK,CAAC,OAAO;gBAChB,OAAO;YACf,CAAO;QACP,CAAK;IACL,CAAG;AACH,CAAC", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1435, "column": 0}, "map": {"version": 3, "file": "linkederrors.js", "sources": ["file:///C:/Users/<USER>/Documents/File%20Kerja%20Daffa/GAWEAN/TERANG_LMS/terang-lms-frontend/node_modules/%40sentry/browser/src/integrations/linkederrors.ts"], "sourcesContent": ["import type { IntegrationFn } from '@sentry/core';\nimport { applyAggregateErrorsToEvent, defineIntegration } from '@sentry/core';\nimport { exceptionFromError } from '../eventbuilder';\n\ninterface LinkedErrorsOptions {\n  key?: string;\n  limit?: number;\n}\n\nconst DEFAULT_KEY = 'cause';\nconst DEFAULT_LIMIT = 5;\n\nconst INTEGRATION_NAME = 'LinkedErrors';\n\nconst _linkedErrorsIntegration = ((options: LinkedErrorsOptions = {}) => {\n  const limit = options.limit || DEFAULT_LIMIT;\n  const key = options.key || DEFAULT_KEY;\n\n  return {\n    name: INTEGRATION_NAME,\n    preprocessEvent(event, hint, client) {\n      const options = client.getOptions();\n\n      applyAggregateErrorsToEvent(\n        // This differs from the LinkedErrors integration in core by using a different exceptionFromError function\n        exceptionFromError,\n        options.stackParser,\n        key,\n        limit,\n        event,\n        hint,\n      );\n    },\n  };\n}) satisfies IntegrationFn;\n\n/**\n * Aggregrate linked errors in an event.\n */\nexport const linkedErrorsIntegration = defineIntegration(_linkedErrorsIntegration);\n"], "names": [], "mappings": ";;;;;;;;AASA,MAAM,WAAA,GAAc,OAAO;AAC3B,MAAM,aAAA,GAAgB,CAAC;AAEvB,MAAM,gBAAA,GAAmB,cAAc;AAEvC,MAAM,wBAAA,GAA4B,CAAC,OAAO,GAAwB,CAAA,CAAE,KAAK;IACvE,MAAM,KAAA,GAAQ,OAAO,CAAC,KAAA,IAAS,aAAa;IAC5C,MAAM,GAAA,GAAM,OAAO,CAAC,GAAA,IAAO,WAAW;IAEtC,OAAO;QACL,IAAI,EAAE,gBAAgB;QACtB,eAAe,EAAC,KAAK,EAAE,IAAI,EAAE,MAAM,EAAE;YACnC,MAAM,OAAA,GAAU,MAAM,CAAC,UAAU,EAAE;6LAEnC,8BAAA,AAA2B,EACjC,0GAAA;mLACQ,qBAAkB,EAClB,OAAO,CAAC,WAAW,EACnB,GAAG,EACH,KAAK,EACL,KAAK,EACL,IAAI;QAEZ,CAAK;IACL,CAAG;AACH,CAAC,CAAA;AAED;;CAEA,SACa,uBAAA,mKAA0B,oBAAA,AAAiB,EAAC,wBAAwB", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1469, "column": 0}, "map": {"version": 3, "file": "stack-parsers.js", "sources": ["file:///C:/Users/<USER>/Documents/File%20Kerja%20Daffa/GAWEAN/TERANG_LMS/terang-lms-frontend/node_modules/%40sentry/browser/src/stack-parsers.ts"], "sourcesContent": ["// This was originally forked from https://github.com/csnover/TraceKit, and was largely\n// re - written as part of raven - js.\n//\n// This code was later copied to the JavaScript mono - repo and further modified and\n// refactored over the years.\n\n// Copyright (c) 2013 Onur <NAME_EMAIL> and all TraceKit contributors.\n//\n// Permission is hereby granted, free of charge, to any person obtaining a copy of this\n// software and associated documentation files(the 'Software'), to deal in the Software\n// without restriction, including without limitation the rights to use, copy, modify,\n// merge, publish, distribute, sublicense, and / or sell copies of the Software, and to\n// permit persons to whom the Software is furnished to do so, subject to the following\n// conditions:\n//\n// The above copyright notice and this permission notice shall be included in all copies\n// or substantial portions of the Software.\n//\n// THE SOFTWARE IS PROVIDED 'AS IS', WITHOUT WARRANTY OF ANY KIND, EXPRESS OR IMPLIED,\n// INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY, FITNESS FOR A\n// PARTICULAR PURPOSE AND NONINFRINGEMENT.IN NO EVENT SHALL THE AUTHORS OR COPYRIGHT\n// HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER LIABILITY, WHETHER IN AN ACTION OF\n// CONTRACT, TORT OR OTHERWISE, ARISING FROM, OUT OF OR IN CONNECTION WITH THE SOFTWARE\n// OR THE USE OR OTHER DEALINGS IN THE SOFTWARE.\n\nimport type { StackFrame, StackLineParser, StackLineParserFn } from '@sentry/core';\nimport { createStackParser, UNKNOWN_FUNCTION } from '@sentry/core';\n\nconst OPERA10_PRIORITY = 10;\nconst OPERA11_PRIORITY = 20;\nconst CHROME_PRIORITY = 30;\nconst WINJS_PRIORITY = 40;\nconst GECKO_PRIORITY = 50;\n\nfunction createFrame(filename: string, func: string, lineno?: number, colno?: number): StackFrame {\n  const frame: StackFrame = {\n    filename,\n    function: func === '<anonymous>' ? UNKNOWN_FUNCTION : func,\n    in_app: true, // All browser frames are considered in_app\n  };\n\n  if (lineno !== undefined) {\n    frame.lineno = lineno;\n  }\n\n  if (colno !== undefined) {\n    frame.colno = colno;\n  }\n\n  return frame;\n}\n\n// This regex matches frames that have no function name (ie. are at the top level of a module).\n// For example \"at http://localhost:5000//script.js:1:126\"\n// Frames _with_ function names usually look as follows: \"at commitLayoutEffects (react-dom.development.js:23426:1)\"\nconst chromeRegexNoFnName = /^\\s*at (\\S+?)(?::(\\d+))(?::(\\d+))\\s*$/i;\n\n// This regex matches all the frames that have a function name.\nconst chromeRegex =\n  /^\\s*at (?:(.+?\\)(?: \\[.+\\])?|.*?) ?\\((?:address at )?)?(?:async )?((?:<anonymous>|[-a-z]+:|.*bundle|\\/)?.*?)(?::(\\d+))?(?::(\\d+))?\\)?\\s*$/i;\n\nconst chromeEvalRegex = /\\((\\S*)(?::(\\d+))(?::(\\d+))\\)/;\n\n// Chromium based browsers: Chrome, Brave, new Opera, new Edge\n// We cannot call this variable `chrome` because it can conflict with global `chrome` variable in certain environments\n// See: https://github.com/getsentry/sentry-javascript/issues/6880\nconst chromeStackParserFn: StackLineParserFn = line => {\n  // If the stack line has no function name, we need to parse it differently\n  const noFnParts = chromeRegexNoFnName.exec(line) as null | [string, string, string, string];\n\n  if (noFnParts) {\n    const [, filename, line, col] = noFnParts;\n    return createFrame(filename, UNKNOWN_FUNCTION, +line, +col);\n  }\n\n  const parts = chromeRegex.exec(line) as null | [string, string, string, string, string];\n\n  if (parts) {\n    const isEval = parts[2] && parts[2].indexOf('eval') === 0; // start of line\n\n    if (isEval) {\n      const subMatch = chromeEvalRegex.exec(parts[2]) as null | [string, string, string, string];\n\n      if (subMatch) {\n        // throw out eval line/column and use top-most line/column number\n        parts[2] = subMatch[1]; // url\n        parts[3] = subMatch[2]; // line\n        parts[4] = subMatch[3]; // column\n      }\n    }\n\n    // Kamil: One more hack won't hurt us right? Understanding and adding more rules on top of these regexps right now\n    // would be way too time consuming. (TODO: Rewrite whole RegExp to be more readable)\n    const [func, filename] = extractSafariExtensionDetails(parts[1] || UNKNOWN_FUNCTION, parts[2]);\n\n    return createFrame(filename, func, parts[3] ? +parts[3] : undefined, parts[4] ? +parts[4] : undefined);\n  }\n\n  return;\n};\n\nexport const chromeStackLineParser: StackLineParser = [CHROME_PRIORITY, chromeStackParserFn];\n\n// gecko regex: `(?:bundle|\\d+\\.js)`: `bundle` is for react native, `\\d+\\.js` also but specifically for ram bundles because it\n// generates filenames without a prefix like `file://` the filenames in the stacktrace are just 42.js\n// We need this specific case for now because we want no other regex to match.\nconst geckoREgex =\n  /^\\s*(.*?)(?:\\((.*?)\\))?(?:^|@)?((?:[-a-z]+)?:\\/.*?|\\[native code\\]|[^@]*(?:bundle|\\d+\\.js)|\\/[\\w\\-. /=]+)(?::(\\d+))?(?::(\\d+))?\\s*$/i;\nconst geckoEvalRegex = /(\\S+) line (\\d+)(?: > eval line \\d+)* > eval/i;\n\nconst gecko: StackLineParserFn = line => {\n  const parts = geckoREgex.exec(line) as null | [string, string, string, string, string, string];\n\n  if (parts) {\n    const isEval = parts[3] && parts[3].indexOf(' > eval') > -1;\n    if (isEval) {\n      const subMatch = geckoEvalRegex.exec(parts[3]) as null | [string, string, string];\n\n      if (subMatch) {\n        // throw out eval line/column and use top-most line number\n        parts[1] = parts[1] || 'eval';\n        parts[3] = subMatch[1];\n        parts[4] = subMatch[2];\n        parts[5] = ''; // no column when eval\n      }\n    }\n\n    let filename = parts[3];\n    let func = parts[1] || UNKNOWN_FUNCTION;\n    [func, filename] = extractSafariExtensionDetails(func, filename);\n\n    return createFrame(filename, func, parts[4] ? +parts[4] : undefined, parts[5] ? +parts[5] : undefined);\n  }\n\n  return;\n};\n\nexport const geckoStackLineParser: StackLineParser = [GECKO_PRIORITY, gecko];\n\nconst winjsRegex = /^\\s*at (?:((?:\\[object object\\])?.+) )?\\(?((?:[-a-z]+):.*?):(\\d+)(?::(\\d+))?\\)?\\s*$/i;\n\nconst winjs: StackLineParserFn = line => {\n  const parts = winjsRegex.exec(line) as null | [string, string, string, string, string];\n\n  return parts\n    ? createFrame(parts[2], parts[1] || UNKNOWN_FUNCTION, +parts[3], parts[4] ? +parts[4] : undefined)\n    : undefined;\n};\n\nexport const winjsStackLineParser: StackLineParser = [WINJS_PRIORITY, winjs];\n\nconst opera10Regex = / line (\\d+).*script (?:in )?(\\S+)(?:: in function (\\S+))?$/i;\n\nconst opera10: StackLineParserFn = line => {\n  const parts = opera10Regex.exec(line) as null | [string, string, string, string];\n  return parts ? createFrame(parts[2], parts[3] || UNKNOWN_FUNCTION, +parts[1]) : undefined;\n};\n\nexport const opera10StackLineParser: StackLineParser = [OPERA10_PRIORITY, opera10];\n\nconst opera11Regex =\n  / line (\\d+), column (\\d+)\\s*(?:in (?:<anonymous function: ([^>]+)>|([^)]+))\\(.*\\))? in (.*):\\s*$/i;\n\nconst opera11: StackLineParserFn = line => {\n  const parts = opera11Regex.exec(line) as null | [string, string, string, string, string, string];\n  return parts ? createFrame(parts[5], parts[3] || parts[4] || UNKNOWN_FUNCTION, +parts[1], +parts[2]) : undefined;\n};\n\nexport const opera11StackLineParser: StackLineParser = [OPERA11_PRIORITY, opera11];\n\nexport const defaultStackLineParsers = [chromeStackLineParser, geckoStackLineParser];\n\nexport const defaultStackParser = createStackParser(...defaultStackLineParsers);\n\n/**\n * Safari web extensions, starting version unknown, can produce \"frames-only\" stacktraces.\n * What it means, is that instead of format like:\n *\n * Error: wat\n *   at function@url:row:col\n *   at function@url:row:col\n *   at function@url:row:col\n *\n * it produces something like:\n *\n *   function@url:row:col\n *   function@url:row:col\n *   function@url:row:col\n *\n * Because of that, it won't be captured by `chrome` RegExp and will fall into `Gecko` branch.\n * This function is extracted so that we can use it in both places without duplicating the logic.\n * Unfortunately \"just\" changing RegExp is too complicated now and making it pass all tests\n * and fix this case seems like an impossible, or at least way too time-consuming task.\n */\nconst extractSafariExtensionDetails = (func: string, filename: string): [string, string] => {\n  const isSafariExtension = func.indexOf('safari-extension') !== -1;\n  const isSafariWebExtension = func.indexOf('safari-web-extension') !== -1;\n\n  return isSafariExtension || isSafariWebExtension\n    ? [\n        func.indexOf('@') !== -1 ? (func.split('@')[0] as string) : UNKNOWN_FUNCTION,\n        isSafariExtension ? `safari-extension:${filename}` : `safari-web-extension:${filename}`,\n      ]\n    : [func, filename];\n};\n"], "names": [], "mappings": ";;;;;;;;;;;AA4BA,MAAM,gBAAA,GAAmB,EAAE;AAC3B,MAAM,gBAAA,GAAmB,EAAE;AAC3B,MAAM,eAAA,GAAkB,EAAE;AAC1B,MAAM,cAAA,GAAiB,EAAE;AACzB,MAAM,cAAA,GAAiB,EAAE;AAEzB,SAAS,WAAW,CAAC,QAAQ,EAAU,IAAI,EAAU,MAAM,EAAW,KAAK,EAAuB;IAChG,MAAM,KAAK,GAAe;QACxB,QAAQ;QACR,QAAQ,EAAE,IAAA,KAAS,oLAAgB,mBAAA,GAAmB,IAAI;QAC1D,MAAM,EAAE,IAAI;IAChB,CAAG;IAED,IAAI,MAAA,KAAW,SAAS,EAAE;QACxB,KAAK,CAAC,MAAA,GAAS,MAAM;IACzB;IAEE,IAAI,KAAA,KAAU,SAAS,EAAE;QACvB,KAAK,CAAC,KAAA,GAAQ,KAAK;IACvB;IAEE,OAAO,KAAK;AACd;AAEA,+FAAA;AACA,0DAAA;AACA,oHAAA;AACA,MAAM,mBAAA,GAAsB,wCAAwC;AAEpE,+DAAA;AACA,MAAM,WAAA,GACJ,4IAA4I;AAE9I,MAAM,eAAA,GAAkB,+BAA+B;AAEvD,8DAAA;AACA,sHAAA;AACA,kEAAA;AACA,MAAM,mBAAmB,IAAsB,IAAA,IAAQ;IACvD,0EAAA;IACE,MAAM,YAAY,mBAAmB,CAAC,IAAI,CAAC,IAAI,CAAA;IAE/C,IAAI,SAAS,EAAE;QACb,MAAM,GAAG,QAAQ,EAAE,IAAI,EAAE,GAAG,CAAA,GAAI,SAAS;QACzC,OAAO,WAAW,CAAC,QAAQ,sKAAE,mBAAgB,EAAE,CAAC,IAAI,EAAE,CAAC,GAAG,CAAC;IAC/D;IAEE,MAAM,QAAQ,WAAW,CAAC,IAAI,CAAC,IAAI,CAAA;IAEnC,IAAI,KAAK,EAAE;QACT,MAAM,MAAA,GAAS,KAAK,CAAC,CAAC,CAAA,IAAK,KAAK,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,MAAM,CAAA,KAAM,CAAC,CAAA,CAAA,gBAAA;QAEzD,IAAI,MAAM,EAAE;YACV,MAAM,QAAA,GAAW,eAAe,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,CAAA;YAE9C,IAAI,QAAQ,EAAE;gBACpB,iEAAA;gBACQ,KAAK,CAAC,CAAC,CAAA,GAAI,QAAQ,CAAC,CAAC,CAAC,CAAA,CAAA,MAAA;gBACtB,KAAK,CAAC,CAAC,CAAA,GAAI,QAAQ,CAAC,CAAC,CAAC,CAAA,CAAA,OAAA;gBACtB,KAAK,CAAC,CAAC,CAAA,GAAI,QAAQ,CAAC,CAAC,CAAC,CAAA,CAAA,SAAA;YAC9B;QACA;QAEA,kHAAA;QACA,oFAAA;QACI,MAAM,CAAC,IAAI,EAAE,QAAQ,CAAA,GAAI,6BAA6B,CAAC,KAAK,CAAC,CAAC,CAAA,wKAAK,mBAAgB,EAAE,KAAK,CAAC,CAAC,CAAC,CAAC;QAE9F,OAAO,WAAW,CAAC,QAAQ,EAAE,IAAI,EAAE,KAAK,CAAC,CAAC,CAAA,GAAI,CAAC,KAAK,CAAC,CAAC,CAAA,GAAI,SAAS,EAAE,KAAK,CAAC,CAAC,CAAA,GAAI,CAAC,KAAK,CAAC,CAAC,CAAA,GAAI,SAAS,CAAC;IAC1G;IAEE;AACF,CAAC;AAEM,MAAM,qBAAqB,GAAoB;IAAC,eAAe;IAAE,mBAAmB;CAAA;AAE3F,8HAAA;AACA,qGAAA;AACA,8EAAA;AACA,MAAM,UAAA,GACJ,sIAAsI;AACxI,MAAM,cAAA,GAAiB,+CAA+C;AAEtE,MAAM,KAAK,IAAsB,IAAA,IAAQ;IACvC,MAAM,QAAQ,UAAU,CAAC,IAAI,CAAC,IAAI,CAAA;IAElC,IAAI,KAAK,EAAE;QACT,MAAM,MAAA,GAAS,KAAK,CAAC,CAAC,CAAA,IAAK,KAAK,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,SAAS,CAAA,GAAI,CAAA,CAAE;QAC3D,IAAI,MAAM,EAAE;YACV,MAAM,QAAA,GAAW,cAAc,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,CAAA;YAE7C,IAAI,QAAQ,EAAE;gBACpB,0DAAA;gBACQ,KAAK,CAAC,CAAC,CAAA,GAAI,KAAK,CAAC,CAAC,CAAA,IAAK,MAAM;gBAC7B,KAAK,CAAC,CAAC,CAAA,GAAI,QAAQ,CAAC,CAAC,CAAC;gBACtB,KAAK,CAAC,CAAC,CAAA,GAAI,QAAQ,CAAC,CAAC,CAAC;gBACtB,KAAK,CAAC,CAAC,CAAA,GAAI,EAAE,CAAA,CAAA,sBAAA;YACrB;QACA;QAEI,IAAI,QAAA,GAAW,KAAK,CAAC,CAAC,CAAC;QACvB,IAAI,OAAO,KAAK,CAAC,CAAC,CAAA,IAAK,uLAAgB;QACvC,CAAC,IAAI,EAAE,QAAQ,CAAA,GAAI,6BAA6B,CAAC,IAAI,EAAE,QAAQ,CAAC;QAEhE,OAAO,WAAW,CAAC,QAAQ,EAAE,IAAI,EAAE,KAAK,CAAC,CAAC,CAAA,GAAI,CAAC,KAAK,CAAC,CAAC,CAAA,GAAI,SAAS,EAAE,KAAK,CAAC,CAAC,CAAA,GAAI,CAAC,KAAK,CAAC,CAAC,CAAA,GAAI,SAAS,CAAC;IAC1G;IAEE;AACF,CAAC;AAEM,MAAM,oBAAoB,GAAoB;IAAC,cAAc;IAAE,KAAK;CAAA;AAE3E,MAAM,UAAA,GAAa,sFAAsF;AAEzG,MAAM,KAAK,IAAsB,IAAA,IAAQ;IACvC,MAAM,QAAQ,UAAU,CAAC,IAAI,CAAC,IAAI,CAAA;IAElC,OAAO,QACH,WAAW,CAAC,KAAK,CAAC,CAAC,CAAC,EAAE,KAAK,CAAC,CAAC,CAAA,wKAAK,mBAAgB,EAAE,CAAC,KAAK,CAAC,CAAC,CAAC,EAAE,KAAK,CAAC,CAAC,CAAA,GAAI,CAAC,KAAK,CAAC,CAAC,CAAA,GAAI,SAAS,IAC/F,SAAS;AACf,CAAC;AAEM,MAAM,oBAAoB,GAAoB;IAAC,cAAc;IAAE,KAAK;CAAA;AAE3E,MAAM,YAAA,GAAe,6DAA6D;AAElF,MAAM,OAAO,GAAsB,IAAA,IAAQ;IACzC,MAAM,QAAQ,YAAY,CAAC,IAAI,CAAC,IAAI,CAAA;IACpC,OAAO,KAAA,GAAQ,WAAW,CAAC,KAAK,CAAC,CAAC,CAAC,EAAE,KAAK,CAAC,CAAC,CAAA,wKAAK,mBAAgB,EAAE,CAAC,KAAK,CAAC,CAAC,CAAC,CAAA,GAAI,SAAS;AAC3F,CAAC;AAEM,MAAM,sBAAsB,GAAoB;IAAC,gBAAgB;IAAE,OAAO;CAAA;AAEjF,MAAM,YAAA,GACJ,mGAAmG;AAErG,MAAM,OAAO,IAAsB,IAAA,IAAQ;IACzC,MAAM,QAAQ,YAAY,CAAC,IAAI,CAAC,IAAI,CAAA;IACpC,OAAO,KAAA,GAAQ,WAAW,CAAC,KAAK,CAAC,CAAC,CAAC,EAAE,KAAK,CAAC,CAAC,CAAA,IAAK,KAAK,CAAC,CAAC,CAAA,wKAAK,mBAAgB,EAAE,CAAC,KAAK,CAAC,CAAC,CAAC,EAAE,CAAC,KAAK,CAAC,CAAC,CAAC,CAAA,GAAI,SAAS;AAClH,CAAC;AAEM,MAAM,sBAAsB,GAAoB;IAAC,gBAAgB;IAAE,OAAO;CAAA;MAEpE,uBAAA,GAA0B;IAAC,qBAAqB;IAAE,oBAAoB;CAAA;MAEtE,kBAAA,2KAAqB,oBAAA,AAAiB,CAAC,IAAG,uBAAuB;AAE9E;;;;;;;;;;;;;;;;;;;CAmBA,GACA,MAAM,gCAAgC,CAAC,IAAI,EAAU,QAAQ,KAA+B;IAC1F,MAAM,iBAAA,GAAoB,IAAI,CAAC,OAAO,CAAC,kBAAkB,CAAA,KAAM,CAAA,CAAE;IACjE,MAAM,oBAAA,GAAuB,IAAI,CAAC,OAAO,CAAC,sBAAsB,CAAA,KAAM,CAAA,CAAE;IAExE,OAAO,qBAAqB,uBACxB;QACE,IAAI,CAAC,OAAO,CAAC,GAAG,CAAA,KAAM,CAAA,CAAC,GAAK,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAA,uKAAe,mBAAgB;QAC5E,oBAAoB,CAAC,iBAAiB,EAAE,QAAQ,CAAC,CAAA,GAAA,CAAA,qBAAA,EAAA,QAAA,CAAA,CAAA;KACA,GACA;QAAA,IAAA;QAAA,QAAA;KAAA;AACA,CAAA", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1639, "column": 0}, "map": {"version": 3, "file": "fetch.js", "sources": ["file:///C:/Users/<USER>/Documents/File%20Kerja%20Daffa/GAWEAN/TERANG_LMS/terang-lms-frontend/node_modules/%40sentry/browser/src/transports/fetch.ts"], "sourcesContent": ["import type { Transport, TransportMakeRequestResponse, TransportRequest } from '@sentry/core';\nimport { createTransport, rejectedSyncPromise } from '@sentry/core';\nimport { clearCachedImplementation, getNativeImplementation } from '@sentry-internal/browser-utils';\nimport type { WINDOW } from '../helpers';\nimport type { BrowserTransportOptions } from './types';\n\n/**\n * Creates a Transport that uses the Fetch API to send events to Sentry.\n */\nexport function makeFetchTransport(\n  options: BrowserTransportOptions,\n  nativeFetch: typeof WINDOW.fetch | undefined = getNativeImplementation('fetch'),\n): Transport {\n  let pendingBodySize = 0;\n  let pendingCount = 0;\n\n  function makeRequest(request: TransportRequest): PromiseLike<TransportMakeRequestResponse> {\n    const requestSize = request.body.length;\n    pendingBodySize += requestSize;\n    pendingCount++;\n\n    const requestOptions: RequestInit = {\n      body: request.body,\n      method: 'POST',\n      referrerPolicy: 'strict-origin',\n      headers: options.headers,\n      // Outgoing requests are usually cancelled when navigating to a different page, causing a \"TypeError: Failed to\n      // fetch\" error and sending a \"network_error\" client-outcome - in Chrome, the request status shows \"(cancelled)\".\n      // The `keepalive` flag keeps outgoing requests alive, even when switching pages. We want this since we're\n      // frequently sending events right before the user is switching pages (eg. when finishing navigation transactions).\n      // Gotchas:\n      // - `keepalive` isn't supported by Firefox\n      // - As per spec (https://fetch.spec.whatwg.org/#http-network-or-cache-fetch):\n      //   If the sum of contentLength and inflightKeepaliveBytes is greater than 64 kibibytes, then return a network error.\n      //   We will therefore only activate the flag when we're below that limit.\n      // There is also a limit of requests that can be open at the same time, so we also limit this to 15\n      // See https://github.com/getsentry/sentry-javascript/pull/7553 for details\n      keepalive: pendingBodySize <= 60_000 && pendingCount < 15,\n      ...options.fetchOptions,\n    };\n\n    if (!nativeFetch) {\n      clearCachedImplementation('fetch');\n      return rejectedSyncPromise('No fetch implementation available');\n    }\n\n    try {\n      // Note: We do not need to suppress tracing here, becasue we are using the native fetch, instead of our wrapped one.\n      return nativeFetch(options.url, requestOptions).then(response => {\n        pendingBodySize -= requestSize;\n        pendingCount--;\n        return {\n          statusCode: response.status,\n          headers: {\n            'x-sentry-rate-limits': response.headers.get('X-Sentry-Rate-Limits'),\n            'retry-after': response.headers.get('Retry-After'),\n          },\n        };\n      });\n    } catch (e) {\n      clearCachedImplementation('fetch');\n      pendingBodySize -= requestSize;\n      pendingCount--;\n      return rejectedSyncPromise(e);\n    }\n  }\n\n  return createTransport(options, makeRequest);\n}\n"], "names": [], "mappings": ";;;;;;;;AAMA;;CAEA,GACO,SAAS,kBAAkB,CAChC,OAAO,EACP,WAAW,uMAAoC,0BAAA,AAAuB,EAAC,OAAO,CAAC;IAE/E,IAAI,eAAA,GAAkB,CAAC;IACvB,IAAI,YAAA,GAAe,CAAC;IAEpB,SAAS,WAAW,CAAC,OAAO,EAA+D;QACzF,MAAM,WAAA,GAAc,OAAO,CAAC,IAAI,CAAC,MAAM;QACvC,eAAA,IAAmB,WAAW;QAC9B,YAAY,EAAE;QAEd,MAAM,cAAc,GAAgB;YAClC,IAAI,EAAE,OAAO,CAAC,IAAI;YAClB,MAAM,EAAE,MAAM;YACd,cAAc,EAAE,eAAe;YAC/B,OAAO,EAAE,OAAO,CAAC,OAAO;YAC9B,+GAAA;YACA,iHAAA;YACA,0GAAA;YACA,mHAAA;YACA,WAAA;YACA,2CAAA;YACA,8EAAA;YACA,sHAAA;YACA,0EAAA;YACA,mGAAA;YACA,2EAAA;YACM,SAAS,EAAE,eAAA,IAAmB,SAAU,YAAA,GAAe,EAAE;YACzD,GAAG,OAAO,CAAC,YAAY;QAC7B,CAAK;QAED,IAAI,CAAC,WAAW,EAAE;gNAChB,4BAAA,AAAyB,EAAC,OAAO,CAAC;YAClC,OAAO,+LAAA,AAAmB,EAAC,mCAAmC,CAAC;QACrE;QAEI,IAAI;YACR,oHAAA;YACM,OAAO,WAAW,CAAC,OAAO,CAAC,GAAG,EAAE,cAAc,CAAC,CAAC,IAAI,EAAC,YAAY;gBAC/D,eAAA,IAAmB,WAAW;gBAC9B,YAAY,EAAE;gBACd,OAAO;oBACL,UAAU,EAAE,QAAQ,CAAC,MAAM;oBAC3B,OAAO,EAAE;wBACP,sBAAsB,EAAE,QAAQ,CAAC,OAAO,CAAC,GAAG,CAAC,sBAAsB,CAAC;wBACpE,aAAa,EAAE,QAAQ,CAAC,OAAO,CAAC,GAAG,CAAC,aAAa,CAAC;oBAC9D,CAAW;gBACX,CAAS;YACT,CAAO,CAAC;QACR,CAAI,CAAE,OAAO,CAAC,EAAE;gNACV,4BAAA,AAAyB,EAAC,OAAO,CAAC;YAClC,eAAA,IAAmB,WAAW;YAC9B,YAAY,EAAE;YACd,gLAAO,sBAAA,AAAmB,EAAC,CAAC,CAAC;QACnC;IACA;IAEE,8KAAO,kBAAA,AAAe,EAAC,OAAO,EAAE,WAAW,CAAC;AAC9C", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1709, "column": 0}, "map": {"version": 3, "file": "detectBrowserExtension.js", "sources": ["file:///C:/Users/<USER>/Documents/File%20Kerja%20Daffa/GAWEAN/TERANG_LMS/terang-lms-frontend/node_modules/%40sentry/browser/src/utils/detectBrowserExtension.ts"], "sourcesContent": ["import { consoleSandbox, getLocationHref } from '@sentry/core';\nimport { DEBUG_BUILD } from '../debug-build';\nimport { WINDOW } from '../helpers';\n\ntype ExtensionRuntime = {\n  runtime?: {\n    id?: string;\n  };\n};\ntype ExtensionProperties = {\n  chrome?: ExtensionRuntime;\n  browser?: ExtensionRuntime;\n  nw?: unknown;\n};\n\n/**\n * Returns true if the SDK is running in an embedded browser extension.\n * Stand-alone browser extensions (which do not share the same data as the main browser page) are fine.\n */\nexport function checkAndWarnIfIsEmbeddedBrowserExtension(): boolean {\n  if (_isEmbeddedBrowserExtension()) {\n    if (DEBUG_BUILD) {\n      consoleSandbox(() => {\n        // eslint-disable-next-line no-console\n        console.error(\n          '[Sentry] You cannot use Sentry.init() in a browser extension, see: https://docs.sentry.io/platforms/javascript/best-practices/browser-extensions/',\n        );\n      });\n    }\n\n    return true;\n  }\n\n  return false;\n}\n\nfunction _isEmbeddedBrowserExtension(): boolean {\n  if (typeof WINDOW.window === 'undefined') {\n    // No need to show the error if we're not in a browser window environment (e.g. service workers)\n    return false;\n  }\n\n  const _window = WINDOW as typeof WINDOW & ExtensionProperties;\n\n  // Running the SDK in NW.js, which appears like a browser extension but isn't, is also fine\n  // see: https://github.com/getsentry/sentry-javascript/issues/12668\n  if (_window.nw) {\n    return false;\n  }\n\n  const extensionObject = _window['chrome'] || _window['browser'];\n\n  if (!extensionObject?.runtime?.id) {\n    return false;\n  }\n\n  const href = getLocationHref();\n  const extensionProtocols = ['chrome-extension', 'moz-extension', 'ms-browser-extension', 'safari-web-extension'];\n\n  // Running the SDK in a dedicated extension page and calling Sentry.init is fine; no risk of data leakage\n  const isDedicatedExtensionPage =\n    WINDOW === WINDOW.top && extensionProtocols.some(protocol => href.startsWith(`${protocol}://`));\n\n  return !isDedicatedExtensionPage;\n}\n"], "names": [], "mappings": ";;;;;;;;;;AAeA;;;CAGA,GACO,SAAS,wCAAwC,GAAY;IAClE,IAAI,2BAA2B,EAAE,EAAE;QACjC,6KAAI,cAAW,EAAE;yLACf,iBAAA,AAAc,EAAC,MAAM;gBAC3B,sCAAA;gBACQ,OAAO,CAAC,KAAK,CACX,mJAAmJ;YAE7J,CAAO,CAAC;QACR;QAEI,OAAO,IAAI;IACf;IAEE,OAAO,KAAK;AACd;AAEA,SAAS,2BAA2B,GAAY;IAC9C,IAAI,OAAO,2KAAM,CAAC,MAAA,KAAW,WAAW,EAAE;QAC5C,gGAAA;QACI,OAAO,KAAK;IAChB;IAEE,MAAM,OAAA,GAAU,2KAAA;IAElB,2FAAA;IACA,mEAAA;IACE,IAAI,OAAO,CAAC,EAAE,EAAE;QACd,OAAO,KAAK;IAChB;IAEE,MAAM,eAAA,GAAkB,OAAO,CAAC,QAAQ,CAAA,IAAK,OAAO,CAAC,SAAS,CAAC;IAE/D,IAAI,CAAC,eAAe,EAAE,OAAO,EAAE,EAAE,EAAE;QACjC,OAAO,KAAK;IAChB;IAEE,MAAM,IAAA,GAAO,uLAAA,AAAe,EAAE;IAC9B,MAAM,kBAAA,GAAqB;QAAC,kBAAkB;QAAE,eAAe;QAAE,sBAAsB;QAAE,sBAAsB;KAAC;IAElH,yGAAA;IACE,MAAM,wBAAA,qKACJ,SAAA,uKAAW,SAAM,CAAC,GAAA,IAAO,kBAAkB,CAAC,IAAI,EAAC,WAAY,IAAI,CAAC,UAAU,CAAC,CAAC,EAAA,QAAA,CAAA,GAAA,CAAA,CAAA,CAAA;IAEA,OAAA,CAAA,wBAAA;AACA", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1768, "column": 0}, "map": {"version": 3, "file": "sdk.js", "sources": ["file:///C:/Users/<USER>/Documents/File%20Kerja%20Daffa/GAWEAN/TERANG_LMS/terang-lms-frontend/node_modules/%40sentry/browser/src/sdk.ts"], "sourcesContent": ["import type { Client, Integration, Options } from '@sentry/core';\nimport {\n  dedupeIntegration,\n  functionToStringIntegration,\n  getIntegrationsToSetup,\n  inboundFiltersIntegration,\n  initAndBind,\n  stackParserFromStackParserOptions,\n} from '@sentry/core';\nimport type { BrowserClientOptions, BrowserOptions } from './client';\nimport { BrowserClient } from './client';\nimport { breadcrumbsIntegration } from './integrations/breadcrumbs';\nimport { browserApiErrorsIntegration } from './integrations/browserapierrors';\nimport { browserSessionIntegration } from './integrations/browsersession';\nimport { globalHandlersIntegration } from './integrations/globalhandlers';\nimport { httpContextIntegration } from './integrations/httpcontext';\nimport { linkedErrorsIntegration } from './integrations/linkederrors';\nimport { defaultStackParser } from './stack-parsers';\nimport { makeFetchTransport } from './transports/fetch';\nimport { checkAndWarnIfIsEmbeddedBrowserExtension } from './utils/detectBrowserExtension';\n\n/** Get the default integrations for the browser SDK. */\nexport function getDefaultIntegrations(_options: Options): Integration[] {\n  /**\n   * Note: Please make sure this stays in sync with Angular SDK, which re-exports\n   * `getDefaultIntegrations` but with an adjusted set of integrations.\n   */\n  return [\n    // TODO(v10): Replace with `eventFiltersIntegration` once we remove the deprecated `inboundFiltersIntegration`\n    // eslint-disable-next-line deprecation/deprecation\n    inboundFiltersIntegration(),\n    functionToStringIntegration(),\n    browserApiErrorsIntegration(),\n    breadcrumbsIntegration(),\n    globalHandlersIntegration(),\n    linkedErrorsIntegration(),\n    dedupeIntegration(),\n    httpContextIntegration(),\n    browserSessionIntegration(),\n  ];\n}\n\n/**\n * The Sentry Browser SDK Client.\n *\n * To use this SDK, call the {@link init} function as early as possible when\n * loading the web page. To set context information or send manual events, use\n * the provided methods.\n *\n * @example\n *\n * ```\n *\n * import { init } from '@sentry/browser';\n *\n * init({\n *   dsn: '__DSN__',\n *   // ...\n * });\n * ```\n *\n * @example\n * ```\n *\n * import { addBreadcrumb } from '@sentry/browser';\n * addBreadcrumb({\n *   message: 'My Breadcrumb',\n *   // ...\n * });\n * ```\n *\n * @example\n *\n * ```\n *\n * import * as Sentry from '@sentry/browser';\n * Sentry.captureMessage('Hello, world!');\n * Sentry.captureException(new Error('Good bye'));\n * Sentry.captureEvent({\n *   message: 'Manual',\n *   stacktrace: [\n *     // ...\n *   ],\n * });\n * ```\n *\n * @see {@link BrowserOptions} for documentation on configuration options.\n */\nexport function init(options: BrowserOptions = {}): Client | undefined {\n  const shouldDisableBecauseIsBrowserExtenstion =\n    !options.skipBrowserExtensionCheck && checkAndWarnIfIsEmbeddedBrowserExtension();\n\n  const clientOptions: BrowserClientOptions = {\n    ...options,\n    enabled: shouldDisableBecauseIsBrowserExtenstion ? false : options.enabled,\n    stackParser: stackParserFromStackParserOptions(options.stackParser || defaultStackParser),\n    integrations: getIntegrationsToSetup({\n      integrations: options.integrations,\n      defaultIntegrations:\n        options.defaultIntegrations == null ? getDefaultIntegrations(options) : options.defaultIntegrations,\n    }),\n    transport: options.transport || makeFetchTransport,\n  };\n  return initAndBind(BrowserClient, clientOptions);\n}\n\n/**\n * This function is here to be API compatible with the loader.\n * @hidden\n */\nexport function forceLoad(): void {\n  // Noop\n}\n\n/**\n * This function is here to be API compatible with the loader.\n * @hidden\n */\nexport function onLoad(callback: () => void): void {\n  callback();\n}\n"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAqBA,sDAAA,GACO,SAAS,sBAAsB,CAAC,QAAQ,EAA0B;IACzE;;;GAGA,GACE,OAAO;QACT,8GAAA;QACA,mDAAA;YACI,yMAAA,AAAyB,EAAE;6LAC3B,8BAAA,AAA2B,EAAE;uMAC7B,8BAAA,AAA2B,EAAE;kMAC7B,yBAAA,AAAsB,EAAE;QACxB,yNAAA,AAAyB,EAAE;mMAC3B,0BAAA,AAAuB,EAAE;mLACzB,oBAAA,AAAiB,EAAE;SACnB,kNAAA,AAAsB,EAAE;qMACxB,4BAAA,AAAyB,EAAE;KAC5B;AACH;AAEA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;CA6CA,GACO,SAAS,IAAI,CAAC,OAAO,GAAmB,CAAA,CAAE,EAAsB;IACrE,MAAM,uCAAA,GACJ,CAAC,OAAO,CAAC,yBAAA,KAA6B,wOAAA,AAAwC,EAAE;IAElF,MAAM,aAAa,GAAyB;QAC1C,GAAG,OAAO;QACV,OAAO,EAAE,uCAAA,GAA0C,QAAQ,OAAO,CAAC,OAAO;QAC1E,WAAW,0KAAE,oCAAA,AAAiC,EAAC,OAAO,CAAC,WAAA,+KAAe,qBAAkB,CAAC;QACzF,YAAY,kKAAE,yBAAA,AAAsB,EAAC;YACnC,YAAY,EAAE,OAAO,CAAC,YAAY;YAClC,mBAAmB,EACjB,OAAO,CAAC,mBAAA,IAAuB,IAAA,GAAO,sBAAsB,CAAQ,CAAA,GAAI,OAAO,CAAC,mBAAmB;QAC3G,CAAK,CAAC;QACF,SAAS,EAAE,OAAO,CAAC,SAAA,kLAAa,qBAAkB;IACtD,CAAG;IACD,+JAAO,cAAA,AAAW,mKAAC,gBAAa,EAAE,aAAa,CAAC;AAClD;AAEA;;;CAGA,GACO,SAAS,SAAS,GAAS;AAClC,OAAA;AACA;AAEA;;;CAGA,GACO,SAAS,MAAM,CAAC,QAAQ,EAAoB;IACjD,QAAQ,EAAE;AACZ", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1898, "column": 0}, "map": {"version": 3, "file": "backgroundtab.js", "sources": ["file:///C:/Users/<USER>/Documents/File%20Kerja%20Daffa/GAWEAN/TERANG_LMS/terang-lms-frontend/node_modules/%40sentry/browser/src/tracing/backgroundtab.ts"], "sourcesContent": ["import { debug, getActiveSpan, getRootSpan, SPAN_STATUS_ERROR, spanToJSON } from '@sentry/core';\nimport { DEBUG_BUILD } from '../debug-build';\nimport { WINDOW } from '../helpers';\n\n/**\n * Add a listener that cancels and finishes a transaction when the global\n * document is hidden.\n */\nexport function registerBackgroundTabDetection(): void {\n  if (WINDOW.document) {\n    WINDOW.document.addEventListener('visibilitychange', () => {\n      const activeSpan = getActiveSpan();\n      if (!activeSpan) {\n        return;\n      }\n\n      const rootSpan = getRootSpan(activeSpan);\n\n      if (WINDOW.document.hidden && rootSpan) {\n        const cancelledStatus = 'cancelled';\n\n        const { op, status } = spanToJSON(rootSpan);\n\n        if (DEBUG_BUILD) {\n          debug.log(`[Tracing] Transaction: ${cancelledStatus} -> since tab moved to the background, op: ${op}`);\n        }\n\n        // We should not set status if it is already set, this prevent important statuses like\n        // error or data loss from being overwritten on transaction.\n        if (!status) {\n          rootSpan.setStatus({ code: SPAN_STATUS_ERROR, message: cancelledStatus });\n        }\n\n        rootSpan.setAttribute('sentry.cancellation_reason', 'document.hidden');\n        rootSpan.end();\n      }\n    });\n  } else {\n    DEBUG_BUILD && debug.warn('[Tracing] Could not set up background tab detection due to lack of global document');\n  }\n}\n"], "names": [], "mappings": ";;;;;;;;;;;AAIA;;;CAGA,GACO,SAAS,8BAA8B,GAAS;IACrD,sKAAI,SAAM,CAAC,QAAQ,EAAE;0KACnB,SAAM,CAAC,QAAQ,CAAC,gBAAgB,CAAC,kBAAkB,EAAE,MAAM;YACzD,MAAM,UAAA,0KAAa,gBAAA,AAAa,EAAE;YAClC,IAAI,CAAC,UAAU,EAAE;gBACf;YACR;YAEM,MAAM,QAAA,IAAW,oLAAA,AAAW,EAAC,UAAU,CAAC;YAExC,sKAAI,SAAM,CAAC,QAAQ,CAAC,MAAA,IAAU,QAAQ,EAAE;gBACtC,MAAM,eAAA,GAAkB,WAAW;gBAEnC,MAAM,EAAE,EAAE,EAAE,MAAA,EAAA,0KAAW,aAAA,AAAU,EAAC,QAAQ,CAAC;gBAE3C,IAAI,uLAAW,EAAE;6LACf,QAAK,CAAC,GAAG,CAAC,CAAC,uBAAuB,EAAE,eAAe,CAAC,2CAA2C,EAAE,EAAE,CAAC,CAAA,CAAA;gBACA;gBAEA,sFAAA;gBACA,4DAAA;gBACA,IAAA,CAAA,MAAA,EAAA;oBACA,QAAA,CAAA,SAAA,CAAA;wBAAA,IAAA,uKAAA,qBAAA;wBAAA,OAAA,EAAA,eAAA;oBAAA,CAAA,CAAA;gBACA;gBAEA,QAAA,CAAA,YAAA,CAAA,4BAAA,EAAA,iBAAA,CAAA;gBACA,QAAA,CAAA,GAAA,EAAA;YACA;QACA,CAAA,CAAA;IACA,CAAA,MAAA;iLACA,cAAA,6KAAA,QAAA,CAAA,IAAA,CAAA,oFAAA,CAAA;IACA;AACA", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1950, "column": 0}, "map": {"version": 3, "file": "linkedTraces.js", "sources": ["file:///C:/Users/<USER>/Documents/File%20Kerja%20Daffa/GAWEAN/TERANG_LMS/terang-lms-frontend/node_modules/%40sentry/browser/src/tracing/linkedTraces.ts"], "sourcesContent": ["import type { Client, PropagationContext, Span } from '@sentry/core';\nimport {\n  type SpanContextData,\n  debug,\n  getCurrentScope,\n  getRootSpan,\n  SEMANTIC_ATTRIBUTE_SENTRY_PREVIOUS_TRACE_SAMPLE_RATE,\n  SEMANTIC_ATTRIBUTE_SENTRY_SAMPLE_RATE,\n  SEMANTIC_LINK_ATTRIBUTE_LINK_TYPE,\n  spanToJSON,\n} from '@sentry/core';\nimport { DEBUG_BUILD } from '../debug-build';\nimport { WINDOW } from '../exports';\n\nexport interface PreviousTraceInfo {\n  /**\n   * Span context of the previous trace's local root span\n   */\n  spanContext: SpanContextData;\n\n  /**\n   * Timestamp in seconds when the previous trace was started\n   */\n  startTimestamp: number;\n\n  /**\n   * sample rate of the previous trace\n   */\n  sampleRate: number;\n\n  /**\n   * The sample rand of the previous trace\n   */\n  sampleRand: number;\n}\n\n// 1h in seconds\nexport const PREVIOUS_TRACE_MAX_DURATION = 3600;\n\n// session storage key\nexport const PREVIOUS_TRACE_KEY = 'sentry_previous_trace';\n\nexport const PREVIOUS_TRACE_TMP_SPAN_ATTRIBUTE = 'sentry.previous_trace';\n\n/**\n * Takes care of linking traces and applying the (consistent) sampling behavoiour based on the passed options\n * @param options - options for linking traces and consistent trace sampling (@see BrowserTracingOptions)\n * @param client - Sentry client\n */\nexport function linkTraces(\n  client: Client,\n  {\n    linkPreviousTrace,\n    consistentTraceSampling,\n  }: {\n    linkPreviousTrace: 'session-storage' | 'in-memory';\n    consistentTraceSampling: boolean;\n  },\n): void {\n  const useSessionStorage = linkPreviousTrace === 'session-storage';\n\n  let inMemoryPreviousTraceInfo = useSessionStorage ? getPreviousTraceFromSessionStorage() : undefined;\n\n  client.on('spanStart', span => {\n    if (getRootSpan(span) !== span) {\n      return;\n    }\n\n    const oldPropagationContext = getCurrentScope().getPropagationContext();\n    inMemoryPreviousTraceInfo = addPreviousTraceSpanLink(inMemoryPreviousTraceInfo, span, oldPropagationContext);\n\n    if (useSessionStorage) {\n      storePreviousTraceInSessionStorage(inMemoryPreviousTraceInfo);\n    }\n  });\n\n  let isFirstTraceOnPageload = true;\n  if (consistentTraceSampling) {\n    /*\n    When users opt into `consistentTraceSampling`, we need to ensure that we propagate\n    the previous trace's sample rate and rand to the current trace. This is necessary because otherwise, span\n    metric extrapolation is inaccurate, as we'd propagate too high of a sample rate for the subsequent traces.\n\n    So therefore, we pretend that the previous trace was the parent trace of the newly started trace. To do that,\n    we mutate the propagation context of the current trace and set the sample rate and sample rand of the previous trace.\n    Timing-wise, it is fine because it happens before we even sample the root span.\n\n    @see https://github.com/getsentry/sentry-javascript/issues/15754\n    */\n    client.on('beforeSampling', mutableSamplingContextData => {\n      if (!inMemoryPreviousTraceInfo) {\n        return;\n      }\n\n      const scope = getCurrentScope();\n      const currentPropagationContext = scope.getPropagationContext();\n\n      // We do not want to force-continue the sampling decision if we continue a trace\n      // that was started on the backend. Most prominently, this will happen in MPAs where\n      // users hard-navigate between pages. In this case, the sampling decision of a potentially\n      // started trace on the server takes precedence.\n      // Why? We want to prioritize inter-trace consistency over intra-trace consistency.\n      if (isFirstTraceOnPageload && currentPropagationContext.parentSpanId) {\n        isFirstTraceOnPageload = false;\n        return;\n      }\n\n      scope.setPropagationContext({\n        ...currentPropagationContext,\n        dsc: {\n          ...currentPropagationContext.dsc,\n          sample_rate: String(inMemoryPreviousTraceInfo.sampleRate),\n          sampled: String(spanContextSampled(inMemoryPreviousTraceInfo.spanContext)),\n        },\n        sampleRand: inMemoryPreviousTraceInfo.sampleRand,\n      });\n\n      mutableSamplingContextData.parentSampled = spanContextSampled(inMemoryPreviousTraceInfo.spanContext);\n      mutableSamplingContextData.parentSampleRate = inMemoryPreviousTraceInfo.sampleRate;\n\n      mutableSamplingContextData.spanAttributes = {\n        ...mutableSamplingContextData.spanAttributes,\n        [SEMANTIC_ATTRIBUTE_SENTRY_PREVIOUS_TRACE_SAMPLE_RATE]: inMemoryPreviousTraceInfo.sampleRate,\n      };\n    });\n  }\n}\n\n/**\n * Adds a previous_trace span link to the passed span if the passed\n * previousTraceInfo is still valid.\n *\n * @returns the updated previous trace info (based on the current span/trace) to\n * be used on the next call\n */\nexport function addPreviousTraceSpanLink(\n  previousTraceInfo: PreviousTraceInfo | undefined,\n  span: Span,\n  oldPropagationContext: PropagationContext,\n): PreviousTraceInfo {\n  const spanJson = spanToJSON(span);\n\n  function getSampleRate(): number {\n    try {\n      return (\n        Number(oldPropagationContext.dsc?.sample_rate) ?? Number(spanJson.data?.[SEMANTIC_ATTRIBUTE_SENTRY_SAMPLE_RATE])\n      );\n    } catch {\n      return 0;\n    }\n  }\n\n  const updatedPreviousTraceInfo = {\n    spanContext: span.spanContext(),\n    startTimestamp: spanJson.start_timestamp,\n    sampleRate: getSampleRate(),\n    sampleRand: oldPropagationContext.sampleRand,\n  };\n\n  if (!previousTraceInfo) {\n    return updatedPreviousTraceInfo;\n  }\n\n  const previousTraceSpanCtx = previousTraceInfo.spanContext;\n  if (previousTraceSpanCtx.traceId === spanJson.trace_id) {\n    // This means, we're still in the same trace so let's not update the previous trace info\n    // or add a link to the current span.\n    // Once we move away from the long-lived, route-based trace model, we can remove this cases\n    return previousTraceInfo;\n  }\n\n  // Only add the link if the startTimeStamp of the previous trace's root span is within\n  // PREVIOUS_TRACE_MAX_DURATION (1h) of the current root span's startTimestamp\n  // This is done to\n  // - avoid adding links to \"stale\" traces\n  // - enable more efficient querying for previous/next traces in Sentry\n  if (Date.now() / 1000 - previousTraceInfo.startTimestamp <= PREVIOUS_TRACE_MAX_DURATION) {\n    if (DEBUG_BUILD) {\n      debug.log(\n        `Adding previous_trace ${previousTraceSpanCtx} link to span ${{\n          op: spanJson.op,\n          ...span.spanContext(),\n        }}`,\n      );\n    }\n\n    span.addLink({\n      context: previousTraceSpanCtx,\n      attributes: {\n        [SEMANTIC_LINK_ATTRIBUTE_LINK_TYPE]: 'previous_trace',\n      },\n    });\n\n    // TODO: Remove this once EAP can store span links. We currently only set this attribute so that we\n    // can obtain the previous trace information from the EAP store. Long-term, EAP will handle\n    // span links and then we should remove this again. Also throwing in a TODO(v10), to remind us\n    // to check this at v10 time :)\n    span.setAttribute(\n      PREVIOUS_TRACE_TMP_SPAN_ATTRIBUTE,\n      `${previousTraceSpanCtx.traceId}-${previousTraceSpanCtx.spanId}-${\n        spanContextSampled(previousTraceSpanCtx) ? 1 : 0\n      }`,\n    );\n  }\n\n  return updatedPreviousTraceInfo;\n}\n\n/**\n * Stores @param previousTraceInfo in sessionStorage.\n */\nexport function storePreviousTraceInSessionStorage(previousTraceInfo: PreviousTraceInfo): void {\n  try {\n    WINDOW.sessionStorage.setItem(PREVIOUS_TRACE_KEY, JSON.stringify(previousTraceInfo));\n  } catch (e) {\n    // Ignore potential errors (e.g. if sessionStorage is not available)\n    DEBUG_BUILD && debug.warn('Could not store previous trace in sessionStorage', e);\n  }\n}\n\n/**\n * Retrieves the previous trace from sessionStorage if available.\n */\nexport function getPreviousTraceFromSessionStorage(): PreviousTraceInfo | undefined {\n  try {\n    const previousTraceInfo = WINDOW.sessionStorage?.getItem(PREVIOUS_TRACE_KEY);\n    // @ts-expect-error - intentionally risking JSON.parse throwing when previousTraceInfo is null to save bundle size\n    return JSON.parse(previousTraceInfo);\n  } catch {\n    return undefined;\n  }\n}\n\n/**\n * see {@link import('@sentry/core').spanIsSampled}\n */\nexport function spanContextSampled(ctx: SpanContextData): boolean {\n  return ctx.traceFlags === 0x1;\n}\n"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;AAoCA,gBAAA;AACO,MAAM,2BAAA,GAA8B;AAE3C,sBAAA;AACO,MAAM,kBAAA,GAAqB;AAE3B,MAAM,iCAAA,GAAoC;AAEjD;;;;CAIA,GACO,SAAS,UAAU,CACxB,MAAM,EACN,EACE,iBAAiB,EACjB,uBAAuB,EAC3B;IAKE,MAAM,iBAAA,GAAoB,iBAAA,KAAsB,iBAAiB;IAEjE,IAAI,4BAA4B,iBAAA,GAAoB,kCAAkC,EAAC,GAAI,SAAS;IAEpG,MAAM,CAAC,EAAE,CAAC,WAAW,GAAE,QAAQ;QAC7B,IAAI,qLAAA,AAAW,EAAC,IAAI,CAAA,KAAM,IAAI,EAAE;YAC9B;QACN;QAEI,MAAM,0LAAwB,kBAAA,AAAe,EAAE,EAAC,qBAAqB,EAAE;QACvE,yBAAA,GAA4B,wBAAwB,CAAC,yBAAyB,EAAE,IAAI,EAAE,qBAAqB,CAAC;QAE5G,IAAI,iBAAiB,EAAE;YACrB,kCAAkC,CAAC,yBAAyB,CAAC;QACnE;IACA,CAAG,CAAC;IAEF,IAAI,sBAAA,GAAyB,IAAI;IACjC,IAAI,uBAAuB,EAAE;QAC/B;;;;;;;;;;IAUA,GACI,MAAM,CAAC,EAAE,CAAC,gBAAgB,GAAE,8BAA8B;YACxD,IAAI,CAAC,yBAAyB,EAAE;gBAC9B;YACR;YAEM,MAAM,KAAA,IAAQ,mLAAA,AAAe,EAAE;YAC/B,MAAM,yBAAA,GAA4B,KAAK,CAAC,qBAAqB,EAAE;YAErE,gFAAA;YACA,oFAAA;YACA,0FAAA;YACA,gDAAA;YACA,mFAAA;YACM,IAAI,sBAAA,IAA0B,yBAAyB,CAAC,YAAY,EAAE;gBACpE,sBAAA,GAAyB,KAAK;gBAC9B;YACR;YAEM,KAAK,CAAC,qBAAqB,CAAC;gBAC1B,GAAG,yBAAyB;gBAC5B,GAAG,EAAE;oBACH,GAAG,yBAAyB,CAAC,GAAG;oBAChC,WAAW,EAAE,MAAM,CAAC,yBAAyB,CAAC,UAAU,CAAC;oBACzD,OAAO,EAAE,MAAM,CAAC,kBAAkB,CAAC,yBAAyB,CAAC,WAAW,CAAC,CAAC;gBACpF,CAAS;gBACD,UAAU,EAAE,yBAAyB,CAAC,UAAU;YACxD,CAAO,CAAC;YAEF,0BAA0B,CAAC,aAAA,GAAgB,kBAAkB,CAAC,yBAAyB,CAAC,WAAW,CAAC;YACpG,0BAA0B,CAAC,gBAAA,GAAmB,yBAAyB,CAAC,UAAU;YAElF,0BAA0B,CAAC,cAAA,GAAiB;gBAC1C,GAAG,0BAA0B,CAAC,cAAc;gBAC5C,oKAAC,uDAAoD,CAAA,EAAG,yBAAyB,CAAC,UAAU;YACpG,CAAO;QACP,CAAK,CAAC;IACN;AACA;AAEA;;;;;;CAMA,GACO,SAAS,wBAAwB,CACtC,iBAAiB,EACjB,IAAI,EACJ,qBAAqB;IAErB,MAAM,QAAA,GAAW,oLAAA,AAAU,EAAC,IAAI,CAAC;IAEjC,SAAS,aAAa,GAAW;QAC/B,IAAI;YACF,OACE,MAAM,CAAC,qBAAqB,CAAC,GAAG,EAAE,WAAW,CAAA,IAAK,MAAM,CAAC,QAAQ,CAAC,IAAI,EAAA,mKAAG,yCAAqC,CAAC;QAEvH,EAAM,OAAM;YACN,OAAO,CAAC;QACd;IACA;IAEE,MAAM,2BAA2B;QAC/B,WAAW,EAAE,IAAI,CAAC,WAAW,EAAE;QAC/B,cAAc,EAAE,QAAQ,CAAC,eAAe;QACxC,UAAU,EAAE,aAAa,EAAE;QAC3B,UAAU,EAAE,qBAAqB,CAAC,UAAU;IAChD,CAAG;IAED,IAAI,CAAC,iBAAiB,EAAE;QACtB,OAAO,wBAAwB;IACnC;IAEE,MAAM,oBAAA,GAAuB,iBAAiB,CAAC,WAAW;IAC1D,IAAI,oBAAoB,CAAC,OAAA,KAAY,QAAQ,CAAC,QAAQ,EAAE;QAC1D,wFAAA;QACA,qCAAA;QACA,2FAAA;QACI,OAAO,iBAAiB;IAC5B;IAEA,sFAAA;IACA,6EAAA;IACA,kBAAA;IACA,yCAAA;IACA,sEAAA;IACE,IAAI,IAAI,CAAC,GAAG,EAAC,GAAI,IAAA,GAAO,iBAAiB,CAAC,cAAA,IAAkB,2BAA2B,EAAE;QACvF,6KAAI,cAAW,EAAE;qLACf,QAAK,CAAC,GAAG,CACP,CAAC,sBAAsB,EAAE,oBAAoB,CAAC,cAAc,EAAE;gBAC5D,EAAE,EAAE,QAAQ,CAAC,EAAE;gBACf,GAAG,IAAI,CAAC,WAAW,EAAE;YAC/B,CAAS,CAAC,CAAA;QAEA;QAEA,IAAA,CAAA,OAAA,CAAA;YACA,OAAA,EAAA,oBAAA;YACA,UAAA,EAAA;gBACA,oKAAA,oCAAA,CAAA,EAAA,gBAAA;YACA,CAAA;QACA,CAAA,CAAA;QAEA,mGAAA;QACA,2FAAA;QACA,8FAAA;QACA,+BAAA;QACA,IAAA,CAAA,YAAA,CACA,iCAAA,EACA,CAAA,EAAA,oBAAA,CAAA,OAAA,CAAA,CAAA,EAAA,oBAAA,CAAA,MAAA,CAAA,CAAA,EACA,kBAAA,CAAA,oBAAA,CAAA,GAAA,CAAA,GAAA,GACA;IAEA;IAEA,OAAA,wBAAA;AACA;AAEA;;CAEA,GACA,SAAA,kCAAA,CAAA,iBAAA,EAAA;IACA,IAAA;0KACA,SAAA,CAAA,cAAA,CAAA,OAAA,CAAA,kBAAA,EAAA,IAAA,CAAA,SAAA,CAAA,iBAAA,CAAA,CAAA;IACA,CAAA,CAAA,OAAA,CAAA,EAAA;QACA,oEAAA;iLACA,cAAA,6KAAA,QAAA,CAAA,IAAA,CAAA,kDAAA,EAAA,CAAA,CAAA;IACA;AACA;AAEA;;CAEA,GACA,SAAA,kCAAA,GAAA;IACA,IAAA;QACA,MAAA,iBAAA,qKAAA,SAAA,CAAA,cAAA,EAAA,OAAA,CAAA,kBAAA,CAAA;QACA,kHAAA;QACA,OAAA,IAAA,CAAA,KAAA,CAAA,iBAAA,CAAA;IACA,CAAA,CAAA,OAAA;QACA,OAAA,SAAA;IACA;AACA;AAEA;;CAEA,GACA,SAAA,kBAAA,CAAA,GAAA,EAAA;IACA,OAAA,GAAA,CAAA,UAAA,KAAA,GAAA;AACA", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2135, "column": 0}, "map": {"version": 3, "file": "resource-timing.js", "sources": ["file:///C:/Users/<USER>/Documents/File%20Kerja%20Daffa/GAWEAN/TERANG_LMS/terang-lms-frontend/node_modules/%40sentry/browser/src/tracing/resource-timing.ts"], "sourcesContent": ["import type { Span } from '@sentry/core';\nimport { browserPerformanceTimeOrigin } from '@sentry/core';\nimport { extractNetworkProtocol } from '@sentry-internal/browser-utils';\n\nfunction getAbsoluteTime(time: number = 0): number {\n  return ((browserPerformanceTimeOrigin() || performance.timeOrigin) + time) / 1000;\n}\n\n/**\n * Converts a PerformanceResourceTiming entry to span data for the resource span.\n *\n * @param resourceTiming\n * @returns An array where the first element is the attribute name and the second element is the attribute value.\n */\nexport function resourceTimingToSpanAttributes(\n  resourceTiming: PerformanceResourceTiming,\n): Array<Parameters<Span['setAttribute']>> {\n  const timingSpanData: Array<Parameters<Span['setAttribute']>> = [];\n  // Checking for only `undefined` and `null` is intentional because it's\n  // valid for `nextHopProtocol` to be an empty string.\n  if (resourceTiming.nextHopProtocol != undefined) {\n    const { name, version } = extractNetworkProtocol(resourceTiming.nextHopProtocol);\n    timingSpanData.push(['network.protocol.version', version], ['network.protocol.name', name]);\n  }\n  if (!browserPerformanceTimeOrigin()) {\n    return timingSpanData;\n  }\n  return [\n    ...timingSpanData,\n    ['http.request.redirect_start', getAbsoluteTime(resourceTiming.redirectStart)],\n    ['http.request.fetch_start', getAbsoluteTime(resourceTiming.fetchStart)],\n    ['http.request.domain_lookup_start', getAbsoluteTime(resourceTiming.domainLookupStart)],\n    ['http.request.domain_lookup_end', getAbsoluteTime(resourceTiming.domainLookupEnd)],\n    ['http.request.connect_start', getAbsoluteTime(resourceTiming.connectStart)],\n    ['http.request.secure_connection_start', getAbsoluteTime(resourceTiming.secureConnectionStart)],\n    ['http.request.connection_end', getAbsoluteTime(resourceTiming.connectEnd)],\n    ['http.request.request_start', getAbsoluteTime(resourceTiming.requestStart)],\n    ['http.request.response_start', getAbsoluteTime(resourceTiming.responseStart)],\n    ['http.request.response_end', getAbsoluteTime(resourceTiming.responseEnd)],\n  ];\n}\n"], "names": [], "mappings": ";;;;;;;AAIA,SAAS,eAAe,CAAC,IAAI,GAAW,CAAC,EAAU;IACjD,OAAO,CAAC,mKAAC,+BAAA,AAA4B,EAAC,KAAK,WAAW,CAAC,UAAU,IAAI,IAAI,IAAI,IAAI;AACnF;AAEA;;;;;CAKA,GACO,SAAS,8BAA8B,CAC5C,cAAc;IAEd,MAAM,cAAc,GAA4C,EAAE;IACpE,uEAAA;IACA,qDAAA;IACE,IAAI,cAAc,CAAC,eAAA,IAAmB,SAAS,EAAE;QAC/C,MAAM,EAAE,IAAI,EAAE,OAAA,EAAQ,GAAI,sNAAA,AAAsB,EAAC,cAAc,CAAC,eAAe,CAAC;QAChF,cAAc,CAAC,IAAI,CAAC;YAAC,0BAA0B;YAAE,OAAO;SAAC,EAAE;YAAC,uBAAuB;YAAE,IAAI;SAAC,CAAC;IAC/F;IACE,IAAI,mKAAC,+BAAA,AAA4B,EAAE,GAAE;QACnC,OAAO,cAAc;IACzB;IACE,OAAO;WACF,cAAc;QACjB;YAAC,6BAA6B;YAAE,eAAe,CAAC,cAAc,CAAC,aAAa,CAAC;SAAC;QAC9E;YAAC,0BAA0B;YAAE,eAAe,CAAC,cAAc,CAAC,UAAU,CAAC;SAAC;QACxE;YAAC,kCAAkC;YAAE,eAAe,CAAC,cAAc,CAAC,iBAAiB,CAAC;SAAC;QACvF;YAAC,gCAAgC;YAAE,eAAe,CAAC,cAAc,CAAC,eAAe,CAAC;SAAC;QACnF;YAAC,4BAA4B;YAAE,eAAe,CAAC,cAAc,CAAC,YAAY,CAAC;SAAC;QAC5E;YAAC,sCAAsC;YAAE,eAAe,CAAC,cAAc,CAAC,qBAAqB,CAAC;SAAC;QAC/F;YAAC,6BAA6B;YAAE,eAAe,CAAC,cAAc,CAAC,UAAU,CAAC;SAAC;QAC3E;YAAC,4BAA4B;YAAE,eAAe,CAAC,cAAc,CAAC,YAAY,CAAC;SAAC;QAC5E;YAAC,6BAA6B;YAAE,eAAe,CAAC,cAAc,CAAC,aAAa,CAAC;SAAC;QAC9E;YAAC,2BAA2B;YAAE,eAAe,CAAC,cAAc,CAAC,WAAW,CAAC;SAAC;KAC3E;AACH", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2219, "column": 0}, "map": {"version": 3, "file": "request.js", "sources": ["file:///C:/Users/<USER>/Documents/File%20Kerja%20Daffa/GAWEAN/TERANG_LMS/terang-lms-frontend/node_modules/%40sentry/browser/src/tracing/request.ts"], "sourcesContent": ["import type { <PERSON><PERSON>, Handler<PERSON>ata<PERSON>hr, SentryWrappedXMLHttpRequest, Span, WebFetchHeaders } from '@sentry/core';\nimport {\n  addFetchEndInstrumentationHandler,\n  addFetchInstrumentationHandler,\n  getActiveSpan,\n  getClient,\n  getLocationHref,\n  getTraceData,\n  hasSpansEnabled,\n  instrumentFetchRequest,\n  parseUrl,\n  SEMANTIC_ATTRIBUTE_SENTRY_OP,\n  SEMANTIC_ATTRIBUTE_SENTRY_ORIGIN,\n  SentryNonRecordingSpan,\n  setHttpStatus,\n  spanToJSON,\n  startInactiveSpan,\n  stringMatchesSomePattern,\n  stripUrlQueryAndFragment,\n} from '@sentry/core';\nimport type { XhrHint } from '@sentry-internal/browser-utils';\nimport {\n  addPerformanceInstrumentationHandler,\n  addXhrInstrumentationHandler,\n  SENTRY_XHR_DATA_KEY,\n} from '@sentry-internal/browser-utils';\nimport { WINDOW } from '../helpers';\nimport { resourceTimingToSpanAttributes } from './resource-timing';\n\n/** Options for Request Instrumentation */\nexport interface RequestInstrumentationOptions {\n  /**\n   * List of strings and/or Regular Expressions used to determine which outgoing requests will have `sentry-trace` and `baggage`\n   * headers attached.\n   *\n   * **Default:** If this option is not provided, tracing headers will be attached to all outgoing requests.\n   * If you are using a browser SDK, by default, tracing headers will only be attached to outgoing requests to the same origin.\n   *\n   * **Disclaimer:** Carelessly setting this option in browser environments may result into CORS errors!\n   * Only attach tracing headers to requests to the same origin, or to requests to services you can control CORS headers of.\n   * Cross-origin requests, meaning requests to a different domain, for example a request to `https://api.example.com/` while you're on `https://example.com/`, take special care.\n   * If you are attaching headers to cross-origin requests, make sure the backend handling the request returns a `\"Access-Control-Allow-Headers: sentry-trace, baggage\"` header to ensure your requests aren't blocked.\n   *\n   * If you provide a `tracePropagationTargets` array, the entries you provide will be matched against the entire URL of the outgoing request.\n   * If you are using a browser SDK, the entries will also be matched against the pathname of the outgoing requests.\n   * This is so you can have matchers for relative requests, for example, `/^\\/api/` if you want to trace requests to your `/api` routes on the same domain.\n   *\n   * If any of the two match any of the provided values, tracing headers will be attached to the outgoing request.\n   * Both, the string values, and the RegExes you provide in the array will match if they partially match the URL or pathname.\n   *\n   * Examples:\n   * - `tracePropagationTargets: [/^\\/api/]` and request to `https://same-origin.com/api/posts`:\n   *   - Tracing headers will be attached because the request is sent to the same origin and the regex matches the pathname \"/api/posts\".\n   * - `tracePropagationTargets: [/^\\/api/]` and request to `https://different-origin.com/api/posts`:\n   *   - Tracing headers will not be attached because the pathname will only be compared when the request target lives on the same origin.\n   * - `tracePropagationTargets: [/^\\/api/, 'https://external-api.com']` and request to `https://external-api.com/v1/data`:\n   *   - Tracing headers will be attached because the request URL matches the string `'https://external-api.com'`.\n   */\n  tracePropagationTargets?: Array<string | RegExp>;\n\n  /**\n   * Flag to disable patching all together for fetch requests.\n   *\n   * Default: true\n   */\n  traceFetch: boolean;\n\n  /**\n   * Flag to disable patching all together for xhr requests.\n   *\n   * Default: true\n   */\n  traceXHR: boolean;\n\n  /**\n   * Flag to disable tracking of long-lived streams, like server-sent events (SSE) via fetch.\n   * Do not enable this in case you have live streams or very long running streams.\n   *\n   * Disabled by default since it can lead to issues with streams using the `cancel()` api\n   * (https://github.com/getsentry/sentry-javascript/issues/13950)\n   *\n   * Default: false\n   */\n  trackFetchStreamPerformance: boolean;\n\n  /**\n   * If true, Sentry will capture http timings and add them to the corresponding http spans.\n   *\n   * Default: true\n   */\n  enableHTTPTimings: boolean;\n\n  /**\n   * This function will be called before creating a span for a request with the given url.\n   * Return false if you don't want a span for the given url.\n   *\n   * Default: (url: string) => true\n   */\n  shouldCreateSpanForRequest?(this: void, url: string): boolean;\n\n  /**\n   * Is called when spans are started for outgoing requests.\n   */\n  onRequestSpanStart?(span: Span, requestInformation: { headers?: WebFetchHeaders }): void;\n}\n\nconst responseToSpanId = new WeakMap<object, string>();\nconst spanIdToEndTimestamp = new Map<string, number>();\n\nexport const defaultRequestInstrumentationOptions: RequestInstrumentationOptions = {\n  traceFetch: true,\n  traceXHR: true,\n  enableHTTPTimings: true,\n  trackFetchStreamPerformance: false,\n};\n\n/** Registers span creators for xhr and fetch requests  */\nexport function instrumentOutgoingRequests(client: Client, _options?: Partial<RequestInstrumentationOptions>): void {\n  const {\n    traceFetch,\n    traceXHR,\n    trackFetchStreamPerformance,\n    shouldCreateSpanForRequest,\n    enableHTTPTimings,\n    tracePropagationTargets,\n    onRequestSpanStart,\n  } = {\n    ...defaultRequestInstrumentationOptions,\n    ..._options,\n  };\n\n  const shouldCreateSpan =\n    typeof shouldCreateSpanForRequest === 'function' ? shouldCreateSpanForRequest : (_: string) => true;\n\n  const shouldAttachHeadersWithTargets = (url: string): boolean => shouldAttachHeaders(url, tracePropagationTargets);\n\n  const spans: Record<string, Span> = {};\n\n  if (traceFetch) {\n    // Keeping track of http requests, whose body payloads resolved later than the initial resolved request\n    // e.g. streaming using server sent events (SSE)\n    client.addEventProcessor(event => {\n      if (event.type === 'transaction' && event.spans) {\n        event.spans.forEach(span => {\n          if (span.op === 'http.client') {\n            const updatedTimestamp = spanIdToEndTimestamp.get(span.span_id);\n            if (updatedTimestamp) {\n              span.timestamp = updatedTimestamp / 1000;\n              spanIdToEndTimestamp.delete(span.span_id);\n            }\n          }\n        });\n      }\n      return event;\n    });\n\n    if (trackFetchStreamPerformance) {\n      addFetchEndInstrumentationHandler(handlerData => {\n        if (handlerData.response) {\n          const span = responseToSpanId.get(handlerData.response);\n          if (span && handlerData.endTimestamp) {\n            spanIdToEndTimestamp.set(span, handlerData.endTimestamp);\n          }\n        }\n      });\n    }\n\n    addFetchInstrumentationHandler(handlerData => {\n      const createdSpan = instrumentFetchRequest(handlerData, shouldCreateSpan, shouldAttachHeadersWithTargets, spans);\n\n      if (handlerData.response && handlerData.fetchData.__span) {\n        responseToSpanId.set(handlerData.response, handlerData.fetchData.__span);\n      }\n\n      // We cannot use `window.location` in the generic fetch instrumentation,\n      // but we need it for reliable `server.address` attribute.\n      // so we extend this in here\n      if (createdSpan) {\n        const fullUrl = getFullURL(handlerData.fetchData.url);\n        const host = fullUrl ? parseUrl(fullUrl).host : undefined;\n        createdSpan.setAttributes({\n          'http.url': fullUrl,\n          'server.address': host,\n        });\n\n        if (enableHTTPTimings) {\n          addHTTPTimings(createdSpan);\n        }\n\n        onRequestSpanStart?.(createdSpan, { headers: handlerData.headers });\n      }\n    });\n  }\n\n  if (traceXHR) {\n    addXhrInstrumentationHandler(handlerData => {\n      const createdSpan = xhrCallback(handlerData, shouldCreateSpan, shouldAttachHeadersWithTargets, spans);\n      if (createdSpan) {\n        if (enableHTTPTimings) {\n          addHTTPTimings(createdSpan);\n        }\n\n        let headers;\n        try {\n          headers = new Headers(handlerData.xhr.__sentry_xhr_v3__?.request_headers);\n        } catch {\n          // noop\n        }\n        onRequestSpanStart?.(createdSpan, { headers });\n      }\n    });\n  }\n}\n\nfunction isPerformanceResourceTiming(entry: PerformanceEntry): entry is PerformanceResourceTiming {\n  return (\n    entry.entryType === 'resource' &&\n    'initiatorType' in entry &&\n    typeof (entry as PerformanceResourceTiming).nextHopProtocol === 'string' &&\n    (entry.initiatorType === 'fetch' || entry.initiatorType === 'xmlhttprequest')\n  );\n}\n\n/**\n * Creates a temporary observer to listen to the next fetch/xhr resourcing timings,\n * so that when timings hit their per-browser limit they don't need to be removed.\n *\n * @param span A span that has yet to be finished, must contain `url` on data.\n */\nfunction addHTTPTimings(span: Span): void {\n  const { url } = spanToJSON(span).data;\n\n  if (!url || typeof url !== 'string') {\n    return;\n  }\n\n  const cleanup = addPerformanceInstrumentationHandler('resource', ({ entries }) => {\n    entries.forEach(entry => {\n      if (isPerformanceResourceTiming(entry) && entry.name.endsWith(url)) {\n        const spanAttributes = resourceTimingToSpanAttributes(entry);\n        spanAttributes.forEach(attributeArray => span.setAttribute(...attributeArray));\n        // In the next tick, clean this handler up\n        // We have to wait here because otherwise this cleans itself up before it is fully done\n        setTimeout(cleanup);\n      }\n    });\n  });\n}\n\n/**\n * A function that determines whether to attach tracing headers to a request.\n * We only export this function for testing purposes.\n */\nexport function shouldAttachHeaders(\n  targetUrl: string,\n  tracePropagationTargets: (string | RegExp)[] | undefined,\n): boolean {\n  // window.location.href not being defined is an edge case in the browser but we need to handle it.\n  // Potentially dangerous situations where it may not be defined: Browser Extensions, Web Workers, patching of the location obj\n  const href = getLocationHref();\n\n  if (!href) {\n    // If there is no window.location.origin, we default to only attaching tracing headers to relative requests, i.e. ones that start with `/`\n    // BIG DISCLAIMER: Users can call URLs with a double slash (fetch(\"//example.com/api\")), this is a shorthand for \"send to the same protocol\",\n    // so we need a to exclude those requests, because they might be cross origin.\n    const isRelativeSameOriginRequest = !!targetUrl.match(/^\\/(?!\\/)/);\n    if (!tracePropagationTargets) {\n      return isRelativeSameOriginRequest;\n    } else {\n      return stringMatchesSomePattern(targetUrl, tracePropagationTargets);\n    }\n  } else {\n    let resolvedUrl;\n    let currentOrigin;\n\n    // URL parsing may fail, we default to not attaching trace headers in that case.\n    try {\n      resolvedUrl = new URL(targetUrl, href);\n      currentOrigin = new URL(href).origin;\n    } catch {\n      return false;\n    }\n\n    const isSameOriginRequest = resolvedUrl.origin === currentOrigin;\n    if (!tracePropagationTargets) {\n      return isSameOriginRequest;\n    } else {\n      return (\n        stringMatchesSomePattern(resolvedUrl.toString(), tracePropagationTargets) ||\n        (isSameOriginRequest && stringMatchesSomePattern(resolvedUrl.pathname, tracePropagationTargets))\n      );\n    }\n  }\n}\n\n/**\n * Create and track xhr request spans\n *\n * @returns Span if a span was created, otherwise void.\n */\nexport function xhrCallback(\n  handlerData: HandlerDataXhr,\n  shouldCreateSpan: (url: string) => boolean,\n  shouldAttachHeaders: (url: string) => boolean,\n  spans: Record<string, Span>,\n): Span | undefined {\n  const xhr = handlerData.xhr;\n  const sentryXhrData = xhr?.[SENTRY_XHR_DATA_KEY];\n\n  if (!xhr || xhr.__sentry_own_request__ || !sentryXhrData) {\n    return undefined;\n  }\n\n  const { url, method } = sentryXhrData;\n\n  const shouldCreateSpanResult = hasSpansEnabled() && shouldCreateSpan(url);\n\n  // check first if the request has finished and is tracked by an existing span which should now end\n  if (handlerData.endTimestamp && shouldCreateSpanResult) {\n    const spanId = xhr.__sentry_xhr_span_id__;\n    if (!spanId) return;\n\n    const span = spans[spanId];\n    if (span && sentryXhrData.status_code !== undefined) {\n      setHttpStatus(span, sentryXhrData.status_code);\n      span.end();\n\n      // eslint-disable-next-line @typescript-eslint/no-dynamic-delete\n      delete spans[spanId];\n    }\n    return undefined;\n  }\n\n  const fullUrl = getFullURL(url);\n  const parsedUrl = fullUrl ? parseUrl(fullUrl) : parseUrl(url);\n\n  const urlForSpanName = stripUrlQueryAndFragment(url);\n\n  const hasParent = !!getActiveSpan();\n\n  const span =\n    shouldCreateSpanResult && hasParent\n      ? startInactiveSpan({\n          name: `${method} ${urlForSpanName}`,\n          attributes: {\n            url,\n            type: 'xhr',\n            'http.method': method,\n            'http.url': fullUrl,\n            'server.address': parsedUrl?.host,\n            [SEMANTIC_ATTRIBUTE_SENTRY_ORIGIN]: 'auto.http.browser',\n            [SEMANTIC_ATTRIBUTE_SENTRY_OP]: 'http.client',\n            ...(parsedUrl?.search && { 'http.query': parsedUrl?.search }),\n            ...(parsedUrl?.hash && { 'http.fragment': parsedUrl?.hash }),\n          },\n        })\n      : new SentryNonRecordingSpan();\n\n  xhr.__sentry_xhr_span_id__ = span.spanContext().spanId;\n  spans[xhr.__sentry_xhr_span_id__] = span;\n\n  if (shouldAttachHeaders(url)) {\n    addTracingHeadersToXhrRequest(\n      xhr,\n      // If performance is disabled (TWP) or there's no active root span (pageload/navigation/interaction),\n      // we do not want to use the span as base for the trace headers,\n      // which means that the headers will be generated from the scope and the sampling decision is deferred\n      hasSpansEnabled() && hasParent ? span : undefined,\n    );\n  }\n\n  const client = getClient();\n  if (client) {\n    client.emit('beforeOutgoingRequestSpan', span, handlerData as XhrHint);\n  }\n\n  return span;\n}\n\nfunction addTracingHeadersToXhrRequest(xhr: SentryWrappedXMLHttpRequest, span?: Span): void {\n  const { 'sentry-trace': sentryTrace, baggage } = getTraceData({ span });\n\n  if (sentryTrace) {\n    setHeaderOnXhr(xhr, sentryTrace, baggage);\n  }\n}\n\nfunction setHeaderOnXhr(\n  xhr: SentryWrappedXMLHttpRequest,\n  sentryTraceHeader: string,\n  sentryBaggageHeader: string | undefined,\n): void {\n  const originalHeaders = xhr.__sentry_xhr_v3__?.request_headers;\n\n  if (originalHeaders?.['sentry-trace']) {\n    // bail if a sentry-trace header is already set\n    return;\n  }\n\n  try {\n    // eslint-disable-next-line @typescript-eslint/no-non-null-assertion\n    xhr.setRequestHeader!('sentry-trace', sentryTraceHeader);\n    if (sentryBaggageHeader) {\n      // only add our headers if\n      // - no pre-existing baggage header exists\n      // - or it is set and doesn't yet contain sentry values\n      const originalBaggageHeader = originalHeaders?.['baggage'];\n      if (!originalBaggageHeader || !baggageHeaderHasSentryValues(originalBaggageHeader)) {\n        // From MDN: \"If this method is called several times with the same header, the values are merged into one single request header.\"\n        // We can therefore simply set a baggage header without checking what was there before\n        // https://developer.mozilla.org/en-US/docs/Web/API/XMLHttpRequest/setRequestHeader\n        // eslint-disable-next-line @typescript-eslint/no-non-null-assertion\n        xhr.setRequestHeader!('baggage', sentryBaggageHeader);\n      }\n    }\n  } catch {\n    // Error: InvalidStateError: Failed to execute 'setRequestHeader' on 'XMLHttpRequest': The object's state must be OPENED.\n  }\n}\n\nfunction baggageHeaderHasSentryValues(baggageHeader: string): boolean {\n  return baggageHeader.split(',').some(value => value.trim().startsWith('sentry-'));\n}\n\nfunction getFullURL(url: string): string | undefined {\n  try {\n    // By adding a base URL to new URL(), this will also work for relative urls\n    // If `url` is a full URL, the base URL is ignored anyhow\n    const parsed = new URL(url, WINDOW.location.origin);\n    return parsed.href;\n  } catch {\n    return undefined;\n  }\n}\n"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;AA6BA,wCAAA,GA6EA,MAAM,gBAAA,GAAmB,IAAI,OAAO,EAAkB;AACtD,MAAM,oBAAA,GAAuB,IAAI,GAAG,EAAkB;AAE/C,MAAM,oCAAoC,GAAkC;IACjF,UAAU,EAAE,IAAI;IAChB,QAAQ,EAAE,IAAI;IACd,iBAAiB,EAAE,IAAI;IACvB,2BAA2B,EAAE,KAAK;AACpC;AAEA,wDAAA,GACO,SAAS,0BAA0B,CAAC,MAAM,EAAU,QAAQ,EAAiD;IAClH,MAAM,EACJ,UAAU,EACV,QAAQ,EACR,2BAA2B,EAC3B,0BAA0B,EAC1B,iBAAiB,EACjB,uBAAuB,EACvB,kBAAkB,EACtB,GAAM;QACF,GAAG,oCAAoC;QACvC,GAAG,QAAQ;IACf,CAAG;IAED,MAAM,gBAAA,GACJ,OAAO,0BAAA,KAA+B,UAAA,GAAa,0BAAA,GAA6B,CAAC,CAAC,GAAa,IAAI;IAErG,MAAM,8BAAA,GAAiC,CAAC,GAAG,GAAsB,mBAAmB,CAAC,GAAG,EAAE,uBAAuB,CAAC;IAElH,MAAM,KAAK,GAAyB,CAAA,CAAE;IAEtC,IAAI,UAAU,EAAE;QAClB,uGAAA;QACA,gDAAA;QACI,MAAM,CAAC,iBAAiB,CAAC,SAAS;YAChC,IAAI,KAAK,CAAC,IAAA,KAAS,aAAA,IAAiB,KAAK,CAAC,KAAK,EAAE;gBAC/C,KAAK,CAAC,KAAK,CAAC,OAAO,EAAC,QAAQ;oBAC1B,IAAI,IAAI,CAAC,EAAA,KAAO,aAAa,EAAE;wBAC7B,MAAM,gBAAA,GAAmB,oBAAoB,CAAC,GAAG,CAAC,IAAI,CAAC,OAAO,CAAC;wBAC/D,IAAI,gBAAgB,EAAE;4BACpB,IAAI,CAAC,SAAA,GAAY,gBAAA,GAAmB,IAAI;4BACxC,oBAAoB,CAAC,MAAM,CAAC,IAAI,CAAC,OAAO,CAAC;wBACvD;oBACA;gBACA,CAAS,CAAC;YACV;YACM,OAAO,KAAK;QAClB,CAAK,CAAC;QAEF,IAAI,2BAA2B,EAAE;oLAC/B,oCAAA,AAAiC,GAAC,WAAA,IAAe;gBAC/C,IAAI,WAAW,CAAC,QAAQ,EAAE;oBACxB,MAAM,IAAA,GAAO,gBAAgB,CAAC,GAAG,CAAC,WAAW,CAAC,QAAQ,CAAC;oBACvD,IAAI,IAAA,IAAQ,WAAW,CAAC,YAAY,EAAE;wBACpC,oBAAoB,CAAC,GAAG,CAAC,IAAI,EAAE,WAAW,CAAC,YAAY,CAAC;oBACpE;gBACA;YACA,CAAO,CAAC;QACR;gLAEI,iCAAA,AAA8B,GAAC,WAAA,IAAe;YAC5C,MAAM,WAAA,6JAAc,yBAAA,AAAsB,EAAC,WAAW,EAAE,gBAAgB,EAAE,8BAA8B,EAAE,KAAK,CAAC;YAEhH,IAAI,WAAW,CAAC,QAAA,IAAY,WAAW,CAAC,SAAS,CAAC,MAAM,EAAE;gBACxD,gBAAgB,CAAC,GAAG,CAAC,WAAW,CAAC,QAAQ,EAAE,WAAW,CAAC,SAAS,CAAC,MAAM,CAAC;YAChF;YAEA,wEAAA;YACA,0DAAA;YACA,4BAAA;YACM,IAAI,WAAW,EAAE;gBACf,MAAM,OAAA,GAAU,UAAU,CAAC,WAAW,CAAC,SAAS,CAAC,GAAG,CAAC;gBACrD,MAAM,IAAA,GAAO,OAAA,GAAU,4KAAA,AAAQ,EAAC,OAAO,CAAC,CAAC,IAAA,GAAO,SAAS;gBACzD,WAAW,CAAC,aAAa,CAAC;oBACxB,UAAU,EAAE,OAAO;oBACnB,gBAAgB,EAAE,IAAI;gBAChC,CAAS,CAAC;gBAEF,IAAI,iBAAiB,EAAE;oBACrB,cAAc,CAAC,WAAW,CAAC;gBACrC;gBAEQ,kBAAkB,GAAG,WAAW,EAAE;oBAAE,OAAO,EAAE,WAAW,CAAC,OAAA;gBAAA,CAAS,CAAC;YAC3E;QACA,CAAK,CAAC;IACN;IAEE,IAAI,QAAQ,EAAE;YACZ,yNAAA,AAA4B,GAAC,WAAA,IAAe;YAC1C,MAAM,WAAA,GAAc,WAAW,CAAC,WAAW,EAAE,gBAAgB,EAAE,8BAA8B,EAAE,KAAK,CAAC;YACrG,IAAI,WAAW,EAAE;gBACf,IAAI,iBAAiB,EAAE;oBACrB,cAAc,CAAC,WAAW,CAAC;gBACrC;gBAEQ,IAAI,OAAO;gBACX,IAAI;oBACF,OAAA,GAAU,IAAI,OAAO,CAAC,WAAW,CAAC,GAAG,CAAC,iBAAiB,EAAE,eAAe,CAAC;gBACnF,EAAU,OAAM;gBAChB,OAAA;gBACA;gBACQ,kBAAkB,GAAG,WAAW,EAAE;oBAAE,OAAA;gBAAA,CAAS,CAAC;YACtD;QACA,CAAK,CAAC;IACN;AACA;AAEA,SAAS,2BAA2B,CAAC,KAAK,EAAwD;IAChG,OACE,KAAK,CAAC,SAAA,KAAc,UAAA,IACpB,eAAA,IAAmB,KAAA,IACnB,OAAO,AAAC,KAAA,CAAoC,eAAA,KAAoB,QAAA,IACpE,CAAK,KAAK,CAAC,aAAA,KAAkB,OAAA,IAAW,KAAK,CAAC,aAAA,KAAkB,gBAAgB;AAEhF;AAEA;;;;;CAKA,GACA,SAAS,cAAc,CAAC,IAAI,EAAc;IACxC,MAAM,EAAE,GAAA,EAAI,0KAAI,aAAA,AAAU,EAAC,IAAI,CAAC,CAAC,IAAI;IAErC,IAAI,CAAC,GAAA,IAAO,OAAO,GAAA,KAAQ,QAAQ,EAAE;QACnC;IACJ;IAEE,MAAM,OAAA,qMAAU,uCAAA,AAAoC,EAAC,UAAU,EAAE,CAAC,EAAE,OAAA,EAAS,KAAK;QAChF,OAAO,CAAC,OAAO,EAAC,SAAS;YACvB,IAAI,2BAA2B,CAAC,KAAK,CAAA,IAAK,KAAK,CAAC,IAAI,CAAC,QAAQ,CAAC,GAAG,CAAC,EAAE;gBAClE,MAAM,cAAA,+LAAiB,iCAAA,AAA8B,EAAC,KAAK,CAAC;gBAC5D,cAAc,CAAC,OAAO,EAAC,cAAA,GAAkB,IAAI,CAAC,YAAY,CAAC,GAAG,cAAc,CAAC,CAAC;gBACtF,0CAAA;gBACA,uFAAA;gBACQ,UAAU,CAAC,OAAO,CAAC;YAC3B;QACA,CAAK,CAAC;IACN,CAAG,CAAC;AACJ;AAEA;;;CAGA,GACO,SAAS,mBAAmB,CACjC,SAAS,EACT,uBAAuB;IAEzB,kGAAA;IACA,8HAAA;IACE,MAAM,IAAA,GAAO,uLAAA,AAAe,EAAE;IAE9B,IAAI,CAAC,IAAI,EAAE;QACb,0IAAA;QACA,6IAAA;QACA,8EAAA;QACI,MAAM,2BAAA,GAA8B,CAAC,CAAC,SAAS,CAAC,KAAK,CAAC,WAAW,CAAC;QAClE,IAAI,CAAC,uBAAuB,EAAE;YAC5B,OAAO,2BAA2B;QACxC,OAAW;YACL,2KAAO,2BAAA,AAAwB,EAAC,SAAS,EAAE,uBAAuB,CAAC;QACzE;IACA,OAAS;QACL,IAAI,WAAW;QACf,IAAI,aAAa;QAErB,gFAAA;QACI,IAAI;YACF,WAAA,GAAc,IAAI,GAAG,CAAC,SAAS,EAAE,IAAI,CAAC;YACtC,aAAA,GAAgB,IAAI,GAAG,CAAC,IAAI,CAAC,CAAC,MAAM;QAC1C,EAAM,OAAM;YACN,OAAO,KAAK;QAClB;QAEI,MAAM,mBAAA,GAAsB,WAAW,CAAC,MAAA,KAAW,aAAa;QAChE,IAAI,CAAC,uBAAuB,EAAE;YAC5B,OAAO,mBAAmB;QAChC,OAAW;YACL,QACE,8LAAA,AAAwB,EAAC,WAAW,CAAC,QAAQ,EAAE,EAAE,uBAAuB,CAAA,IACvE,mBAAA,wKAAuB,2BAAA,AAAwB,EAAC,WAAW,CAAC,QAAQ,EAAE,uBAAuB,CAAC;QAEvG;IACA;AACA;AAEA;;;;CAIA,GACO,SAAS,WAAW,CACzB,WAAW,EACX,gBAAgB,EAChB,mBAAmB,EACnB,KAAK;IAEL,MAAM,GAAA,GAAM,WAAW,CAAC,GAAG;IAC3B,MAAM,aAAA,GAAgB,GAAG,EAAA,2LAAG,sBAAmB,CAAC;IAEhD,IAAI,CAAC,GAAA,IAAO,GAAG,CAAC,sBAAA,IAA0B,CAAC,aAAa,EAAE;QACxD,OAAO,SAAS;IACpB;IAEE,MAAM,EAAE,GAAG,EAAE,MAAA,EAAO,GAAI,aAAa;IAErC,MAAM,sBAAA,gLAAyB,kBAAA,AAAe,OAAM,gBAAgB,CAAC,GAAG,CAAC;IAE3E,kGAAA;IACE,IAAI,WAAW,CAAC,YAAA,IAAgB,sBAAsB,EAAE;QACtD,MAAM,MAAA,GAAS,GAAG,CAAC,sBAAsB;QACzC,IAAI,CAAC,MAAM,EAAE;QAEb,MAAM,IAAA,GAAO,KAAK,CAAC,MAAM,CAAC;QAC1B,IAAI,IAAA,IAAQ,aAAa,CAAC,WAAA,KAAgB,SAAS,EAAE;sLACnD,gBAAA,AAAa,EAAC,IAAI,EAAE,aAAa,CAAC,WAAW,CAAC;YAC9C,IAAI,CAAC,GAAG,EAAE;YAEhB,gEAAA;YACM,OAAO,KAAK,CAAC,MAAM,CAAC;QAC1B;QACI,OAAO,SAAS;IACpB;IAEE,MAAM,OAAA,GAAU,UAAU,CAAC,GAAG,CAAC;IAC/B,MAAM,SAAA,GAAY,OAAA,oKAAU,WAAA,AAAQ,EAAC,OAAO,CAAA,oKAAI,WAAA,AAAQ,EAAC,GAAG,CAAC;IAE7D,MAAM,cAAA,IAAiB,2LAAA,AAAwB,EAAC,GAAG,CAAC;IAEpD,MAAM,SAAA,GAAY,CAAC,wKAAC,gBAAA,AAAa,EAAE;IAEnC,MAAM,IAAA,GACJ,0BAA0B,iLACtB,oBAAA,AAAiB,EAAC;QAChB,IAAI,EAAE,CAAC,EAAA,MAAA,CAAA,CAAA,EAAA,cAAA,CAAA,CAAA;QACA,UAAA,EAAA;YACA,GAAA;YACA,IAAA,EAAA,KAAA;YACA,aAAA,EAAA,MAAA;YACA,UAAA,EAAA,OAAA;YACA,gBAAA,EAAA,SAAA,EAAA,IAAA;YACA,oKAAA,mCAAA,CAAA,EAAA,mBAAA;YACA,oKAAA,+BAAA,CAAA,EAAA,aAAA;YACA,GAAA,SAAA,EAAA,MAAA,IAAA;gBAAA,YAAA,EAAA,SAAA,EAAA,MAAA;YAAA,CAAA,CAAA;YACA,GAAA,SAAA,EAAA,IAAA,IAAA;gBAAA,eAAA,EAAA,SAAA,EAAA,IAAA;YAAA,CAAA,CAAA;QACA,CAAA;IACA,CAAA,IACA,sLAAA,yBAAA,EAAA;IAEA,GAAA,CAAA,sBAAA,GAAA,IAAA,CAAA,WAAA,EAAA,CAAA,MAAA;IACA,KAAA,CAAA,GAAA,CAAA,sBAAA,CAAA,GAAA,IAAA;IAEA,IAAA,mBAAA,CAAA,GAAA,CAAA,EAAA;QACA,6BAAA,CACA,GAAA,EACA,qGAAA;QACA,gEAAA;QACA,sGAAA;QACA,+LAAA,EAAA,KAAA,SAAA,GAAA,IAAA,GAAA,SAAA;IAEA;IAEA,MAAA,MAAA,qKAAA,YAAA,EAAA;IACA,IAAA,MAAA,EAAA;QACA,MAAA,CAAA,IAAA,CAAA,2BAAA,EAAA,IAAA,EAAA,WAAA,EAAA;IACA;IAEA,OAAA,IAAA;AACA;AAEA,SAAA,6BAAA,CAAA,GAAA,EAAA,IAAA,EAAA;IACA,MAAA,EAAA,cAAA,EAAA,WAAA,EAAA,OAAA,EAAA,0KAAA,eAAA,EAAA;QAAA,IAAA;IAAA,CAAA,CAAA;IAEA,IAAA,WAAA,EAAA;QACA,cAAA,CAAA,GAAA,EAAA,WAAA,EAAA,OAAA,CAAA;IACA;AACA;AAEA,SAAA,cAAA,CACA,GAAA,EACA,iBAAA,EACA,mBAAA;IAEA,MAAA,eAAA,GAAA,GAAA,CAAA,iBAAA,EAAA,eAAA;IAEA,IAAA,eAAA,EAAA,CAAA,cAAA,CAAA,EAAA;QACA,+CAAA;QACA;IACA;IAEA,IAAA;QACA,oEAAA;QACA,GAAA,CAAA,gBAAA,CAAA,cAAA,EAAA,iBAAA,CAAA;QACA,IAAA,mBAAA,EAAA;YACA,0BAAA;YACA,0CAAA;YACA,uDAAA;YACA,MAAA,qBAAA,GAAA,eAAA,EAAA,CAAA,SAAA,CAAA;YACA,IAAA,CAAA,qBAAA,IAAA,CAAA,4BAAA,CAAA,qBAAA,CAAA,EAAA;gBACA,iIAAA;gBACA,sFAAA;gBACA,mFAAA;gBACA,oEAAA;gBACA,GAAA,CAAA,gBAAA,CAAA,SAAA,EAAA,mBAAA,CAAA;YACA;QACA;IACA,CAAA,CAAA,OAAA;IACA,yHAAA;IACA;AACA;AAEA,SAAA,4BAAA,CAAA,aAAA,EAAA;IACA,OAAA,aAAA,CAAA,KAAA,CAAA,GAAA,CAAA,CAAA,IAAA,EAAA,KAAA,GAAA,KAAA,CAAA,IAAA,EAAA,CAAA,UAAA,CAAA,SAAA,CAAA,CAAA;AACA;AAEA,SAAA,UAAA,CAAA,GAAA,EAAA;IACA,IAAA;QACA,2EAAA;QACA,yDAAA;QACA,MAAA,MAAA,GAAA,IAAA,GAAA,CAAA,GAAA,oKAAA,SAAA,CAAA,QAAA,CAAA,MAAA,CAAA;QACA,OAAA,MAAA,CAAA,IAAA;IACA,CAAA,CAAA,OAAA;QACA,OAAA,SAAA;IACA;AACA", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2509, "column": 0}, "map": {"version": 3, "file": "browserTracingIntegration.js", "sources": ["file:///C:/Users/<USER>/Documents/File%20Kerja%20Daffa/GAWEAN/TERANG_LMS/terang-lms-frontend/node_modules/%40sentry/browser/src/tracing/browserTracingIntegration.ts"], "sourcesContent": ["/* eslint-disable max-lines */\nimport type { Client, IntegrationFn, Span, StartSpanOptions, TransactionSource, WebFetchHeaders } from '@sentry/core';\nimport {\n  addNonEnumerableProperty,\n  browserPerformanceTimeOrigin,\n  dateTimestampInSeconds,\n  debug,\n  generateTraceId,\n  getClient,\n  getCurrentScope,\n  getDynamicSamplingContextFromSpan,\n  getIsolationScope,\n  getLocationHref,\n  GLOBAL_OBJ,\n  parseStringToURLObject,\n  propagationContextFromHeaders,\n  registerSpanErrorInstrumentation,\n  SEMANTIC_ATTRIBUTE_SENTRY_IDLE_SPAN_FINISH_REASON,\n  SEMANTIC_ATTRIBUTE_SENTRY_ORIGIN,\n  SEMANTIC_ATTRIBUTE_SENTRY_SOURCE,\n  spanIsSampled,\n  spanToJSON,\n  startIdleSpan,\n  startInactiveSpan,\n  timestampInSeconds,\n  TRACING_DEFAULTS,\n} from '@sentry/core';\nimport {\n  addHistoryInstrumentationHandler,\n  addPerformanceEntries,\n  registerInpInteractionListener,\n  startTrackingElementTiming,\n  startTrackingINP,\n  startTrackingInteractions,\n  startTrackingLongAnimationFrames,\n  startTrackingLongTasks,\n  startTrackingWebVitals,\n} from '@sentry-internal/browser-utils';\nimport { DEBUG_BUILD } from '../debug-build';\nimport { getHttpRequestData, WINDOW } from '../helpers';\nimport { registerBackgroundTabDetection } from './backgroundtab';\nimport { linkTraces } from './linkedTraces';\nimport { defaultRequestInstrumentationOptions, instrumentOutgoingRequests } from './request';\n\nexport const BROWSER_TRACING_INTEGRATION_ID = 'BrowserTracing';\n\ninterface RouteInfo {\n  name: string | undefined;\n  source: TransactionSource | undefined;\n}\n\n/** Options for Browser Tracing integration */\nexport interface BrowserTracingOptions {\n  /**\n   * The time that has to pass without any span being created.\n   * If this time is exceeded, the idle span will finish.\n   *\n   * Default: 1000 (ms)\n   */\n  idleTimeout: number;\n\n  /**\n   * The max. time an idle span may run.\n   * If this time is exceeded, the idle span will finish no matter what.\n   *\n   * Default: 30000 (ms)\n   */\n  finalTimeout: number;\n\n  /**\n   The max. time an idle span may run.\n   * If this time is exceeded, the idle span will finish no matter what.\n   *\n   * Default: 15000 (ms)\n   */\n  childSpanTimeout: number;\n\n  /**\n   * If a span should be created on page load.\n   * If this is set to `false`, this integration will not start the default page load span.\n   * Default: true\n   */\n  instrumentPageLoad: boolean;\n\n  /**\n   * If a span should be created on navigation (history change).\n   * If this is set to `false`, this integration will not start the default navigation spans.\n   * Default: true\n   */\n  instrumentNavigation: boolean;\n\n  /**\n   * Flag spans where tabs moved to background with \"cancelled\". Browser background tab timing is\n   * not suited towards doing precise measurements of operations. By default, we recommend that this option\n   * be enabled as background transactions can mess up your statistics in nondeterministic ways.\n   *\n   * Default: true\n   */\n  markBackgroundSpan: boolean;\n\n  /**\n   * If true, Sentry will capture long tasks and add them to the corresponding transaction.\n   *\n   * Default: true\n   */\n  enableLongTask: boolean;\n\n  /**\n   * If true, Sentry will capture long animation frames and add them to the corresponding transaction.\n   *\n   * Default: false\n   */\n  enableLongAnimationFrame: boolean;\n\n  /**\n   * If true, Sentry will capture first input delay and add it to the corresponding transaction.\n   *\n   * Default: true\n   */\n  enableInp: boolean;\n\n  /**\n   * If true, Sentry will capture [element timing](https://developer.mozilla.org/en-US/docs/Web/API/PerformanceElementTiming)\n   * information and add it to the corresponding transaction.\n   *\n   * Default: true\n   */\n  enableElementTiming: boolean;\n\n  /**\n   * Flag to disable patching all together for fetch requests.\n   *\n   * Default: true\n   */\n  traceFetch: boolean;\n\n  /**\n   * Flag to disable patching all together for xhr requests.\n   *\n   * Default: true\n   */\n  traceXHR: boolean;\n\n  /**\n   * Flag to disable tracking of long-lived streams, like server-sent events (SSE) via fetch.\n   * Do not enable this in case you have live streams or very long running streams.\n   *\n   * Default: false\n   */\n  trackFetchStreamPerformance: boolean;\n\n  /**\n   * If true, Sentry will capture http timings and add them to the corresponding http spans.\n   *\n   * Default: true\n   */\n  enableHTTPTimings: boolean;\n\n  /**\n   * Resource spans with `op`s matching strings in the array will not be emitted.\n   *\n   * Default: []\n   */\n  ignoreResourceSpans: Array<'resouce.script' | 'resource.css' | 'resource.img' | 'resource.other' | string>;\n\n  /**\n   * Spans created from the following browser Performance APIs,\n   *\n   * - [`performance.mark(...)`](https://developer.mozilla.org/en-US/docs/Web/API/Performance/mark)\n   * - [`performance.measure(...)`](https://developer.mozilla.org/en-US/docs/Web/API/Performance/measure)\n   *\n   * will not be emitted if their names match strings in this array.\n   *\n   * This is useful, if you come across `mark` or `measure` spans in your Sentry traces\n   * that you want to ignore. For example, sometimes, browser extensions or libraries\n   * emit these entries on their own, which might not be relevant to your application.\n   *\n   * * @example\n   * ```ts\n   * Sentry.init({\n   *   integrations: [\n   *     Sentry.browserTracingIntegration({\n   *      ignorePerformanceApiSpans: ['myMeasurement', /myMark/],\n   *     }),\n   *   ],\n   * });\n   *\n   * // no spans will be created for these:\n   * performance.mark('myMark');\n   * performance.measure('myMeasurement');\n   *\n   * // spans will be created for these:\n   * performance.mark('authenticated');\n   * performance.measure('input-duration', ...);\n   * ```\n   *\n   * Default: [] - By default, all `mark` and `measure` entries are sent as spans.\n   */\n  ignorePerformanceApiSpans: Array<string | RegExp>;\n\n  /**\n   * By default, the SDK will try to detect redirects and avoid creating separate spans for them.\n   * If you want to opt-out of this behavior, you can set this option to `false`.\n   *\n   * Default: true\n   */\n  detectRedirects: boolean;\n\n  /**\n   * Link the currently started trace to a previous trace (e.g. a prior pageload, navigation or\n   * manually started span). When enabled, this option will allow you to navigate between traces\n   * in the Sentry UI.\n   *\n   * You can set this option to the following values:\n   *\n   * - `'in-memory'`: The previous trace data will be stored in memory.\n   *   This is useful for single-page applications and enabled by default.\n   *\n   * - `'session-storage'`: The previous trace data will be stored in the `sessionStorage`.\n   *   This is useful for multi-page applications or static sites but it means that the\n   *   Sentry SDK writes to the browser's `sessionStorage`.\n   *\n   * - `'off'`: The previous trace data will not be stored or linked.\n   *\n   * You can also use {@link BrowserTracingOptions.consistentTraceSampling} to get\n   * consistent trace sampling of subsequent traces. Otherwise, by default, your\n   * `tracesSampleRate` or `tracesSampler` config significantly influences how often\n   * traces will be linked.\n   *\n   * @default 'in-memory' - see explanation above\n   */\n  linkPreviousTrace: 'in-memory' | 'session-storage' | 'off';\n\n  /**\n   * If true, Sentry will consistently sample subsequent traces based on the\n   * sampling decision of the initial trace. For example, if the initial page\n   * load trace was sampled positively, all subsequent traces (e.g. navigations)\n   * are also sampled positively. In case the initial trace was sampled negatively,\n   * all subsequent traces are also sampled negatively.\n   *\n   * This option allows you to get consistent, linked traces within a user journey\n   * while maintaining an overall quota based on your trace sampling settings.\n   *\n   * This option is only effective if {@link BrowserTracingOptions.linkPreviousTrace}\n   * is enabled (i.e. not set to `'off'`).\n   *\n   * @default `false` - this is an opt-in feature.\n   */\n  consistentTraceSampling: boolean;\n\n  /**\n   * _experiments allows the user to send options to define how this integration works.\n   *\n   * Default: undefined\n   */\n  _experiments: Partial<{\n    enableInteractions: boolean;\n    enableStandaloneClsSpans: boolean;\n    enableStandaloneLcpSpans: boolean;\n  }>;\n\n  /**\n   * A callback which is called before a span for a pageload or navigation is started.\n   * It receives the options passed to `startSpan`, and expects to return an updated options object.\n   */\n  beforeStartSpan?: (options: StartSpanOptions) => StartSpanOptions;\n\n  /**\n   * This function will be called before creating a span for a request with the given url.\n   * Return false if you don't want a span for the given url.\n   *\n   * Default: (url: string) => true\n   */\n  shouldCreateSpanForRequest?(this: void, url: string): boolean;\n\n  /**\n   * This callback is invoked directly after a span is started for an outgoing fetch or XHR request.\n   * You can use it to annotate the span with additional data or attributes, for example by setting\n   * attributes based on the passed request headers.\n   */\n  onRequestSpanStart?(span: Span, requestInformation: { headers?: WebFetchHeaders }): void;\n}\n\nconst DEFAULT_BROWSER_TRACING_OPTIONS: BrowserTracingOptions = {\n  ...TRACING_DEFAULTS,\n  instrumentNavigation: true,\n  instrumentPageLoad: true,\n  markBackgroundSpan: true,\n  enableLongTask: true,\n  enableLongAnimationFrame: true,\n  enableInp: true,\n  enableElementTiming: true,\n  ignoreResourceSpans: [],\n  ignorePerformanceApiSpans: [],\n  detectRedirects: true,\n  linkPreviousTrace: 'in-memory',\n  consistentTraceSampling: false,\n  _experiments: {},\n  ...defaultRequestInstrumentationOptions,\n};\n\n/**\n * The Browser Tracing integration automatically instruments browser pageload/navigation\n * actions as transactions, and captures requests, metrics and errors as spans.\n *\n * The integration can be configured with a variety of options, and can be extended to use\n * any routing library.\n *\n * We explicitly export the proper type here, as this has to be extended in some cases.\n */\nexport const browserTracingIntegration = ((_options: Partial<BrowserTracingOptions> = {}) => {\n  const latestRoute: RouteInfo = {\n    name: undefined,\n    source: undefined,\n  };\n\n  /**\n   * This is just a small wrapper that makes `document` optional.\n   * We want to be extra-safe and always check that this exists, to ensure weird environments do not blow up.\n   */\n  const optionalWindowDocument = WINDOW.document as (typeof WINDOW)['document'] | undefined;\n\n  const {\n    enableInp,\n    enableElementTiming,\n    enableLongTask,\n    enableLongAnimationFrame,\n    _experiments: { enableInteractions, enableStandaloneClsSpans, enableStandaloneLcpSpans },\n    beforeStartSpan,\n    idleTimeout,\n    finalTimeout,\n    childSpanTimeout,\n    markBackgroundSpan,\n    traceFetch,\n    traceXHR,\n    trackFetchStreamPerformance,\n    shouldCreateSpanForRequest,\n    enableHTTPTimings,\n    ignoreResourceSpans,\n    ignorePerformanceApiSpans,\n    instrumentPageLoad,\n    instrumentNavigation,\n    detectRedirects,\n    linkPreviousTrace,\n    consistentTraceSampling,\n    onRequestSpanStart,\n  } = {\n    ...DEFAULT_BROWSER_TRACING_OPTIONS,\n    ..._options,\n  };\n\n  let _collectWebVitals: undefined | (() => void);\n  let lastInteractionTimestamp: number | undefined;\n\n  /** Create routing idle transaction. */\n  function _createRouteSpan(client: Client, startSpanOptions: StartSpanOptions, makeActive = true): void {\n    const isPageloadTransaction = startSpanOptions.op === 'pageload';\n\n    const finalStartSpanOptions: StartSpanOptions = beforeStartSpan\n      ? beforeStartSpan(startSpanOptions)\n      : startSpanOptions;\n\n    const attributes = finalStartSpanOptions.attributes || {};\n\n    // If `finalStartSpanOptions.name` is different than `startSpanOptions.name`\n    // it is because `beforeStartSpan` set a custom name. Therefore we set the source to 'custom'.\n    if (startSpanOptions.name !== finalStartSpanOptions.name) {\n      attributes[SEMANTIC_ATTRIBUTE_SENTRY_SOURCE] = 'custom';\n      finalStartSpanOptions.attributes = attributes;\n    }\n\n    if (!makeActive) {\n      // We want to ensure this has 0s duration\n      const now = dateTimestampInSeconds();\n      startInactiveSpan({\n        ...finalStartSpanOptions,\n        startTime: now,\n      }).end(now);\n      return;\n    }\n\n    latestRoute.name = finalStartSpanOptions.name;\n    latestRoute.source = attributes[SEMANTIC_ATTRIBUTE_SENTRY_SOURCE];\n\n    const idleSpan = startIdleSpan(finalStartSpanOptions, {\n      idleTimeout,\n      finalTimeout,\n      childSpanTimeout,\n      // should wait for finish signal if it's a pageload transaction\n      disableAutoFinish: isPageloadTransaction,\n      beforeSpanEnd: span => {\n        // This will generally always be defined here, because it is set in `setup()` of the integration\n        // but technically, it is optional, so we guard here to be extra safe\n        _collectWebVitals?.();\n        addPerformanceEntries(span, {\n          recordClsOnPageloadSpan: !enableStandaloneClsSpans,\n          recordLcpOnPageloadSpan: !enableStandaloneLcpSpans,\n          ignoreResourceSpans,\n          ignorePerformanceApiSpans,\n        });\n        setActiveIdleSpan(client, undefined);\n\n        // A trace should stay consistent over the entire timespan of one route - even after the pageload/navigation ended.\n        // Only when another navigation happens, we want to create a new trace.\n        // This way, e.g. errors that occur after the pageload span ended are still associated to the pageload trace.\n        const scope = getCurrentScope();\n        const oldPropagationContext = scope.getPropagationContext();\n\n        scope.setPropagationContext({\n          ...oldPropagationContext,\n          traceId: idleSpan.spanContext().traceId,\n          sampled: spanIsSampled(idleSpan),\n          dsc: getDynamicSamplingContextFromSpan(span),\n        });\n      },\n    });\n\n    setActiveIdleSpan(client, idleSpan);\n\n    function emitFinish(): void {\n      if (optionalWindowDocument && ['interactive', 'complete'].includes(optionalWindowDocument.readyState)) {\n        client.emit('idleSpanEnableAutoFinish', idleSpan);\n      }\n    }\n\n    if (isPageloadTransaction && optionalWindowDocument) {\n      optionalWindowDocument.addEventListener('readystatechange', () => {\n        emitFinish();\n      });\n\n      emitFinish();\n    }\n  }\n\n  return {\n    name: BROWSER_TRACING_INTEGRATION_ID,\n    setup(client) {\n      registerSpanErrorInstrumentation();\n\n      _collectWebVitals = startTrackingWebVitals({\n        recordClsStandaloneSpans: enableStandaloneClsSpans || false,\n        recordLcpStandaloneSpans: enableStandaloneLcpSpans || false,\n        client,\n      });\n\n      if (enableInp) {\n        startTrackingINP();\n      }\n\n      if (enableElementTiming) {\n        startTrackingElementTiming();\n      }\n\n      if (\n        enableLongAnimationFrame &&\n        GLOBAL_OBJ.PerformanceObserver &&\n        PerformanceObserver.supportedEntryTypes &&\n        PerformanceObserver.supportedEntryTypes.includes('long-animation-frame')\n      ) {\n        startTrackingLongAnimationFrames();\n      } else if (enableLongTask) {\n        startTrackingLongTasks();\n      }\n\n      if (enableInteractions) {\n        startTrackingInteractions();\n      }\n\n      if (detectRedirects && optionalWindowDocument) {\n        const interactionHandler = (): void => {\n          lastInteractionTimestamp = timestampInSeconds();\n        };\n        addEventListener('click', interactionHandler, { capture: true });\n        addEventListener('keydown', interactionHandler, { capture: true, passive: true });\n      }\n\n      function maybeEndActiveSpan(): void {\n        const activeSpan = getActiveIdleSpan(client);\n\n        if (activeSpan && !spanToJSON(activeSpan).timestamp) {\n          DEBUG_BUILD && debug.log(`[Tracing] Finishing current active span with op: ${spanToJSON(activeSpan).op}`);\n          // If there's an open active span, we need to finish it before creating an new one.\n          activeSpan.setAttribute(SEMANTIC_ATTRIBUTE_SENTRY_IDLE_SPAN_FINISH_REASON, 'cancelled');\n          activeSpan.end();\n        }\n      }\n\n      client.on('startNavigationSpan', (startSpanOptions, navigationOptions) => {\n        if (getClient() !== client) {\n          return;\n        }\n\n        if (navigationOptions?.isRedirect) {\n          DEBUG_BUILD &&\n            debug.warn('[Tracing] Detected redirect, navigation span will not be the root span, but a child span.');\n          _createRouteSpan(\n            client,\n            {\n              op: 'navigation.redirect',\n              ...startSpanOptions,\n            },\n            false,\n          );\n          return;\n        }\n\n        maybeEndActiveSpan();\n\n        getIsolationScope().setPropagationContext({ traceId: generateTraceId(), sampleRand: Math.random() });\n\n        const scope = getCurrentScope();\n        scope.setPropagationContext({ traceId: generateTraceId(), sampleRand: Math.random() });\n        // We reset this to ensure we do not have lingering incorrect data here\n        // places that call this hook may set this where appropriate - else, the URL at span sending time is used\n        scope.setSDKProcessingMetadata({\n          normalizedRequest: undefined,\n        });\n\n        _createRouteSpan(client, {\n          op: 'navigation',\n          ...startSpanOptions,\n        });\n      });\n\n      client.on('startPageLoadSpan', (startSpanOptions, traceOptions = {}) => {\n        if (getClient() !== client) {\n          return;\n        }\n        maybeEndActiveSpan();\n\n        const sentryTrace = traceOptions.sentryTrace || getMetaContent('sentry-trace');\n        const baggage = traceOptions.baggage || getMetaContent('baggage');\n\n        const propagationContext = propagationContextFromHeaders(sentryTrace, baggage);\n\n        const scope = getCurrentScope();\n        scope.setPropagationContext(propagationContext);\n\n        // We store the normalized request data on the scope, so we get the request data at time of span creation\n        // otherwise, the URL etc. may already be of the following navigation, and we'd report the wrong URL\n        scope.setSDKProcessingMetadata({\n          normalizedRequest: getHttpRequestData(),\n        });\n\n        _createRouteSpan(client, {\n          op: 'pageload',\n          ...startSpanOptions,\n        });\n      });\n    },\n    afterAllSetup(client) {\n      let startingUrl: string | undefined = getLocationHref();\n\n      if (linkPreviousTrace !== 'off') {\n        linkTraces(client, { linkPreviousTrace, consistentTraceSampling });\n      }\n\n      if (WINDOW.location) {\n        if (instrumentPageLoad) {\n          const origin = browserPerformanceTimeOrigin();\n          startBrowserTracingPageLoadSpan(client, {\n            name: WINDOW.location.pathname,\n            // pageload should always start at timeOrigin (and needs to be in s, not ms)\n            startTime: origin ? origin / 1000 : undefined,\n            attributes: {\n              [SEMANTIC_ATTRIBUTE_SENTRY_SOURCE]: 'url',\n              [SEMANTIC_ATTRIBUTE_SENTRY_ORIGIN]: 'auto.pageload.browser',\n            },\n          });\n        }\n\n        if (instrumentNavigation) {\n          addHistoryInstrumentationHandler(({ to, from }) => {\n            /**\n             * This early return is there to account for some cases where a navigation transaction starts right after\n             * long-running pageload. We make sure that if `from` is undefined and a valid `startingURL` exists, we don't\n             * create an uneccessary navigation transaction.\n             *\n             * This was hard to duplicate, but this behavior stopped as soon as this fix was applied. This issue might also\n             * only be caused in certain development environments where the usage of a hot module reloader is causing\n             * errors.\n             */\n            if (from === undefined && startingUrl?.indexOf(to) !== -1) {\n              startingUrl = undefined;\n              return;\n            }\n\n            startingUrl = undefined;\n            const parsed = parseStringToURLObject(to);\n            const activeSpan = getActiveIdleSpan(client);\n            const navigationIsRedirect =\n              activeSpan && detectRedirects && isRedirect(activeSpan, lastInteractionTimestamp);\n            startBrowserTracingNavigationSpan(\n              client,\n              {\n                name: parsed?.pathname || WINDOW.location.pathname,\n                attributes: {\n                  [SEMANTIC_ATTRIBUTE_SENTRY_SOURCE]: 'url',\n                  [SEMANTIC_ATTRIBUTE_SENTRY_ORIGIN]: 'auto.navigation.browser',\n                },\n              },\n              { url: to, isRedirect: navigationIsRedirect },\n            );\n          });\n        }\n      }\n\n      if (markBackgroundSpan) {\n        registerBackgroundTabDetection();\n      }\n\n      if (enableInteractions) {\n        registerInteractionListener(client, idleTimeout, finalTimeout, childSpanTimeout, latestRoute);\n      }\n\n      if (enableInp) {\n        registerInpInteractionListener();\n      }\n\n      instrumentOutgoingRequests(client, {\n        traceFetch,\n        traceXHR,\n        trackFetchStreamPerformance,\n        tracePropagationTargets: client.getOptions().tracePropagationTargets,\n        shouldCreateSpanForRequest,\n        enableHTTPTimings,\n        onRequestSpanStart,\n      });\n    },\n  };\n}) satisfies IntegrationFn;\n\n/**\n * Manually start a page load span.\n * This will only do something if a browser tracing integration integration has been setup.\n *\n * If you provide a custom `traceOptions` object, it will be used to continue the trace\n * instead of the default behavior, which is to look it up on the <meta> tags.\n */\nexport function startBrowserTracingPageLoadSpan(\n  client: Client,\n  spanOptions: StartSpanOptions,\n  traceOptions?: { sentryTrace?: string | undefined; baggage?: string | undefined },\n): Span | undefined {\n  client.emit('startPageLoadSpan', spanOptions, traceOptions);\n  getCurrentScope().setTransactionName(spanOptions.name);\n\n  const pageloadSpan = getActiveIdleSpan(client);\n\n  if (pageloadSpan) {\n    client.emit('afterStartPageLoadSpan', pageloadSpan);\n  }\n\n  return pageloadSpan;\n}\n\n/**\n * Manually start a navigation span.\n * This will only do something if a browser tracing integration has been setup.\n */\nexport function startBrowserTracingNavigationSpan(\n  client: Client,\n  spanOptions: StartSpanOptions,\n  options?: { url?: string; isRedirect?: boolean },\n): Span | undefined {\n  const { url, isRedirect } = options || {};\n  client.emit('beforeStartNavigationSpan', spanOptions, { isRedirect });\n  client.emit('startNavigationSpan', spanOptions, { isRedirect });\n\n  const scope = getCurrentScope();\n  scope.setTransactionName(spanOptions.name);\n\n  // We store the normalized request data on the scope, so we get the request data at time of span creation\n  // otherwise, the URL etc. may already be of the following navigation, and we'd report the wrong URL\n  if (url && !isRedirect) {\n    scope.setSDKProcessingMetadata({\n      normalizedRequest: {\n        ...getHttpRequestData(),\n        url,\n      },\n    });\n  }\n\n  return getActiveIdleSpan(client);\n}\n\n/** Returns the value of a meta tag */\nexport function getMetaContent(metaName: string): string | undefined {\n  /**\n   * This is just a small wrapper that makes `document` optional.\n   * We want to be extra-safe and always check that this exists, to ensure weird environments do not blow up.\n   */\n  const optionalWindowDocument = WINDOW.document as (typeof WINDOW)['document'] | undefined;\n\n  const metaTag = optionalWindowDocument?.querySelector(`meta[name=${metaName}]`);\n  return metaTag?.getAttribute('content') || undefined;\n}\n\n/** Start listener for interaction transactions */\nfunction registerInteractionListener(\n  client: Client,\n  idleTimeout: BrowserTracingOptions['idleTimeout'],\n  finalTimeout: BrowserTracingOptions['finalTimeout'],\n  childSpanTimeout: BrowserTracingOptions['childSpanTimeout'],\n  latestRoute: RouteInfo,\n): void {\n  /**\n   * This is just a small wrapper that makes `document` optional.\n   * We want to be extra-safe and always check that this exists, to ensure weird environments do not blow up.\n   */\n  const optionalWindowDocument = WINDOW.document as (typeof WINDOW)['document'] | undefined;\n\n  let inflightInteractionSpan: Span | undefined;\n  const registerInteractionTransaction = (): void => {\n    const op = 'ui.action.click';\n\n    const activeIdleSpan = getActiveIdleSpan(client);\n    if (activeIdleSpan) {\n      const currentRootSpanOp = spanToJSON(activeIdleSpan).op;\n      if (['navigation', 'pageload'].includes(currentRootSpanOp as string)) {\n        DEBUG_BUILD &&\n          debug.warn(`[Tracing] Did not create ${op} span because a pageload or navigation span is in progress.`);\n        return undefined;\n      }\n    }\n\n    if (inflightInteractionSpan) {\n      inflightInteractionSpan.setAttribute(SEMANTIC_ATTRIBUTE_SENTRY_IDLE_SPAN_FINISH_REASON, 'interactionInterrupted');\n      inflightInteractionSpan.end();\n      inflightInteractionSpan = undefined;\n    }\n\n    if (!latestRoute.name) {\n      DEBUG_BUILD && debug.warn(`[Tracing] Did not create ${op} transaction because _latestRouteName is missing.`);\n      return undefined;\n    }\n\n    inflightInteractionSpan = startIdleSpan(\n      {\n        name: latestRoute.name,\n        op,\n        attributes: {\n          [SEMANTIC_ATTRIBUTE_SENTRY_SOURCE]: latestRoute.source || 'url',\n        },\n      },\n      {\n        idleTimeout,\n        finalTimeout,\n        childSpanTimeout,\n      },\n    );\n  };\n\n  if (optionalWindowDocument) {\n    addEventListener('click', registerInteractionTransaction, { capture: true });\n  }\n}\n\n// We store the active idle span on the client object, so we can access it from exported functions\nconst ACTIVE_IDLE_SPAN_PROPERTY = '_sentry_idleSpan';\nfunction getActiveIdleSpan(client: Client): Span | undefined {\n  return (client as { [ACTIVE_IDLE_SPAN_PROPERTY]?: Span })[ACTIVE_IDLE_SPAN_PROPERTY];\n}\n\nfunction setActiveIdleSpan(client: Client, span: Span | undefined): void {\n  addNonEnumerableProperty(client, ACTIVE_IDLE_SPAN_PROPERTY, span);\n}\n\n// The max. time in seconds between two pageload/navigation spans that makes us consider the second one a redirect\nconst REDIRECT_THRESHOLD = 0.3;\n\nfunction isRedirect(activeSpan: Span, lastInteractionTimestamp: number | undefined): boolean {\n  const spanData = spanToJSON(activeSpan);\n\n  const now = dateTimestampInSeconds();\n\n  // More than 300ms since last navigation/pageload span?\n  // --> never consider this a redirect\n  const startTimestamp = spanData.start_timestamp;\n  if (now - startTimestamp > REDIRECT_THRESHOLD) {\n    return false;\n  }\n\n  // A click happened in the last 300ms?\n  // --> never consider this a redirect\n  if (lastInteractionTimestamp && now - lastInteractionTimestamp <= REDIRECT_THRESHOLD) {\n    return false;\n  }\n\n  return true;\n}\n"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA4CO,MAAM,8BAAA,GAAiC;AA+O9C,MAAM,+BAA+B,GAA0B;IAC7D,uKAAG,mBAAgB;IACnB,oBAAoB,EAAE,IAAI;IAC1B,kBAAkB,EAAE,IAAI;IACxB,kBAAkB,EAAE,IAAI;IACxB,cAAc,EAAE,IAAI;IACpB,wBAAwB,EAAE,IAAI;IAC9B,SAAS,EAAE,IAAI;IACf,mBAAmB,EAAE,IAAI;IACzB,mBAAmB,EAAE,EAAE;IACvB,yBAAyB,EAAE,EAAE;IAC7B,eAAe,EAAE,IAAI;IACrB,iBAAiB,EAAE,WAAW;IAC9B,uBAAuB,EAAE,KAAK;IAC9B,YAAY,EAAE,CAAA,CAAE;IAChB,gLAAG,uCAAoC;AACzC,CAAC;AAED;;;;;;;;CAQA,GACO,MAAM,yBAAA,GAA6B,CAAC,QAAQ,GAAmC,CAAA,CAAE,KAAK;IAC3F,MAAM,WAAW,GAAc;QAC7B,IAAI,EAAE,SAAS;QACf,MAAM,EAAE,SAAS;IACrB,CAAG;IAEH;;;GAGA,GACE,MAAM,sBAAA,qKAAyB,SAAM,CAAC,QAAA;IAEtC,MAAM,EACJ,SAAS,EACT,mBAAmB,EACnB,cAAc,EACd,wBAAwB,EACxB,YAAY,EAAE,EAAE,kBAAkB,EAAE,wBAAwB,EAAE,wBAAA,EAA0B,EACxF,eAAe,EACf,WAAW,EACX,YAAY,EACZ,gBAAgB,EAChB,kBAAkB,EAClB,UAAU,EACV,QAAQ,EACR,2BAA2B,EAC3B,0BAA0B,EAC1B,iBAAiB,EACjB,mBAAmB,EACnB,yBAAyB,EACzB,kBAAkB,EAClB,oBAAoB,EACpB,eAAe,EACf,iBAAiB,EACjB,uBAAuB,EACvB,kBAAkB,EACtB,GAAM;QACF,GAAG,+BAA+B;QAClC,GAAG,QAAQ;IACf,CAAG;IAED,IAAI,iBAAiB;IACrB,IAAI,wBAAwB;IAE9B,qCAAA,GACE,SAAS,gBAAgB,CAAC,MAAM,EAAU,gBAAgB,EAAoB,UAAA,GAAa,IAAI,EAAQ;QACrG,MAAM,qBAAA,GAAwB,gBAAgB,CAAC,EAAA,KAAO,UAAU;QAEhE,MAAM,qBAAqB,GAAqB,kBAC5C,eAAe,CAAC,gBAAgB,IAChC,gBAAgB;QAEpB,MAAM,aAAa,qBAAqB,CAAC,UAAA,IAAc,CAAA,CAAE;QAE7D,4EAAA;QACA,8FAAA;QACI,IAAI,gBAAgB,CAAC,IAAA,KAAS,qBAAqB,CAAC,IAAI,EAAE;YACxD,UAAU,oKAAC,mCAAgC,CAAA,GAAI,QAAQ;YACvD,qBAAqB,CAAC,UAAA,GAAa,UAAU;QACnD;QAEI,IAAI,CAAC,UAAU,EAAE;YACrB,yCAAA;YACM,MAAM,GAAA,qKAAM,yBAAA,AAAsB,EAAE;iLACpC,oBAAA,AAAiB,EAAC;gBAChB,GAAG,qBAAqB;gBACxB,SAAS,EAAE,GAAG;YACtB,CAAO,CAAC,CAAC,GAAG,CAAC,GAAG,CAAC;YACX;QACN;QAEI,WAAW,CAAC,IAAA,GAAO,qBAAqB,CAAC,IAAI;QAC7C,WAAW,CAAC,MAAA,GAAS,UAAU,oKAAC,mCAAgC,CAAC;QAEjE,MAAM,QAAA,2KAAW,gBAAA,AAAa,EAAC,qBAAqB,EAAE;YACpD,WAAW;YACX,YAAY;YACZ,gBAAgB;YACtB,+DAAA;YACM,iBAAiB,EAAE,qBAAqB;YACxC,aAAa,GAAE,IAAA,IAAQ;gBAC7B,gGAAA;gBACA,qEAAA;gBACQ,iBAAiB,IAAI;sNACrB,wBAAA,AAAqB,EAAC,IAAI,EAAE;oBAC1B,uBAAuB,EAAE,CAAC,wBAAwB;oBAClD,uBAAuB,EAAE,CAAC,wBAAwB;oBAClD,mBAAmB;oBACnB,yBAAyB;gBACnC,CAAS,CAAC;gBACF,iBAAiB,CAAC,MAAM,EAAE,SAAS,CAAC;gBAE5C,mHAAA;gBACA,uEAAA;gBACA,6GAAA;gBACQ,MAAM,KAAA,oKAAQ,mBAAA,AAAe,EAAE;gBAC/B,MAAM,qBAAA,GAAwB,KAAK,CAAC,qBAAqB,EAAE;gBAE3D,KAAK,CAAC,qBAAqB,CAAC;oBAC1B,GAAG,qBAAqB;oBACxB,OAAO,EAAE,QAAQ,CAAC,WAAW,EAAE,CAAC,OAAO;oBACvC,OAAO,wKAAE,iBAAA,AAAa,EAAC,QAAQ,CAAC;oBAChC,GAAG,wLAAE,oCAAA,AAAiC,EAAC,IAAI,CAAC;gBACtD,CAAS,CAAC;YACV,CAAO;QACP,CAAK,CAAC;QAEF,iBAAiB,CAAC,MAAM,EAAE,QAAQ,CAAC;QAEnC,SAAS,UAAU,GAAS;YAC1B,IAAI,sBAAA,IAA0B;gBAAC,aAAa;gBAAE,UAAU;aAAC,CAAC,QAAQ,CAAC,sBAAsB,CAAC,UAAU,CAAC,EAAE;gBACrG,MAAM,CAAC,IAAI,CAAC,0BAA0B,EAAE,QAAQ,CAAC;YACzD;QACA;QAEI,IAAI,qBAAA,IAAyB,sBAAsB,EAAE;YACnD,sBAAsB,CAAC,gBAAgB,CAAC,kBAAkB,EAAE,MAAM;gBAChE,UAAU,EAAE;YACpB,CAAO,CAAC;YAEF,UAAU,EAAE;QAClB;IACA;IAEE,OAAO;QACL,IAAI,EAAE,8BAA8B;QACpC,KAAK,EAAC,MAAM,EAAE;kLACZ,mCAAA,AAAgC,EAAE;YAElC,iBAAA,yMAAoB,yBAAA,AAAsB,EAAC;gBACzC,wBAAwB,EAAE,wBAAA,IAA4B,KAAK;gBAC3D,wBAAwB,EAAE,wBAAA,IAA4B,KAAK;gBAC3D,MAAM;YACd,CAAO,CAAC;YAEF,IAAI,SAAS,EAAE;2MACb,mBAAA,AAAgB,EAAE;YAC1B;YAEM,IAAI,mBAAmB,EAAE;oBACvB,8NAAA,AAA0B,EAAE;YACpC;YAEM,IACE,wBAAA,uKACA,aAAU,CAAC,mBAAA,IACX,mBAAmB,CAAC,mBAAA,IACpB,mBAAmB,CAAC,mBAAmB,CAAC,QAAQ,CAAC,sBAAsB,GACvE;sNACA,mCAAA,AAAgC,EAAE;YAC1C,CAAM,MAAO,IAAI,cAAc,EAAE;sNACzB,yBAAA,AAAsB,EAAE;YAChC;YAEM,IAAI,kBAAkB,EAAE;gBACtB,kOAAA,AAAyB,EAAE;YACnC;YAEM,IAAI,eAAA,IAAmB,sBAAsB,EAAE;gBAC7C,MAAM,kBAAA,GAAqB,MAAY;oBACrC,wBAAA,qKAA2B,qBAAA,AAAkB,EAAE;gBACzD,CAAS;gBACD,gBAAgB,CAAC,OAAO,EAAE,kBAAkB,EAAE;oBAAE,OAAO,EAAE,IAAA;gBAAA,CAAM,CAAC;gBAChE,gBAAgB,CAAC,SAAS,EAAE,kBAAkB,EAAE;oBAAE,OAAO,EAAE,IAAI;oBAAE,OAAO,EAAE,IAAA;gBAAA,CAAM,CAAC;YACzF;YAEM,SAAS,kBAAkB,GAAS;gBAClC,MAAM,UAAA,GAAa,iBAAiB,CAAC,MAAM,CAAC;gBAE5C,IAAI,UAAA,IAAc,wKAAC,aAAA,AAAU,EAAC,UAAU,CAAC,CAAC,SAAS,EAAE;oBACnD,uLAAA,6KAAe,QAAK,CAAC,GAAG,CAAC,CAAC,iDAAiD,yKAAE,aAAA,AAAU,EAAC,UAAU,CAAC,CAAC,EAAE,CAAC,CAAA,CAAA;oBACA,mFAAA;oBACA,UAAA,CAAA,YAAA,CAAA,uNAAA,EAAA,WAAA,CAAA;oBACA,UAAA,CAAA,GAAA,EAAA;gBACA;YACA;YAEA,MAAA,CAAA,EAAA,CAAA,qBAAA,EAAA,CAAA,gBAAA,EAAA,iBAAA,KAAA;gBACA,qKAAA,aAAA,EAAA,MAAA,MAAA,EAAA;oBACA;gBACA;gBAEA,IAAA,iBAAA,EAAA,UAAA,EAAA;6LACA,cAAA,6KACA,QAAA,CAAA,IAAA,CAAA,2FAAA,CAAA;oBACA,gBAAA,CACA,MAAA,EACA;wBACA,EAAA,EAAA,qBAAA;wBACA,GAAA,gBAAA;oBACA,CAAA,EACA,KAAA;oBAEA;gBACA;gBAEA,kBAAA,EAAA;kLAEA,oBAAA,EAAA,EAAA,qBAAA,CAAA;oBAAA,OAAA,kLAAA,kBAAA,EAAA;oBAAA,UAAA,EAAA,IAAA,CAAA,MAAA,EAAA;gBAAA,CAAA,CAAA;gBAEA,MAAA,KAAA,GAAA,oLAAA,EAAA;gBACA,KAAA,CAAA,qBAAA,CAAA;oBAAA,OAAA,kLAAA,kBAAA,EAAA;oBAAA,UAAA,EAAA,IAAA,CAAA,MAAA,EAAA;gBAAA,CAAA,CAAA;gBACA,uEAAA;gBACA,yGAAA;gBACA,KAAA,CAAA,wBAAA,CAAA;oBACA,iBAAA,EAAA,SAAA;gBACA,CAAA,CAAA;gBAEA,gBAAA,CAAA,MAAA,EAAA;oBACA,EAAA,EAAA,YAAA;oBACA,GAAA,gBAAA;gBACA,CAAA,CAAA;YACA,CAAA,CAAA;YAEA,MAAA,CAAA,EAAA,CAAA,mBAAA,EAAA,CAAA,gBAAA,EAAA,YAAA,GAAA,CAAA,CAAA,KAAA;gBACA,sKAAA,YAAA,EAAA,MAAA,MAAA,EAAA;oBACA;gBACA;gBACA,kBAAA,EAAA;gBAEA,MAAA,WAAA,GAAA,YAAA,CAAA,WAAA,IAAA,cAAA,CAAA,cAAA,CAAA;gBACA,MAAA,OAAA,GAAA,YAAA,CAAA,OAAA,IAAA,cAAA,CAAA,SAAA,CAAA;gBAEA,MAAA,kBAAA,wKAAA,gCAAA,EAAA,WAAA,EAAA,OAAA,CAAA;gBAEA,MAAA,KAAA,qKAAA,kBAAA,EAAA;gBACA,KAAA,CAAA,qBAAA,CAAA,kBAAA,CAAA;gBAEA,yGAAA;gBACA,oGAAA;gBACA,KAAA,CAAA,wBAAA,CAAA;oBACA,iBAAA,wKAAA,qBAAA,EAAA;gBACA,CAAA,CAAA;gBAEA,gBAAA,CAAA,MAAA,EAAA;oBACA,EAAA,EAAA,UAAA;oBACA,GAAA,gBAAA;gBACA,CAAA,CAAA;YACA,CAAA,CAAA;QACA,CAAA;QACA,aAAA,EAAA,MAAA,EAAA;YACA,IAAA,WAAA,wKAAA,kBAAA,EAAA;YAEA,IAAA,iBAAA,KAAA,KAAA,EAAA;sMACA,aAAA,EAAA,MAAA,EAAA;oBAAA,iBAAA;oBAAA,uBAAA;gBAAA,CAAA,CAAA;YACA;YAEA,IAAA,2KAAA,CAAA,QAAA,EAAA;gBACA,IAAA,kBAAA,EAAA;oBACA,MAAA,MAAA,qKAAA,+BAAA,EAAA;oBACA,+BAAA,CAAA,MAAA,EAAA;wBACA,IAAA,oKAAA,SAAA,CAAA,QAAA,CAAA,QAAA;wBACA,4EAAA;wBACA,SAAA,EAAA,MAAA,GAAA,MAAA,GAAA,IAAA,GAAA,SAAA;wBACA,UAAA,EAAA;4BACA,oKAAA,mCAAA,CAAA,EAAA,KAAA;4BACA,oKAAA,mCAAA,CAAA,EAAA,uBAAA;wBACA,CAAA;oBACA,CAAA,CAAA;gBACA;gBAEA,IAAA,oBAAA,EAAA;qBACA,oOAAA,EAAA,CAAA,EAAA,EAAA,EAAA,IAAA,EAAA,KAAA;wBACA;;;;;;;;aAQA,GACA,IAAA,IAAA,KAAA,SAAA,IAAA,WAAA,EAAA,OAAA,CAAA,EAAA,CAAA,KAAA,CAAA,CAAA,EAAA;4BACA,WAAA,GAAA,SAAA;4BACA;wBACA;wBAEA,WAAA,GAAA,SAAA;wBACA,MAAA,MAAA,oKAAA,yBAAA,EAAA,EAAA,CAAA;wBACA,MAAA,UAAA,GAAA,iBAAA,CAAA,MAAA,CAAA;wBACA,MAAA,oBAAA,GACA,UAAA,IAAA,eAAA,IAAA,UAAA,CAAA,UAAA,EAAA,wBAAA,CAAA;wBACA,iCAAA,CACA,MAAA,EACA;4BACA,IAAA,EAAA,MAAA,EAAA,QAAA,sKAAA,SAAA,CAAA,QAAA,CAAA,QAAA;4BACA,UAAA,EAAA;gCACA,oKAAA,mCAAA,CAAA,EAAA,KAAA;gCACA,CAAA,sMAAA,CAAA,EAAA,yBAAA;4BACA,CAAA;wBACA,CAAA,EACA;4BAAA,GAAA,EAAA,EAAA;4BAAA,UAAA,EAAA,oBAAA;wBAAA,CAAA;oBAEA,CAAA,CAAA;gBACA;YACA;YAEA,IAAA,kBAAA,EAAA;oBACA,oNAAA,EAAA;YACA;YAEA,IAAA,kBAAA,EAAA;gBACA,2BAAA,CAAA,MAAA,EAAA,WAAA,EAAA,YAAA,EAAA,gBAAA,EAAA,WAAA,CAAA;YACA;YAEA,IAAA,SAAA,EAAA;gBACA,4NAAA,EAAA;YACA;6LAEA,6BAAA,EAAA,MAAA,EAAA;gBACA,UAAA;gBACA,QAAA;gBACA,2BAAA;gBACA,uBAAA,EAAA,MAAA,CAAA,UAAA,EAAA,CAAA,uBAAA;gBACA,0BAAA;gBACA,iBAAA;gBACA,kBAAA;YACA,CAAA,CAAA;QACA,CAAA;IACA,CAAA;AACA,CAAA,CAAA;AAEA;;;;;;CAMA,GACA,SAAA,+BAAA,CACA,MAAA,EACA,WAAA,EACA,YAAA;IAEA,MAAA,CAAA,IAAA,CAAA,mBAAA,EAAA,WAAA,EAAA,YAAA,CAAA;sKACA,kBAAA,EAAA,EAAA,kBAAA,CAAA,WAAA,CAAA,IAAA,CAAA;IAEA,MAAA,YAAA,GAAA,iBAAA,CAAA,MAAA,CAAA;IAEA,IAAA,YAAA,EAAA;QACA,MAAA,CAAA,IAAA,CAAA,wBAAA,EAAA,YAAA,CAAA;IACA;IAEA,OAAA,YAAA;AACA;AAEA;;;CAGA,GACA,SAAA,iCAAA,CACA,MAAA,EACA,WAAA,EACA,OAAA;IAEA,MAAA,EAAA,GAAA,EAAA,UAAA,EAAA,GAAA,OAAA,IAAA,CAAA,CAAA;IACA,MAAA,CAAA,IAAA,CAAA,2BAAA,EAAA,WAAA,EAAA;QAAA,UAAA;IAAA,CAAA,CAAA;IACA,MAAA,CAAA,IAAA,CAAA,qBAAA,EAAA,WAAA,EAAA;QAAA,UAAA;IAAA,CAAA,CAAA;IAEA,MAAA,KAAA,qKAAA,kBAAA,EAAA;IACA,KAAA,CAAA,kBAAA,CAAA,WAAA,CAAA,IAAA,CAAA;IAEA,yGAAA;IACA,oGAAA;IACA,IAAA,GAAA,IAAA,CAAA,UAAA,EAAA;QACA,KAAA,CAAA,wBAAA,CAAA;YACA,iBAAA,EAAA;gBACA,IAAA,0LAAA,GAAA;gBACA,GAAA;YACA,CAAA;QACA,CAAA,CAAA;IACA;IAEA,OAAA,iBAAA,CAAA,MAAA,CAAA;AACA;AAEA,oCAAA,GACA,SAAA,cAAA,CAAA,QAAA,EAAA;IACA;;;GAGA,GACA,MAAA,sBAAA,qKAAA,SAAA,CAAA,QAAA;IAEA,MAAA,OAAA,GAAA,sBAAA,EAAA,aAAA,CAAA,CAAA,UAAA,EAAA,QAAA,CAAA,CAAA,CAAA,CAAA;IACA,OAAA,OAAA,EAAA,YAAA,CAAA,SAAA,CAAA,IAAA,SAAA;AACA;AAEA,gDAAA,GACA,SAAA,2BAAA,CACA,MAAA,EACA,WAAA,EACA,YAAA,EACA,gBAAA,EACA,WAAA;IAEA;;;GAGA,GACA,MAAA,sBAAA,qKAAA,SAAA,CAAA,QAAA;IAEA,IAAA,uBAAA;IACA,MAAA,8BAAA,GAAA,MAAA;QACA,MAAA,EAAA,GAAA,iBAAA;QAEA,MAAA,cAAA,GAAA,iBAAA,CAAA,MAAA,CAAA;QACA,IAAA,cAAA,EAAA;YACA,MAAA,iBAAA,0KAAA,aAAA,EAAA,cAAA,CAAA,CAAA,EAAA;YACA,IAAA;gBAAA,YAAA;gBAAA,UAAA;aAAA,CAAA,QAAA,CAAA,iBAAA,EAAA,CAAA;yLACA,cAAA,6KACA,QAAA,CAAA,IAAA,CAAA,CAAA,yBAAA,EAAA,EAAA,CAAA,2DAAA,CAAA,CAAA;gBACA,OAAA,SAAA;YACA;QACA;QAEA,IAAA,uBAAA,EAAA;YACA,uBAAA,CAAA,YAAA,oKAAA,oDAAA,EAAA,wBAAA,CAAA;YACA,uBAAA,CAAA,GAAA,EAAA;YACA,uBAAA,GAAA,SAAA;QACA;QAEA,IAAA,CAAA,WAAA,CAAA,IAAA,EAAA;qLACA,cAAA,IAAA,iLAAA,CAAA,IAAA,CAAA,CAAA,yBAAA,EAAA,EAAA,CAAA,iDAAA,CAAA,CAAA;YACA,OAAA,SAAA;QACA;QAEA,uBAAA,2KAAA,gBAAA,EACA;YACA,IAAA,EAAA,WAAA,CAAA,IAAA;YACA,EAAA;YACA,UAAA,EAAA;gBACA,CAAA,sMAAA,CAAA,EAAA,WAAA,CAAA,MAAA,IAAA,KAAA;YACA,CAAA;QACA,CAAA,EACA;YACA,WAAA;YACA,YAAA;YACA,gBAAA;QACA,CAAA;IAEA,CAAA;IAEA,IAAA,sBAAA,EAAA;QACA,gBAAA,CAAA,OAAA,EAAA,8BAAA,EAAA;YAAA,OAAA,EAAA,IAAA;QAAA,CAAA,CAAA;IACA;AACA;AAEA,kGAAA;AACA,MAAA,yBAAA,GAAA,kBAAA;AACA,SAAA,iBAAA,CAAA,MAAA,EAAA;IACA,OAAA,MAAA,CAAA,yBAAA,CAAA;AACA;AAEA,SAAA,iBAAA,CAAA,MAAA,EAAA,IAAA,EAAA;wKACA,2BAAA,EAAA,MAAA,EAAA,yBAAA,EAAA,IAAA,CAAA;AACA;AAEA,kHAAA;AACA,MAAA,kBAAA,GAAA,GAAA;AAEA,SAAA,UAAA,CAAA,UAAA,EAAA,wBAAA,EAAA;IACA,MAAA,QAAA,0KAAA,aAAA,EAAA,UAAA,CAAA;IAEA,MAAA,GAAA,qKAAA,yBAAA,EAAA;IAEA,uDAAA;IACA,qCAAA;IACA,MAAA,cAAA,GAAA,QAAA,CAAA,eAAA;IACA,IAAA,GAAA,GAAA,cAAA,GAAA,kBAAA,EAAA;QACA,OAAA,KAAA;IACA;IAEA,sCAAA;IACA,qCAAA;IACA,IAAA,wBAAA,IAAA,GAAA,GAAA,wBAAA,IAAA,kBAAA,EAAA;QACA,OAAA,KAAA;IACA;IAEA,OAAA,IAAA;AACA", "ignoreList": [0], "debugId": null}}]}