import { NextRequest, NextResponse } from 'next/server';
import { db } from '@/lib/db';
import { quizzes, questions, chapters, modules, courses, quizAttempts } from '@/lib/db/schema';
import { eq, and, or } from 'drizzle-orm';

// GET /api/quizzes/[id] - Get a specific quiz with questions
export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const { id } = await params;
    const quizId = parseInt(id);
    
    if (isNaN(quizId)) {
      return NextResponse.json({ error: 'Invalid quiz ID' }, { status: 400 });
    }

    // Get quiz with chapter/module/course information based on quiz type
    const quizData = await db
      .select({
        id: quizzes.id,
        name: quizzes.name,
        description: quizzes.description,
        quizType: quizzes.quizType,
        timeLimit: quizzes.timeLimit,
        minimumScore: quizzes.minimumScore,
        isActive: quizzes.isActive,
        chapterId: quizzes.chapterId,
        moduleId: quizzes.moduleId,
        courseId: quizzes.courseId,
        createdAt: quizzes.createdAt,
        updatedAt: quizzes.updatedAt,
        chapterName: chapters.name,
        moduleName: modules.name,
        courseName: courses.name,
      })
      .from(quizzes)
      .leftJoin(chapters, eq(quizzes.chapterId, chapters.id))
      .leftJoin(modules, or(
        eq(quizzes.moduleId, modules.id),
        eq(chapters.moduleId, modules.id)
      ))
      .leftJoin(courses, or(
        eq(quizzes.courseId, courses.id),
        eq(modules.courseId, courses.id)
      ))
      .where(eq(quizzes.id, quizId))
      .limit(1);

    if (quizData.length === 0) {
      return NextResponse.json({ error: 'Quiz not found' }, { status: 404 });
    }

    const quiz = quizData[0];

    // Get questions for this quiz
    const quizQuestions = await db
      .select()
      .from(questions)
      .where(eq(questions.quizId, quizId));

    // Parse options for each question
    const questionsWithParsedData = quizQuestions.map(question => ({
      ...question,
      question: question.question,
      options: question.options,
      essayAnswer: question.essayAnswer,
      explanation: question.explanation,
    }));

    return NextResponse.json({
      quiz: {
        ...quiz,
        questions: questionsWithParsedData
      }
    });
  } catch (error) {
    console.error('Error fetching quiz:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}

// PUT /api/quizzes/[id] - Update a quiz
export async function PUT(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const { id } = await params;
    const quizId = parseInt(id);
    
    if (isNaN(quizId)) {
      return NextResponse.json({ error: 'Invalid quiz ID' }, { status: 400 });
    }

    const body = await request.json();
    const {
      name,
      description,
      quizType,
      timeLimit,
      minimumScore,
      isActive,
      teacherId,
      moduleId, // Add moduleId for module quizzes
      courseId, // Add courseId for final exams
      questions: updatedQuestions
    } = body;

    console.log('Updating quiz:', {
      quizId,
      quizType,
      moduleId,
      courseId,
      teacherId,
      name
    });

    // FIXED: Get quiz with proper joins based on quiz type
    const existingQuizQuery = db
      .select({
        id: quizzes.id,
        quizType: quizzes.quizType,
        chapterId: quizzes.chapterId,
        moduleId: quizzes.moduleId,
        courseId: quizzes.courseId,
        teacherId: courses.teacherId,
        courseIdFromModule: modules.courseId,
        courseIdFromChapter: courses.id
      })
      .from(quizzes)
      .leftJoin(chapters, eq(quizzes.chapterId, chapters.id))
      .leftJoin(modules, or(
        eq(quizzes.moduleId, modules.id),
        eq(chapters.moduleId, modules.id)
      ))
      .leftJoin(courses, or(
        eq(quizzes.courseId, courses.id),
        eq(modules.courseId, courses.id)
      ))
      .where(eq(quizzes.id, quizId))
      .limit(1);

    const existingQuiz = await existingQuizQuery;

    if (existingQuiz.length === 0) {
      return NextResponse.json({ error: 'Quiz not found' }, { status: 404 });
    }

    const quiz = existingQuiz[0];
    console.log('Found existing quiz:', quiz);

    // FIXED: Verify teacher has permission based on quiz type
    const quizTeacherId = quiz.teacherId;
    if (teacherId && quizTeacherId !== teacherId) {
      console.log('Permission denied:', { quizTeacherId, requestTeacherId: teacherId });
      return NextResponse.json(
        { error: 'Not authorized to update this quiz' },
        { status: 403 }
      );
    }

    // FIXED: Prepare update data with proper handling of foreign keys
    const updateData: any = {
      ...(name && { name }),
      ...(description && { description }),
      ...(quizType && { quizType }),
      ...(timeLimit !== undefined && { timeLimit }),
      ...(minimumScore !== undefined && { minimumScore: minimumScore.toString() }),
      ...(isActive !== undefined && { isActive }),
      updatedAt: new Date()
    };

    // FIXED: Handle foreign key updates based on quiz type
    if (quizType === 'module' && moduleId) {
      updateData.moduleId = moduleId;
      // Clear other foreign keys
      updateData.chapterId = null;
      updateData.courseId = null;
    } else if (quizType === 'final' && courseId) {
      updateData.courseId = courseId;
      // Clear other foreign keys
      updateData.chapterId = null;
      updateData.moduleId = null;
    } else if (quizType === 'chapter') {
      // For chapter quizzes, keep existing chapterId
      // Clear module and course IDs
      updateData.moduleId = null;
      updateData.courseId = null;
    }

    console.log('Update data:', updateData);

    // Update the quiz
    const updatedQuiz = await db
      .update(quizzes)
      .set(updateData)
      .where(eq(quizzes.id, quizId))
      .returning();

    console.log('Updated quiz result:', updatedQuiz[0]);

    // Update questions if provided
    if (updatedQuestions && Array.isArray(updatedQuestions)) {
      console.log('Updating questions:', updatedQuestions.length);
      
      // Delete existing questions
      await db.delete(questions).where(eq(questions.quizId, quizId));

      // Insert updated questions
      if (updatedQuestions.length > 0) {
        const questionsToInsert = updatedQuestions.map((question: any, index: number) => ({
          quizId,
          type: question.type || 'multiple_choice',
          question: typeof question.question === 'string' 
            ? question.question 
            : JSON.stringify(question.question),
          options: question.options ? JSON.stringify(question.options) : null,
          essayAnswer: question.essayAnswer || null,
          explanation: question.explanation ? JSON.stringify(question.explanation) : null,
          points: question.points || 1,
          orderIndex: question.orderIndex !== undefined ? question.orderIndex : index + 1
        }));

        console.log('Inserting questions:', questionsToInsert.length);
        await db.insert(questions).values(questionsToInsert);
      }
    }

    return NextResponse.json({
      success: true,
      quiz: updatedQuiz[0],
      message: 'Quiz updated successfully'
    });
  } catch (error) {
    console.error('Error updating quiz:', error);
    return NextResponse.json(
      { error: 'Internal server error', details: error },
      { status: 500 }
    );
  }
}

// DELETE /api/quizzes/[id] - Delete a quiz
export async function DELETE(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const { id } = await params;
    const quizId = parseInt(id);
    
    if (isNaN(quizId)) {
      return NextResponse.json({ error: 'Invalid quiz ID' }, { status: 400 });
    }

    const searchParams = request.nextUrl.searchParams;
    const teacherId = searchParams.get('teacherId');

    // FIXED: Check quiz ownership with proper joins
    const existingQuizQuery = db
      .select({
        id: quizzes.id,
        quizType: quizzes.quizType,
        chapterId: quizzes.chapterId,
        moduleId: quizzes.moduleId,
        courseId: quizzes.courseId,
        teacherId: courses.teacherId
      })
      .from(quizzes)
      .leftJoin(chapters, eq(quizzes.chapterId, chapters.id))
      .leftJoin(modules, or(
        eq(quizzes.moduleId, modules.id),
        eq(chapters.moduleId, modules.id)
      ))
      .leftJoin(courses, or(
        eq(quizzes.courseId, courses.id),
        eq(modules.courseId, courses.id)
      ))
      .where(eq(quizzes.id, quizId))
      .limit(1);

    const existingQuiz = await existingQuizQuery;

    if (existingQuiz.length === 0) {
      return NextResponse.json({ error: 'Quiz not found' }, { status: 404 });
    }

    // Verify teacher has permission to delete this quiz
    if (teacherId && existingQuiz[0].teacherId !== parseInt(teacherId)) {
      return NextResponse.json(
        { error: 'Not authorized to delete this quiz' },
        { status: 403 }
      );
    }

    // Delete related data in correct order
    // 1. Delete quiz attempts first
    await db.delete(quizAttempts).where(eq(quizAttempts.quizId, quizId));

    // 2. Delete questions
    await db.delete(questions).where(eq(questions.quizId, quizId));

    // 3. Finally delete the quiz
    await db.delete(quizzes).where(eq(quizzes.id, quizId));

    return NextResponse.json({ 
      success: true,
      message: 'Quiz deleted successfully' 
    });
  } catch (error) {
    console.error('Error deleting quiz:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}