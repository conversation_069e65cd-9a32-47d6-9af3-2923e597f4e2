import React from 'react';
import { Card, CardContent } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Option } from './option';
import { Question as QuestionType } from '@/types/lms';

interface QuestionProps {
  question: QuestionType;
  questionNumber: number;
  totalQuestions: number;
  selectedAnswer: number | string | number[] | undefined;
  onAnswerChange: (questionId: string, answer: number | string | number[]) => void;
  showResults?: boolean;
  isCorrect?: boolean;
  disabled?: boolean;
}

export const Question: React.FC<QuestionProps> = ({
  question,
  questionNumber,
  totalQuestions,
  selectedAnswer,
  onAnswerChange,
  showResults = false,
  isCorrect,
  disabled = false
}) => {
  const getQuestionTypeLabel = (type: string) => {
    switch (type) {
      case 'multiple-choice':
      case 'multiple_choice':
        return 'Pilihan Ganda';
      case 'true-false':
      case 'true_false':
        return 'Benar/Salah';
      case 'essay':
        return 'Esai';
      default:
        return type;
    }
  };

  const handleEssayChange = (e: React.ChangeEvent<HTMLTextAreaElement>) => {
    if (!disabled) {
      onAnswerChange(question.id, e.target.value);
    }
  };

  const handleTrueFalseChange = (value: string) => {
    if (!disabled) {
      onAnswerChange(question.id, value);
    }
  };

  return (
    <Card className={`
      border-2 transition-all
      ${showResults 
        ? (isCorrect ? 'border-green-200 bg-green-50' : 'border-red-200 bg-red-50')
        : 'border-gray-200'
      }
    `}>
      <CardContent className="p-6">
        <div className="mb-4 flex items-center justify-between">
          <div className="flex items-center space-x-3">
            <Badge variant="outline" className="bg-blue-50 text-blue-700 border-blue-200">
              Soal {questionNumber} dari {totalQuestions}
            </Badge>
            <Badge variant="secondary" className="text-xs">
              {getQuestionTypeLabel(question.type)}
            </Badge>
          </div>
          {showResults && (
            <Badge 
              variant={isCorrect ? 'default' : 'destructive'}
              className={isCorrect ? 'bg-green-600 hover:bg-green-700' : ''}
            >
              {isCorrect ? 'Benar' : 'Salah'}
            </Badge>
          )}
        </div>

        <div className="mb-6">
          <p className="text-lg leading-relaxed text-gray-900 whitespace-pre-wrap">
            {typeof question.question === 'string' ? (
              question.question
            ) : Array.isArray(question.question) ? (
              question.question.map((block, index) => (
                <React.Fragment key={index}>
                  {block.type === 'text' && <span>{block.value}</span>}
                  {block.type === 'image' && block.value && (
                    <img src={block.value} alt={`Question image ${index}`} className="inline-block max-h-16 object-contain ml-2" />
                  )}
                </React.Fragment>
              ))
            ) : (
              <span>{String(question.question)}</span>
            )}
          </p>
        </div>

        {/* Multiple Choice Options */}
        {(question.type === 'multiple-choice' || question.type === 'multiple_choice') && question.options && (
          <div className="space-y-3">
            {question.options.map((option, index) => (
              <Option
                key={index}
                option={option}
                index={index}
                questionId={question.id}
                selectedAnswer={selectedAnswer}
                onAnswerChange={onAnswerChange}
                type="radio"
                disabled={disabled}
                showResults={showResults}
                correctAnswer={question.correctAnswer}
                isCorrect={isCorrect}
              />
            ))}
          </div>
        )}

        {/* True/False Options */}
        {(question.type === 'true-false' || question.type === 'true_false') && (
          <div className="space-y-3">
            {['true', 'false'].map((value, index) => {
              const isSelected = selectedAnswer === value;
              const isCorrectOption = question.correctAnswer === value;
              
              const getOptionStyles = () => {
                if (!showResults) {
                  return isSelected 
                    ? 'border-blue-500 bg-blue-50' 
                    : 'border-gray-200 hover:border-gray-300 hover:bg-gray-50';
                }
                
                if (isCorrectOption) {
                  return 'border-green-500 bg-green-50';
                } else if (isSelected) {
                  return 'border-red-500 bg-red-50';
                } else {
                  return 'border-gray-200 bg-gray-50';
                }
              };
              
              return (
                <label 
                  key={value}
                  className={`
                    flex cursor-pointer items-center justify-between p-3 rounded-lg border-2 transition-all
                    ${getOptionStyles()}
                    ${disabled ? 'opacity-50 cursor-not-allowed' : ''}
                    ${showResults && isCorrectOption ? 'ring-2 ring-green-200' : ''}
                    ${showResults && isSelected && !isCorrectOption ? 'ring-2 ring-red-200' : ''}
                  `}
                >
                  <div className="flex items-center space-x-3">
                    <input
                      type="radio"
                      name={question.id}
                      value={value}
                      checked={isSelected}
                      onChange={() => handleTrueFalseChange(value)}
                      disabled={disabled}
                      className="h-4 w-4 text-blue-600"
                    />
                    <span className={`font-medium ${disabled ? 'text-gray-500' : 'text-gray-900'}`}>
                      {String.fromCharCode(65 + index)}. {value === 'true' ? 'Benar' : 'Salah'}
                    </span>
                  </div>
                  
                  {/* Show correct/incorrect indicators in results mode */}
                  {showResults && (
                    <div className="flex items-center">
                      {isCorrectOption && (
                        <span className="text-green-600 font-medium text-xs bg-green-100 px-2 py-1 rounded-full">
                          ✓ Benar
                        </span>
                      )}
                      {isSelected && !isCorrectOption && (
                        <span className="text-red-600 font-medium text-xs bg-red-100 px-2 py-1 rounded-full">
                          ✗ Salah
                        </span>
                      )}
                    </div>
                  )}
                </label>
              );
            })}
          </div>
        )}

        {/* Essay Question */}
        {question.type === 'essay' && (
          <textarea
            className={`
              w-full resize-none rounded-lg border-2 p-4 focus:border-transparent focus:ring-2 focus:ring-blue-500
              ${disabled ? 'bg-gray-50 cursor-not-allowed' : ''}
            `}
            rows={8}
            placeholder="Ketik jawaban Anda di sini..."
            value={selectedAnswer as string || ''}
            onChange={handleEssayChange}
            disabled={disabled}
          />
        )}

        {/* Show explanation in results */}
        {showResults && question.explanation && (
          <div className="mt-4 p-4 bg-gray-50 rounded-lg border-l-4 border-blue-500">
            <h4 className="font-semibold text-gray-900 mb-2">Penjelasan:</h4>
            <p className="text-sm text-gray-700 leading-relaxed">
              {typeof question.explanation === 'string' ? (
                question.explanation
              ) : Array.isArray(question.explanation) ? (
                question.explanation.map((block, index) => (
                  <React.Fragment key={index}>
                    {block.type === 'text' && <span>{block.value}</span>}
                    {block.type === 'image' && block.value && (
                      <img src={block.value} alt={`Explanation image ${index}`} className="inline-block max-h-16 object-contain ml-2" />
                    )}
                    {block.type === 'video' && <span>[Video: {block.value}]</span>}
                    {block.type === 'pdf' && <span>[PDF: {block.value}]</span>}
                    {block.type === 'zoom-recording' && <span>[Recording: {block.value}]</span>}
                  </React.Fragment>
                ))
              ) : (
                <span>{String(question.explanation)}</span>
              )}
            </p>
          </div>
        )}
      </CardContent>
    </Card>
  );
};