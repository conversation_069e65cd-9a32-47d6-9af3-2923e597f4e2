import { NextRequest, NextResponse } from 'next/server';
import { db } from '@/lib/db';
import { classEnrollments, users, classes } from '@/lib/db/schema';
import { eq, and } from 'drizzle-orm';

// GET - Get specific class enrollment by ID
export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const { id } = await params;
    const enrollmentId = parseInt(id);

    if (isNaN(enrollmentId)) {
      return NextResponse.json(
        {
          success: false,
          error: 'Invalid enrollment ID'
        },
        { status: 400 }
      );
    }

    const enrollment = await db
      .select({
        id: classEnrollments.id,
        studentId: classEnrollments.studentId,
        classId: classEnrollments.classId,
        enrolledAt: classEnrollments.enrolledAt,
        status: classEnrollments.status,
        createdAt: classEnrollments.createdAt,
        updatedAt: classEnrollments.updatedAt,
        studentName: users.name,
        studentEmail: users.email,
        className: classes.name
      })
      .from(classEnrollments)
      .leftJoin(users, eq(classEnrollments.studentId, users.id))
      .leftJoin(classes, eq(classEnrollments.classId, classes.id))
      .where(eq(classEnrollments.id, enrollmentId))
      .limit(1);

    if (enrollment.length === 0) {
      return NextResponse.json(
        {
          success: false,
          error: 'Class enrollment not found'
        },
        { status: 404 }
      );
    }

    return NextResponse.json({
      success: true,
      data: enrollment[0]
    });
  } catch (error) {
    console.error('Error fetching class enrollment:', error);
    return NextResponse.json(
      {
        success: false,
        error: 'Failed to fetch class enrollment'
      },
      { status: 500 }
    );
  }
}

// PUT - Update class enrollment status
export async function PUT(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const { id } = await params;
    const enrollmentId = parseInt(id);
    const body = await request.json();
    const { status } = body;

    if (isNaN(enrollmentId)) {
      return NextResponse.json(
        {
          success: false,
          error: 'Invalid enrollment ID'
        },
        { status: 400 }
      );
    }

    if (!status || !['active', 'inactive'].includes(status)) {
      return NextResponse.json(
        {
          success: false,
          error: 'Valid status is required (active or inactive)'
        },
        { status: 400 }
      );
    }

    // Check if enrollment exists
    const existingEnrollment = await db
      .select()
      .from(classEnrollments)
      .where(eq(classEnrollments.id, enrollmentId))
      .limit(1);

    if (existingEnrollment.length === 0) {
      return NextResponse.json(
        {
          success: false,
          error: 'Class enrollment not found'
        },
        { status: 404 }
      );
    }

    // Update enrollment
    const updatedEnrollment = await db
      .update(classEnrollments)
      .set({
        status,
        updatedAt: new Date()
      })
      .where(eq(classEnrollments.id, enrollmentId))
      .returning();

    return NextResponse.json({
      success: true,
      data: updatedEnrollment[0],
      message: 'Class enrollment updated successfully'
    });
  } catch (error) {
    console.error('Error updating class enrollment:', error);
    return NextResponse.json(
      {
        success: false,
        error: 'Failed to update class enrollment'
      },
      { status: 500 }
    );
  }
}

// DELETE - Remove class enrollment
export async function DELETE(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const { id } = await params;
    const enrollmentId = parseInt(id);

    if (isNaN(enrollmentId)) {
      return NextResponse.json(
        {
          success: false,
          error: 'Invalid enrollment ID'
        },
        { status: 400 }
      );
    }

    // Check if enrollment exists
    const existingEnrollment = await db
      .select()
      .from(classEnrollments)
      .where(eq(classEnrollments.id, enrollmentId))
      .limit(1);

    if (existingEnrollment.length === 0) {
      return NextResponse.json(
        {
          success: false,
          error: 'Class enrollment not found'
        },
        { status: 404 }
      );
    }

    // Delete enrollment
    await db
      .delete(classEnrollments)
      .where(eq(classEnrollments.id, enrollmentId));

    return NextResponse.json({
      success: true,
      message: 'Class enrollment deleted successfully'
    });
  } catch (error) {
    console.error('Error deleting class enrollment:', error);
    return NextResponse.json(
      {
        success: false,
        error: 'Failed to delete class enrollment'
      },
      { status: 500 }
    );
  }
}