'use client';

import { useState, useEffect } from 'react';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle
} from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { RadioGroup, RadioGroupItem } from '@/components/ui/radio-group';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Progress } from '@/components/ui/progress';
import { Badge } from '@/components/ui/badge';
import { Clock, CheckCircle, AlertCircle } from 'lucide-react';

interface Question {
  id: string;
  type: 'multiple_choice' | 'true_false' | 'essay';
  question: string;
  options?: string[];
  points: number;
}

interface QuizTakerProps {
  quiz: {
    id: string;
    name: string;
    description: string;
    timeLimit: number;
    minimumScore: number;
    questions: Question[];
  };
  onSubmit: (answers: Record<string, string>) => void;
}

export default function QuizTaker({ quiz, onSubmit }: QuizTakerProps) {
  const [currentQuestion, setCurrentQuestion] = useState(0);
  const [answers, setAnswers] = useState<Record<string, string>>({});
  const [timeRemaining, setTimeRemaining] = useState(quiz.timeLimit * 60); // Convert to seconds
  const [isSubmitted, setIsSubmitted] = useState(false);

  // Timer effect
  useEffect(() => {
    if (timeRemaining > 0 && !isSubmitted) {
      const timer = setTimeout(() => {
        setTimeRemaining(timeRemaining - 1);
      }, 1000);
      return () => clearTimeout(timer);
    } else if (timeRemaining === 0 && !isSubmitted) {
      handleSubmit();
    }
  }, [timeRemaining, isSubmitted]);

  const formatTime = (seconds: number) => {
    const minutes = Math.floor(seconds / 60);
    const remainingSeconds = seconds % 60;
    return `${minutes}:${remainingSeconds.toString().padStart(2, '0')}`;
  };

  const handleAnswerChange = (questionId: string, answer: string) => {
    setAnswers((prev) => ({
      ...prev,
      [questionId]: answer
    }));
  };

  const handleSubmit = () => {
    setIsSubmitted(true);
    onSubmit(answers);
  };

  const goToQuestion = (index: number) => {
    setCurrentQuestion(index);
  };

  const nextQuestion = () => {
    if (currentQuestion < quiz.questions.length - 1) {
      setCurrentQuestion(currentQuestion + 1);
    }
  };

  const previousQuestion = () => {
    if (currentQuestion > 0) {
      setCurrentQuestion(currentQuestion - 1);
    }
  };

  const getAnsweredCount = () => {
    return Object.keys(answers).length;
  };

  const isQuestionAnswered = (questionId: string) => {
    return questionId in answers && answers[questionId].trim() !== '';
  };

  const currentQ = quiz.questions[currentQuestion];
  const progress = ((currentQuestion + 1) / quiz.questions.length) * 100;

  if (isSubmitted) {
    return (
      <Card>
        <CardContent className='pt-6'>
          <div className='py-8 text-center'>
            <CheckCircle className='mx-auto mb-4 h-16 w-16 text-green-500' />
            <h2 className='mb-2 text-2xl font-bold'>Quiz Submitted!</h2>
            <p className='text-muted-foreground mb-4'>
              Your answers have been submitted successfully. Results will be
              available shortly.
            </p>
            <div className='text-muted-foreground text-sm'>
              Questions answered: {getAnsweredCount()} of{' '}
              {quiz.questions.length}
            </div>
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <div className='space-y-6'>
      {/* Quiz Header */}
      <Card>
        <CardHeader>
          <div className='flex items-center justify-between'>
            <div>
              <CardTitle>{quiz.name}</CardTitle>
              <CardDescription>{quiz.description}</CardDescription>
            </div>
            <div className='flex items-center space-x-4'>
              <div className='flex items-center space-x-2'>
                <Clock className='text-muted-foreground h-4 w-4' />
                <span
                  className={`font-mono ${timeRemaining < 300 ? 'text-red-500' : ''}`}
                >
                  {formatTime(timeRemaining)}
                </span>
              </div>
              {timeRemaining < 300 && (
                <Badge variant='destructive'>
                  <AlertCircle className='mr-1 h-3 w-3' />
                  Time Running Out
                </Badge>
              )}
            </div>
          </div>
        </CardHeader>
        <CardContent>
          <div className='space-y-2'>
            <div className='flex items-center justify-between text-sm'>
              <span>Progress</span>
              <span>
                Question {currentQuestion + 1} of {quiz.questions.length}
              </span>
            </div>
            <Progress value={progress} className='h-2' />
            <div className='text-muted-foreground flex items-center justify-between text-xs'>
              <span>Answered: {getAnsweredCount()}</span>
              <span>Minimum Score: {quiz.minimumScore}%</span>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Question Navigation */}
      <Card>
        <CardContent className='pt-6'>
          <div className='flex flex-wrap gap-2'>
            {quiz.questions.map((q, index) => (
              <Button
                key={q.id}
                variant={index === currentQuestion ? 'default' : 'outline'}
                size='sm'
                onClick={() => goToQuestion(index)}
                className={`h-10 w-10 ${
                  isQuestionAnswered(q.id)
                    ? 'border-green-300 bg-green-100'
                    : ''
                }`}
              >
                {index + 1}
              </Button>
            ))}
          </div>
        </CardContent>
      </Card>

      {/* Current Question */}
      <Card>
        <CardHeader>
          <div className='flex items-center justify-between'>
            <CardTitle className='text-lg'>
              Question {currentQuestion + 1}
            </CardTitle>
            <Badge variant='outline'>
              {currentQ.points} {currentQ.points === 1 ? 'point' : 'points'}
            </Badge>
          </div>
        </CardHeader>
        <CardContent className='space-y-6'>
          <div className='prose max-w-none'>
            <p className='text-lg'>{currentQ.question}</p>
          </div>

          {/* Multiple Choice */}
          {currentQ.type === 'multiple_choice' && currentQ.options && (
            <RadioGroup
              value={answers[currentQ.id] || ''}
              onValueChange={(value) => handleAnswerChange(currentQ.id, value)}
            >
              <div className='space-y-3'>
                {currentQ.options.map((option, index) => (
                  <div key={index} className='flex items-center space-x-2'>
                    <RadioGroupItem value={option} id={`option-${index}`} />
                    <Label
                      htmlFor={`option-${index}`}
                      className='flex-1 cursor-pointer'
                    >
                      {option}
                    </Label>
                  </div>
                ))}
              </div>
            </RadioGroup>
          )}

          {/* True/False */}
          {currentQ.type === 'true_false' && (
            <RadioGroup
              value={answers[currentQ.id] || ''}
              onValueChange={(value) => handleAnswerChange(currentQ.id, value)}
            >
              <div className='space-y-3'>
                <div className='flex items-center space-x-2'>
                  <RadioGroupItem value='true' id='true' />
                  <Label htmlFor='true' className='cursor-pointer'>
                    True
                  </Label>
                </div>
                <div className='flex items-center space-x-2'>
                  <RadioGroupItem value='false' id='false' />
                  <Label htmlFor='false' className='cursor-pointer'>
                    False
                  </Label>
                </div>
              </div>
            </RadioGroup>
          )}

          {/* Essay */}
          {currentQ.type === 'essay' && (
            <div className='space-y-2'>
              <Label htmlFor='essay-answer'>Your Answer</Label>
              <Textarea
                id='essay-answer'
                value={answers[currentQ.id] || ''}
                onChange={(e) =>
                  handleAnswerChange(currentQ.id, e.target.value)
                }
                placeholder='Type your answer here...'
                rows={6}
                className='resize-none'
              />
              <p className='text-muted-foreground text-xs'>
                Provide a detailed answer. This will be reviewed manually.
              </p>
            </div>
          )}

          {/* Navigation Buttons */}
          <div className='flex items-center justify-between pt-4'>
            <Button
              variant='outline'
              onClick={previousQuestion}
              disabled={currentQuestion === 0}
            >
              Previous
            </Button>

            <div className='flex space-x-2'>
              {currentQuestion === quiz.questions.length - 1 ? (
                <Button
                  onClick={handleSubmit}
                  className='bg-green-600 hover:bg-green-700'
                >
                  Submit Quiz
                </Button>
              ) : (
                <Button onClick={nextQuestion}>Next</Button>
              )}
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Submit Warning */}
      {getAnsweredCount() < quiz.questions.length && (
        <Card className='border-yellow-200 bg-yellow-50'>
          <CardContent className='pt-6'>
            <div className='flex items-center space-x-2'>
              <AlertCircle className='h-5 w-5 text-yellow-600' />
              <p className='text-sm text-yellow-800'>
                You have {quiz.questions.length - getAnsweredCount()} unanswered
                questions. Make sure to answer all questions before submitting.
              </p>
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  );
}
