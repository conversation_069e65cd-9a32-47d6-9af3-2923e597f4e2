'use client';

import Link from 'next/link';
import Image from 'next/image';
import { useState, useEffect } from 'react';
import {
  Award01Icon as AwardIcon,
  BookOpen01Icon as BookOpenIcon
} from 'hugeicons-react';

interface CourseHeroSectionProps {
  title?: string;
  subtitle?: string;
  description?: string;
  ctaText?: string;
  ctaLink?: string;
  onCTA?: () => void;
}

export default function CourseHeroSection({
  title = "Kuasai Arsitektur Profesional",
  subtitle = "Program Sertifikasi Ikatan Arsitek Indonesia (IAI)",
  description = "Tingkatkan karir Anda dengan sertifikasi arsitektur profesional dan modul pembelajaran komprehensif",
  ctaText = "Jelajahi Kursus",
  ctaLink = "#courses",
  onCTA
}: CourseHeroSectionProps) {
  const [currentModuleSet, setCurrentModuleSet] = useState(0);
  const [progress, setProgress] = useState(60);
  const [animatingModules, setAnimatingModules] = useState<number[]>([]);
  const [cursorPosition, setCursorPosition] = useState({ x: 0, y: 0, visible: false, opacity: 0 });
  const [clickEffect, setClickEffect] = useState({ x: 0, y: 0, active: false });
  const [modalOpen, setModalOpen] = useState(false);
  const [modalModule, setModalModule] = useState<any>(null);
  const [modalProgress, setModalProgress] = useState(0);
  const [modalStatus, setModalStatus] = useState('Sedang Membuka');

  const moduleSets = [
    [
      { title: "Modul 1: Dasar-Dasar Arsitektur", status: "Selesai", color: "green" },
      { title: "Modul 2: Prinsip Desain", status: "Sedang Berjalan", color: "blue" },
      { title: "Modul 3: Kode Bangunan", status: "Terkunci", color: "gray" }
    ],
    [
      { title: "Modul 4: Material Konstruksi", status: "Selesai", color: "green" },
      { title: "Modul 5: Struktur Bangunan", status: "Sedang Berjalan", color: "blue" },
      { title: "Modul 6: Sistem MEP", status: "Terkunci", color: "gray" }
    ],
    [
      { title: "Modul 7: Manajemen Proyek", status: "Selesai", color: "green" },
      { title: "Modul 8: Estimasi Biaya", status: "Sedang Berjalan", color: "blue" },
      { title: "Modul 9: Ujian Akhir", status: "Terkunci", color: "gray" }
    ]
  ];

  const progressValues = [60, 75, 90];
  
  // Dynamic module statuses that change during animation
  const getAnimatedModuleData = () => {
    const baseModules = moduleSets[currentModuleSet];
    return baseModules.map((module, index) => {
      if (animatingModules.includes(index)) {
        // Simulate status progression during animation
        if (module.color === 'gray') {
          // Change based on modal status
          if (modalStatus === 'Terbuka') {
            return { ...module, status: 'Terbuka', color: 'green' };
          } else {
            return { ...module, status: 'Membuka...', color: 'yellow' };
          }
        }
        if (module.color === 'blue') return { ...module, status: 'Menyelesaikan...', color: 'orange' };
        if (module.color === 'green') return { ...module, status: 'Mengulang...', color: 'purple' };
      }
      return module;
    });
  };

  useEffect(() => {
    // Function to run the cursor animation
    const runCursorAnimation = () => {
      // Find the "Terkunci" (gray) module in current set
      const currentModules = moduleSets[currentModuleSet];
      const lockedModuleIndex = currentModules.findIndex(module => module.color === 'gray');
      
      if (lockedModuleIndex !== -1) {
        // Step 1: Fade in cursor at starting position
        setCursorPosition({ 
          x: 50, // Start from left side
          y: 30 + (lockedModuleIndex * 60), // Position near the locked module
          visible: true,
          opacity: 1
        });
        
        // Step 2: Move cursor to target (after 800ms)
        setTimeout(() => {
          setCursorPosition({ 
            x: 200, // Move to center of module
            y: 30 + (lockedModuleIndex * 60), 
            visible: true,
            opacity: 1
          });
        }, 800);
        
        // Step 3: Click and animate (after cursor reaches target - 1400ms)
        setTimeout(() => {
          setAnimatingModules([lockedModuleIndex]);
          // Position click effect at cursor tip (bottom-right of cursor center)
          setClickEffect({ x: 191, y: 22 + (lockedModuleIndex * 60), active: true });
          setTimeout(() => setClickEffect(prev => ({ ...prev, active: false })), 300);
          
          // Open modal with the locked module
          const currentModules = moduleSets[currentModuleSet];
          setModalModule({ ...currentModules[lockedModuleIndex], index: lockedModuleIndex });
          setModalOpen(true);
          setModalProgress(0);
          setModalStatus('Sedang Membuka');
          
          // Animate progress bar and change status
          setTimeout(() => {
            setModalProgress(100);
            setModalStatus('Terbuka');
          }, 800);
        }, 1400);
        
        // Step 4: Close modal after user can read "Terbuka" (after 4000ms)
        setTimeout(() => {
          setModalOpen(false);
          setModalModule(null);
        }, 4000);
        
        // Step 5: Start fading out cursor (after modal closes - 4200ms)
        setTimeout(() => {
          setCursorPosition(prev => ({ ...prev, opacity: 0 }));
        }, 4200);
        
        // Step 6: Clear animations completely (after fade out - 5200ms total)
        setTimeout(() => {
          setAnimatingModules([]);
          setCursorPosition(prev => ({ ...prev, visible: false }));
        }, 5200);
      }

      // Change module set (after all animations complete)
      setTimeout(() => {
        setCurrentModuleSet((prev) => (prev + 1) % moduleSets.length);
      }, 5600);
    };

    // Start animation immediately on mount
    runCursorAnimation();

    // Then repeat every 7 seconds
    const interval = setInterval(runCursorAnimation, 7000);

    return () => clearInterval(interval);
  }, [currentModuleSet]);

  useEffect(() => {
    // Animate progress when module set changes
    const targetProgress = progressValues[currentModuleSet];
    const currentProg = progress;
    const step = (targetProgress - currentProg) / 20;
    
    let stepCount = 0;
    const progressInterval = setInterval(() => {
      stepCount++;
      setProgress(prev => {
        const newProgress = currentProg + (step * stepCount);
        if (stepCount >= 20) {
          clearInterval(progressInterval);
          return targetProgress;
        }
        return newProgress;
      });
    }, 50);

    return () => clearInterval(progressInterval);
  }, [currentModuleSet]);

  const getStatusColor = (color: string) => {
    switch (color) {
      case 'green': return 'bg-green-50 border-green-400 text-green-800';
      case 'blue': return 'bg-blue-50 border-blue-400 text-blue-800';
      case 'gray': return 'bg-gray-50 border-gray-300 text-gray-600';
      case 'yellow': return 'bg-yellow-50 border-yellow-400 text-yellow-800';
      case 'orange': return 'bg-orange-50 border-orange-400 text-orange-800';
      case 'purple': return 'bg-purple-50 border-purple-400 text-purple-800';
      default: return 'bg-gray-50 border-gray-300 text-gray-600';
    }
  };

  const getStatusBadgeColor = (color: string) => {
    switch (color) {
      case 'green': return 'bg-green-100 text-green-600';
      case 'blue': return 'bg-blue-100 text-blue-600';
      case 'gray': return 'bg-gray-100 text-gray-500';
      case 'yellow': return 'bg-yellow-100 text-yellow-600 animate-pulse';
      case 'orange': return 'bg-orange-100 text-orange-600 animate-pulse';
      case 'purple': return 'bg-purple-100 text-purple-600 animate-pulse';
      default: return 'bg-gray-100 text-gray-500';
    }
  };

  return (
    <section className="min-h-screen bg-gradient-to-br from-blue-100 to-indigo-50 flex items-center">
      <div className="container mx-auto px-4 sm:px-6 lg:px-8">
        <div className="max-w-7xl mx-auto">
          <div className="grid grid-cols-1 lg:grid-cols-2 items-center gap-12">
            {/* Left Text Content */}
            <div className="text-center lg:text-left px-4 sm:px-6 lg:px-8">
              <h1 className="text-3xl sm:text-4xl md:text-5xl lg:text-6xl font-black text-gray-900 mb-4 leading-tight tracking-wide">
                {title}
              </h1>
              
              <h2 className="text-lg sm:text-xl md:text-2xl lg:text-3xl font-bold text-blue-600 mb-4">
                {subtitle}
              </h2>
              
              <p className="text-base sm:text-lg md:text-xl lg:text-2xl text-gray-600 mb-8 leading-relaxed max-w-xl lg:max-w-2xl mx-auto lg:mx-0 font-medium">
                {description}
              </p>
              
              <div className="flex flex-col sm:flex-row gap-4 justify-center lg:justify-start">
                {ctaLink && !onCTA ? (
                  <Link href={ctaLink}>
                    <button 
                      className="bg-black hover:bg-gray-800 text-white font-bold py-4 px-8 rounded-lg text-lg transition-colors duration-200 shadow-lg hover:shadow-xl"
                      aria-label={ctaText}
                    >
                      {ctaText}
                    </button>
                  </Link>
                ) : (
                  <button 
                    onClick={onCTA}
                    className="bg-black hover:bg-gray-800 text-white font-bold py-4 px-8 rounded-lg text-lg transition-colors duration-200 shadow-lg hover:shadow-xl"
                    aria-label={ctaText}
                  >
                    {ctaText}
                  </button>
                )}
              </div>
            </div>

            {/* Right Illustration */}
            <div className="flex justify-center">
              <div className="relative w-full max-w-sm sm:max-w-md md:max-w-lg lg:max-w-xl">
                {/* Architecture/Course Illustration */}
                <div className="bg-white rounded-2xl shadow-2xl p-8 relative">
                  <div className="space-y-6">
                    {/* Certificate Icon */}
                    <div className="flex justify-center">
                      <Image
                        src="/assets/logo-iai.png"
                        alt="IAI Logo"
                        width={120}
                        height={120}
                        className="object-contain"
                      />
                    </div>
                    
                    {/* Course modules preview - Smooth Scrolling with Interactive Cursor */}
                    <div className="h-48 overflow-hidden relative">
                      <div 
                        className="space-y-3 transition-transform duration-1000 ease-in-out"
                        style={{
                          transform: `translateY(-${currentModuleSet * 192}px)` // 192px = 48px per module * 4 (3 modules + spacing)
                        }}
                      >
                        {moduleSets.map((moduleSet, setIndex) => (
                          <div key={setIndex} className="space-y-3">
                            {(setIndex === currentModuleSet ? getAnimatedModuleData() : moduleSet).map((module, moduleIndex) => (
                              <div 
                                key={`${setIndex}-${moduleIndex}`}
                                className={`${getStatusColor(module.color)} border-l-4 p-3 rounded transition-all duration-500 ${
                                  animatingModules.includes(moduleIndex) ? 'shadow-lg' : ''
                                }`}
                              >
                                <div className="flex justify-between items-center">
                                  <span className="text-sm font-medium">{module.title}</span>
                                  <span className={`text-xs px-2 py-1 rounded-full transition-all duration-300 ${getStatusBadgeColor(module.color)}`}>
                                    {module.status}
                                  </span>
                                </div>
                              </div>
                            ))}
                            {/* Add spacer between sets */}
                            {setIndex < moduleSets.length - 1 && <div className="h-3"></div>}
                          </div>
                        ))}
                      </div>
                      
                      {/* Animated Cursor */}
                      {cursorPosition.visible && (
                        <div 
                          className="absolute pointer-events-none transition-all duration-500 ease-out z-10"
                          style={{
                            left: `${cursorPosition.x}px`,
                            top: `${cursorPosition.y}px`,
                            transform: 'translate(-50%, -50%)',
                            opacity: cursorPosition.opacity
                          }}
                        >
                          {/* Mouse Cursor Shape */}
                          <div className="relative">
                            <svg width="24" height="24" viewBox="0 0 24 24" className="drop-shadow-lg">
                              <path 
                                d="M5 3v18l5.5-5H18L5 3z" 
                                fill="#1d4ed8" 
                                stroke="#ffffff" 
                                strokeWidth="1"
                                className="animate-pulse"
                              />
                              <path 
                                d="M5 3v18l5.5-5H18L5 3z" 
                                fill="#3b82f6" 
                                opacity="0.8"
                              />
                            </svg>
                            {/* Glow effect */}
                            <div className="absolute inset-0 bg-blue-400 rounded-full blur-md opacity-50 animate-ping"></div>
                          </div>
                        </div>
                      )}
                      
                      {/* Click Effect */}
                      {clickEffect.active && (
                        <div 
                          className="absolute pointer-events-none z-20"
                          style={{
                            left: `${clickEffect.x}px`,
                            top: `${clickEffect.y}px`,
                            transform: 'translate(-50%, -50%)'
                          }}
                        >
                          <div className="w-8 h-8 border-2 border-blue-500 rounded-full animate-ping"></div>
                          <div className="w-4 h-4 bg-blue-500 rounded-full absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2"></div>
                        </div>
                      )}
                      
                      {/* Fade overlay at top and bottom */}
                      <div className="absolute top-0 left-0 right-0 h-6 bg-gradient-to-b from-white to-transparent pointer-events-none z-5"></div>
                      <div className="absolute bottom-0 left-0 right-0 h-6 bg-gradient-to-t from-white to-transparent pointer-events-none z-5"></div>
                    </div>
                    
                    {/* Progress indicator - Animated */}
                    <div className="pt-4 border-t border-gray-200">
                      <div className="flex justify-between items-center mb-2">
                        <span className="text-sm font-medium text-gray-700">Kemajuan Keseluruhan</span>
                        <span className="text-sm text-gray-500 transition-all duration-300">
                          {Math.round(progress)}%
                        </span>
                      </div>
                      <div className="w-full bg-gray-200 rounded-full h-3 overflow-hidden">
                        <div 
                          className="bg-blue-600 h-3 rounded-full transition-all duration-1000 ease-out relative"
                          style={{ width: `${progress}%` }}
                        >
                          <div className="absolute inset-0 bg-gradient-to-r from-transparent via-white to-transparent opacity-30 animate-pulse"></div>
                        </div>
                      </div>
                      
                      {/* Progress indicator dots */}
                      <div className="flex justify-center mt-3 space-x-2">
                        {moduleSets.map((_, index) => (
                          <div
                            key={index}
                            className={`w-2 h-2 rounded-full transition-all duration-300 ${
                              index === currentModuleSet ? 'bg-blue-600 scale-125' : 'bg-gray-300'
                            }`}
                          />
                        ))}
                      </div>
                    </div>
                  </div>
                  
                  {/* Floating elements */}
                  <div className="absolute -top-4 -right-4 w-12 h-12 bg-yellow-400 rounded-full flex items-center justify-center shadow-lg">
                    <AwardIcon className="w-6 h-6 text-yellow-800" />
                  </div>
                  
                  <div className="absolute -bottom-4 -left-4 w-12 h-12 bg-green-400 rounded-full flex items-center justify-center shadow-lg">
                    <BookOpenIcon className="w-6 h-6 text-green-800" />
                  </div>
                  
                  {/* Small Modal Popup within white container */}
                  {modalOpen && modalModule && (
                    <div className="absolute inset-0 flex items-center justify-center z-50 animate-in fade-in duration-300 rounded-2xl">
                      <div className="bg-white rounded-xl p-4 max-w-xs w-full mx-4 transform transition-all duration-500 scale-100 animate-in zoom-in-95 shadow-2xl border">
                        <div className="text-center">
                          <div className="w-10 h-10 bg-yellow-100 rounded-full flex items-center justify-center mx-auto mb-3">
                            <svg className="w-5 h-5 text-yellow-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z" />
                            </svg>
                          </div>
                          
                          <h3 className="text-sm font-bold text-gray-900 mb-2">
                            {modalStatus === 'Terbuka' ? 'Modul Berhasil Dibuka!' : 'Membuka Modul'}
                          </h3>
                          
                          <div className="mb-3">
                            <span className={`inline-block px-3 py-1 rounded-full text-xs font-medium ${
                              modalStatus === 'Terbuka' 
                                ? 'bg-green-100 text-green-800' 
                                : 'bg-yellow-100 text-yellow-800'
                            }`}>
                              {modalStatus}
                            </span>
                          </div>
                          
                          <div className="w-full bg-gray-200 rounded-full h-1.5 mb-3">
                            <div className={`h-1.5 rounded-full transition-all duration-1000 ${
                              modalStatus === 'Terbuka' ? 'bg-green-600' : 'bg-yellow-600'
                            }`} style={{ width: `${modalProgress}%` }}></div>
                          </div>
                        </div>
                      </div>
                    </div>
                  )}
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
}