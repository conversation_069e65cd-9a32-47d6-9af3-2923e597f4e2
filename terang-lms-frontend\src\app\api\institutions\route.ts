import { NextRequest, NextResponse } from 'next/server';
import { query } from '@/lib/db/raw';
import { ApiResponse } from '@/types/database';

// GET /api/institutions - Get all institutions
export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const search = searchParams.get('search');
    const limit = parseInt(searchParams.get('limit') || '50');
    const offset = parseInt(searchParams.get('offset') || '0');

    let institutions;
    let countResult;

    if (search) {
      // With search filter
      const searchPattern = `%${search.toLowerCase()}%`;

      institutions = await query`
        SELECT
          id,
          name,
          type,
          subscription_plan,
          billing_cycle,
          payment_status,
          payment_due_date,
          student_count,
          teacher_count,
          created_at,
          updated_at
        FROM institutions
        WHERE LOWER(name) LIKE ${searchPattern} OR LOWER(type::text) LIKE ${searchPattern}
        ORDER BY created_at DESC
        LIMIT ${limit}
        OFFSET ${offset}
      `;

      // Get total count for pagination with search
      countResult = await query`
        SELECT COUNT(*) as total
        FROM institutions
        WHERE LOWER(name) LIKE ${searchPattern} OR LOWER(type::text) LIKE ${searchPattern}
      `;
    } else {
      // Without search filter
      institutions = await query`
        SELECT
          id,
          name,
          type,
          subscription_plan,
          billing_cycle,
          payment_status,
          payment_due_date,
          student_count,
          teacher_count,
          created_at,
          updated_at
        FROM institutions
        ORDER BY created_at DESC
        LIMIT ${limit}
        OFFSET ${offset}
      `;

      // Get total count for pagination without search
      countResult = await query`
        SELECT COUNT(*) as total
        FROM institutions
      `;
    }

    return NextResponse.json({
      success: true,
      data: {
        institutions,
        total: parseInt(countResult[0].total),
        limit,
        offset
      },
      message: 'Institutions retrieved successfully'
    } as ApiResponse);
  } catch (error) {
    console.error('Get institutions error:', error);
    return NextResponse.json(
      {
        success: false,
        error: 'Failed to retrieve institutions'
      } as ApiResponse,
      { status: 500 }
    );
  }
}

// POST /api/institutions - Create new institution
export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const {
      name,
      type,
      subscriptionPlan,
      billingCycle,
      studentCount,
      teacherCount,
      paymentStatus
    } = body;

    // Validate required fields
    if (!name || !type) {
      return NextResponse.json(
        { success: false, error: 'Name and type are required' } as ApiResponse,
        { status: 400 }
      );
    }

    // Calculate payment due date (30 days from now for monthly, 365 days for yearly)
    const daysToAdd = billingCycle === 'yearly' ? 365 : 30;
    const paymentDueDate = new Date();
    paymentDueDate.setDate(paymentDueDate.getDate() + daysToAdd);

    const result = await query`
      INSERT INTO institutions (
        name,
        type,
        subscription_plan,
        billing_cycle,
        payment_status,
        payment_due_date,
        student_count,
        teacher_count
      ) VALUES (
        ${name},
        ${type},
        ${subscriptionPlan || 'basic'},
        ${billingCycle || 'monthly'},
        ${paymentStatus || 'unpaid'},
        ${paymentDueDate.toISOString()},
        ${studentCount || 0},
        ${teacherCount || 0}
      ) RETURNING *
    `;

    return NextResponse.json({
      success: true,
      data: result[0],
      message: 'Institution created successfully'
    } as ApiResponse);
  } catch (error) {
    console.error('Create institution error:', error);
    return NextResponse.json(
      { success: false, error: 'Failed to create institution' } as ApiResponse,
      { status: 500 }
    );
  }
}
