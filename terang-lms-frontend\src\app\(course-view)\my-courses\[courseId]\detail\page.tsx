'use client';

import React, { useState } from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import {
  BookOpen01Icon as BookOpenIcon,
  UserIcon as UsersIcon,
  Clock01Icon as ClockIcon,
  Calendar03Icon as CalendarIcon,
  CheckmarkCircle01Icon as CheckCircleIcon,
  DollarCircleIcon,
  Briefcase01Icon as BriefcaseIcon,
  GraduateMaleIcon as GraduationCapIcon,
  StarsIcon as StarIcon,
  Award05Icon as AwardIcon,
  Building01Icon as BuildingIcon,
  CustomerSupportIcon as SupportIcon,
  ArrowLeft01Icon as ArrowLeftIcon
} from 'hugeicons-react';
import Link from 'next/link';
import { useParams } from 'next/navigation';
import { useEnrollment } from '@/contexts/enrollment-context';
import CourseDetailTabs from '@/components/lms/course-detail-tabs';

const CourseDetailPage: React.FC = () => {
  const params = useParams();
  const courseId = params.courseId as string;
  const { getCourseById } = useEnrollment();
  
  const [activeTab, setActiveTab] = useState('overview');
  
  // Get the specific course based on courseId from URL
  const courseData = getCourseById(courseId);

  if (!courseData) {
    return (
      <div className='min-h-screen bg-gray-50 flex items-center justify-center'>
        <div className='text-center'>
          <h1 className='text-2xl font-bold text-gray-900 mb-4'>Kursus Tidak Ditemukan</h1>
          <p className='text-gray-600 mb-6'>Kursus yang Anda cari tidak tersedia.</p>
          <Link href='/my-courses'>
            <Button>
              <ArrowLeftIcon className='mr-2 h-4 w-4' />
              Kembali ke Kursus Saya
            </Button>
          </Link>
        </div>
      </div>
    );
  }

  const formatPrice = (price: number, currency: string = 'IDR') => {
    if (currency === 'IDR') {
      return 'Rp' + new Intl.NumberFormat('id-ID').format(price);
    }
    return new Intl.NumberFormat('id-ID', {
      style: 'currency',
      currency: currency
    }).format(price);
  };

  const navigationItems = [
    { id: 'overview', label: 'Ringkasan', icon: BookOpenIcon },
    { id: 'admissions', label: 'Penerimaan', icon: GraduationCapIcon },
    { id: 'academics', label: 'Akademik', icon: AwardIcon },
    { id: 'tuition', label: 'Biaya', icon: DollarCircleIcon },
    { id: 'careers', label: 'Karier', icon: BriefcaseIcon },
    { id: 'experience', label: 'Pengalaman', icon: StarIcon }
  ];

  return (
    <div className='min-h-screen bg-gray-50'>
      {/* Header with Back Button */}
      <div className='bg-white border-b'>
        <div className='max-w-full px-6 py-4'>
          <div className='flex items-center justify-between'>
            <div className='flex items-center space-x-4'>
              <Link href='/my-courses'>
                <Button
                  variant='outline'
                  size='sm'
                  className='flex items-center space-x-2'
                >
                  <ArrowLeftIcon className='h-4 w-4' />
                  <span>Kembali ke Kursus Saya</span>
                </Button>
              </Link>
              <div className='flex items-center space-x-3'>
                <BuildingIcon className='h-8 w-8 text-[var(--iai-primary)]' />
                <div>
                  <h1 className='text-2xl font-bold text-gray-900'>
                    {courseData.name}
                  </h1>
                  <p className='text-gray-600'>Kode Kursus: {courseData.code}</p>
                  <p className='text-gray-600'>
                    Instruktur: {courseData.instructor}
                  </p>
                </div>
              </div>
            </div>
            <div className='flex items-center space-x-4'>
              <Badge
                variant={
                  courseData.status === 'completed'
                    ? 'default'
                    : 'secondary'
                }
              >
                {courseData.status === 'completed'
                  ? 'Selesai'
                  : 'Sedang Belajar'}
              </Badge>
            </div>
          </div>
        </div>
      </div>

      {/* Main Content Layout */}
      <div className='flex h-[calc(100vh-80px)]'>
        {/* Left Navigation Panel */}
        <div className='w-80 bg-white border-r flex-shrink-0'>
          <div className='p-6'>
            <h2 className='text-lg font-semibold text-gray-900 mb-4'>
              Detail Kursus
            </h2>
            <nav className='space-y-2'>
              {navigationItems.map((item) => {
                const Icon = item.icon;
                return (
                  <button
                    key={item.id}
                    onClick={() => setActiveTab(item.id)}
                    className={`w-full flex items-center space-x-3 px-3 py-2 rounded-lg text-left transition-colors ${
                      activeTab === item.id
                        ? 'bg-blue-50 text-blue-700 border border-blue-200'
                        : 'text-gray-700 hover:bg-gray-50'
                    }`}
                  >
                    <Icon className='h-4 w-4' />
                    <span className='text-sm font-medium'>{item.label}</span>
                  </button>
                );
              })}
            </nav>
          </div>
        </div>

        {/* Right Content Area */}
        <div className='flex-1 overflow-auto'>
          <div className='p-6'>
            <div className='max-w-4xl'>
              <CourseDetailTabs
                course={courseData}
                activeTab={activeTab}
                onTabChange={setActiveTab}
                hideTabsList={true}
              />
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default CourseDetailPage;
