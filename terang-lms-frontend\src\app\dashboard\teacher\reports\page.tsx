'use client';

import { useState } from 'react';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle
} from '@/components/ui/card';
import { But<PERSON> } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>ist, TabsTrigger } from '@/components/ui/tabs';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue
} from '@/components/ui/select';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow
} from '@/components/ui/table';
import { Progress } from '@/components/ui/progress';
import {
  BarChart,
  Bar,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  ResponsiveContainer,
  PieChart,
  Pie,
  Cell
} from 'recharts';
import {
  TrendingUp,
  Users,
  BookOpen,
  Award,
  Download,
  Eye
} from 'lucide-react';

export default function ReportsPage() {
  const [selectedCourse, setSelectedCourse] = useState('all');

  // Mock data - in real app, this would come from API
  const courses = [
    { id: 'all', name: 'All Courses' },
    { id: '1', name: 'Introduction to Algebra' },
    { id: '2', name: 'Physics Fundamentals' },
    { id: '3', name: 'Chemistry Basics' }
  ];

  const progressData = [
    { name: 'Module 1', completed: 85, inProgress: 10, notStarted: 5 },
    { name: 'Module 2', completed: 70, inProgress: 20, notStarted: 10 },
    { name: 'Module 3', completed: 55, inProgress: 25, notStarted: 20 },
    { name: 'Module 4', completed: 40, inProgress: 30, notStarted: 30 }
  ];

  const completionData = [
    { name: 'Completed', value: 65, color: '#22c55e' },
    { name: 'In Progress', value: 25, color: '#f59e0b' },
    { name: 'Not Started', value: 10, color: '#ef4444' }
  ];

  const studentProgress = [
    {
      id: 1,
      name: 'Alice Johnson',
      email: '<EMAIL>',
      course: 'Introduction to Algebra',
      overallProgress: 85,
      quizAverage: 92,
      lastActivity: '2024-08-03',
      status: 'on_track'
    },
    {
      id: 2,
      name: 'Bob Wilson',
      email: '<EMAIL>',
      course: 'Physics Fundamentals',
      overallProgress: 45,
      quizAverage: 78,
      lastActivity: '2024-08-01',
      status: 'behind'
    },
    {
      id: 3,
      name: 'Carol Brown',
      email: '<EMAIL>',
      course: 'Chemistry Basics',
      overallProgress: 100,
      quizAverage: 95,
      lastActivity: '2024-08-02',
      status: 'completed'
    }
  ];

  const quizResults = [
    {
      id: 1,
      studentName: 'Alice Johnson',
      quizName: 'Module 1 Quiz',
      course: 'Introduction to Algebra',
      score: 92,
      maxScore: 100,
      submittedAt: '2024-08-01',
      status: 'passed'
    },
    {
      id: 2,
      studentName: 'Bob Wilson',
      quizName: 'Module 1 Quiz',
      course: 'Physics Fundamentals',
      score: 65,
      maxScore: 100,
      submittedAt: '2024-08-02',
      status: 'needs_review'
    }
  ];

  const certificates = [
    {
      id: 1,
      studentName: 'Carol Brown',
      course: 'Chemistry Basics',
      completedAt: '2024-07-30',
      certificateId: 'CERT-2024-001',
      status: 'issued'
    },
    {
      id: 2,
      studentName: 'Alice Johnson',
      course: 'Introduction to Algebra',
      completedAt: '2024-08-01',
      certificateId: 'CERT-2024-002',
      status: 'pending_verification'
    }
  ];

  const getStatusBadge = (status: string) => {
    const variants: Record<string, any> = {
      on_track: 'default',
      behind: 'destructive',
      completed: 'secondary',
      passed: 'default',
      needs_review: 'destructive',
      issued: 'default',
      pending_verification: 'outline'
    };
    return (
      <Badge variant={variants[status] || 'outline'}>
        {status.replace('_', ' ')}
      </Badge>
    );
  };

  return (
    <div className='space-y-6'>
      <div className='flex items-center justify-between'>
        <div>
          <h1 className='text-3xl font-bold tracking-tight'>
            Reports & Analytics
          </h1>
          <p className='text-muted-foreground'>
            Track student progress and course performance
          </p>
        </div>
        <div className='flex space-x-2'>
          <Select value={selectedCourse} onValueChange={setSelectedCourse}>
            <SelectTrigger className='w-48'>
              <SelectValue />
            </SelectTrigger>
            <SelectContent>
              {courses.map((course) => (
                <SelectItem key={course.id} value={course.id}>
                  {course.name}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
          <Button variant='outline'>
            <Download className='mr-2 h-4 w-4' />
            Export
          </Button>
        </div>
      </div>

      {/* Overview Cards */}
      <div className='grid gap-4 md:grid-cols-2 lg:grid-cols-4'>
        <Card>
          <CardHeader className='flex flex-row items-center justify-between space-y-0 pb-2'>
            <CardTitle className='text-sm font-medium'>
              Total Students
            </CardTitle>
            <Users className='text-muted-foreground h-4 w-4' />
          </CardHeader>
          <CardContent>
            <div className='text-2xl font-bold'>105</div>
            <p className='text-muted-foreground text-xs'>Across all courses</p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className='flex flex-row items-center justify-between space-y-0 pb-2'>
            <CardTitle className='text-sm font-medium'>
              Average Progress
            </CardTitle>
            <TrendingUp className='text-muted-foreground h-4 w-4' />
          </CardHeader>
          <CardContent>
            <div className='text-2xl font-bold'>76%</div>
            <p className='text-muted-foreground text-xs'>+5% from last month</p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className='flex flex-row items-center justify-between space-y-0 pb-2'>
            <CardTitle className='text-sm font-medium'>
              Completed Courses
            </CardTitle>
            <BookOpen className='text-muted-foreground h-4 w-4' />
          </CardHeader>
          <CardContent>
            <div className='text-2xl font-bold'>68</div>
            <p className='text-muted-foreground text-xs'>This month</p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className='flex flex-row items-center justify-between space-y-0 pb-2'>
            <CardTitle className='text-sm font-medium'>
              Certificates Issued
            </CardTitle>
            <Award className='text-muted-foreground h-4 w-4' />
          </CardHeader>
          <CardContent>
            <div className='text-2xl font-bold'>45</div>
            <p className='text-muted-foreground text-xs'>This month</p>
          </CardContent>
        </Card>
      </div>

      <Tabs defaultValue='progress' className='space-y-6'>
        <TabsList>
          <TabsTrigger value='progress'>Student Progress</TabsTrigger>
          <TabsTrigger value='quizzes'>Quiz Results</TabsTrigger>
          <TabsTrigger value='certificates'>Certificates</TabsTrigger>
          <TabsTrigger value='analytics'>Analytics</TabsTrigger>
        </TabsList>

        <TabsContent value='progress'>
          <Card>
            <CardHeader>
              <CardTitle>Student Progress Overview</CardTitle>
              <CardDescription>
                Track individual student progress across courses
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className='rounded-md border'>
                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead>Student</TableHead>
                      <TableHead>Course</TableHead>
                      <TableHead>Progress</TableHead>
                      <TableHead>Quiz Average</TableHead>
                      <TableHead>Last Activity</TableHead>
                      <TableHead>Status</TableHead>
                      <TableHead className='w-[70px]'>Actions</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {studentProgress.map((student) => (
                      <TableRow key={student.id}>
                        <TableCell>
                          <div className='space-y-1'>
                            <p className='font-medium'>{student.name}</p>
                            <p className='text-muted-foreground text-sm'>
                              {student.email}
                            </p>
                          </div>
                        </TableCell>
                        <TableCell>{student.course}</TableCell>
                        <TableCell>
                          <div className='space-y-1'>
                            <Progress
                              value={student.overallProgress}
                              className='h-2'
                            />
                            <span className='text-sm'>
                              {student.overallProgress}%
                            </span>
                          </div>
                        </TableCell>
                        <TableCell>{student.quizAverage}%</TableCell>
                        <TableCell>
                          {new Date(student.lastActivity).toLocaleDateString()}
                        </TableCell>
                        <TableCell>{getStatusBadge(student.status)}</TableCell>
                        <TableCell>
                          <Button variant='ghost' size='sm'>
                            <Eye className='h-4 w-4' />
                          </Button>
                        </TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value='quizzes'>
          <Card>
            <CardHeader>
              <CardTitle>Quiz Results</CardTitle>
              <CardDescription>
                Review and validate quiz submissions
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className='rounded-md border'>
                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead>Student</TableHead>
                      <TableHead>Quiz</TableHead>
                      <TableHead>Course</TableHead>
                      <TableHead>Score</TableHead>
                      <TableHead>Submitted</TableHead>
                      <TableHead>Status</TableHead>
                      <TableHead className='w-[70px]'>Actions</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {quizResults.map((result) => (
                      <TableRow key={result.id}>
                        <TableCell>{result.studentName}</TableCell>
                        <TableCell>{result.quizName}</TableCell>
                        <TableCell>{result.course}</TableCell>
                        <TableCell>
                          <span className='font-medium'>
                            {result.score}/{result.maxScore}
                          </span>
                          <span className='text-muted-foreground ml-2 text-sm'>
                            (
                            {Math.round((result.score / result.maxScore) * 100)}
                            %)
                          </span>
                        </TableCell>
                        <TableCell>
                          {new Date(result.submittedAt).toLocaleDateString()}
                        </TableCell>
                        <TableCell>{getStatusBadge(result.status)}</TableCell>
                        <TableCell>
                          <Button variant='ghost' size='sm'>
                            <Eye className='h-4 w-4' />
                          </Button>
                        </TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value='certificates'>
          <Card>
            <CardHeader>
              <CardTitle>Certificate Management</CardTitle>
              <CardDescription>
                Manage and validate course completion certificates
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className='rounded-md border'>
                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead>Student</TableHead>
                      <TableHead>Course</TableHead>
                      <TableHead>Completed</TableHead>
                      <TableHead>Certificate ID</TableHead>
                      <TableHead>Status</TableHead>
                      <TableHead className='w-[100px]'>Actions</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {certificates.map((cert) => (
                      <TableRow key={cert.id}>
                        <TableCell>{cert.studentName}</TableCell>
                        <TableCell>{cert.course}</TableCell>
                        <TableCell>
                          {new Date(cert.completedAt).toLocaleDateString()}
                        </TableCell>
                        <TableCell>
                          <code className='bg-muted rounded px-1 text-sm'>
                            {cert.certificateId}
                          </code>
                        </TableCell>
                        <TableCell>{getStatusBadge(cert.status)}</TableCell>
                        <TableCell>
                          <div className='flex space-x-1'>
                            <Button variant='ghost' size='sm'>
                              <Eye className='h-3 w-3' />
                            </Button>
                            <Button variant='ghost' size='sm'>
                              <Download className='h-3 w-3' />
                            </Button>
                          </div>
                        </TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value='analytics'>
          <div className='grid gap-6 md:grid-cols-2'>
            <Card>
              <CardHeader>
                <CardTitle>Module Progress</CardTitle>
                <CardDescription>
                  Student progress across course modules
                </CardDescription>
              </CardHeader>
              <CardContent>
                <ResponsiveContainer width='100%' height={300}>
                  <BarChart data={progressData}>
                    <CartesianGrid strokeDasharray='3 3' />
                    <XAxis dataKey='name' />
                    <YAxis />
                    <Tooltip />
                    <Bar dataKey='completed' fill='#22c55e' name='Completed' />
                    <Bar
                      dataKey='inProgress'
                      fill='#f59e0b'
                      name='In Progress'
                    />
                    <Bar
                      dataKey='notStarted'
                      fill='#ef4444'
                      name='Not Started'
                    />
                  </BarChart>
                </ResponsiveContainer>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>Course Completion</CardTitle>
                <CardDescription>
                  Overall course completion distribution
                </CardDescription>
              </CardHeader>
              <CardContent>
                <ResponsiveContainer width='100%' height={300}>
                  <PieChart>
                    <Pie
                      data={completionData}
                      cx='50%'
                      cy='50%'
                      outerRadius={80}
                      dataKey='value'
                      label={({ name, value }) => `${name}: ${value}%`}
                    >
                      {completionData.map((entry, index) => (
                        <Cell key={`cell-${index}`} fill={entry.color} />
                      ))}
                    </Pie>
                    <Tooltip />
                  </PieChart>
                </ResponsiveContainer>
              </CardContent>
            </Card>
          </div>
        </TabsContent>
      </Tabs>
    </div>
  );
}
