import React, { useState, useEffect } from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';
import { 
  Play, 
  FileText, 
  BookMarked, 
  CheckCircle, 
  Lock, 
  ChevronLeft, 
  ChevronRight,
  Clock,
  Target,
  Award
} from 'lucide-react';
import { Course, Content, Quiz } from '@/types/lms';

interface ContentViewerProps {
  course: Course;
  currentModuleId: string;
  currentChapterId: string;
  currentContentId: string;
  onNavigate: (moduleId: string, chapterId: string, contentId: string) => void;
  onStartQuiz: (quizId: string, quizType: 'chapter' | 'module' | 'final') => void;
  onContentComplete: (contentId: string) => void;
}

export const ContentViewer: React.FC<ContentViewerProps> = ({
  course,
  currentModuleId,
  currentChapterId,
  currentContentId,
  onNavigate,
  onStartQuiz,
  onContentComplete
}) => {
  const [isCompleted, setIsCompleted] = useState(false);

  // Find current content
  const currentModule = course.modules.find(m => m.id === currentModuleId);
  const currentChapter = currentModule?.chapters.find(c => c.id === currentChapterId);
  const currentContent = currentChapter?.contents.find(c => c.id === currentContentId);

  // Find navigation info
  const moduleIndex = course.modules.findIndex(m => m.id === currentModuleId);
  const chapterIndex = currentModule?.chapters.findIndex(c => c.id === currentChapterId) || 0;
  const contentIndex = currentChapter?.contents.findIndex(c => c.id === currentContentId) || 0;

  // Navigation helpers
  const getNextContent = () => {
    if (!currentModule || !currentChapter || !currentContent) return null;
    
    const nextContentIndex = contentIndex + 1;
    if (nextContentIndex < currentChapter.contents.length) {
      return {
        moduleId: currentModuleId,
        chapterId: currentChapterId,
        contentId: currentChapter.contents[nextContentIndex].id
      };
    }
    
    // Check if there's a chapter quiz
    if (currentChapter.quiz && currentChapter.contents.every(c => c.isCompleted)) {
      return {
        moduleId: currentModuleId,
        chapterId: currentChapterId,
        contentId: 'chapter-quiz'
      };
    }
    
    // Check next chapter
    const nextChapterIndex = chapterIndex + 1;
    if (nextChapterIndex < currentModule.chapters.length) {
      const nextChapter = currentModule.chapters[nextChapterIndex];
      if (nextChapter.isUnlocked && nextChapter.contents.length > 0) {
        return {
          moduleId: currentModuleId,
          chapterId: nextChapter.id,
          contentId: nextChapter.contents[0].id
        };
      }
    }
    
    // Check module quiz
    if (currentModule.moduleQuiz && currentModule.chapters.every(ch => 
      ch.contents.every(c => c.isCompleted) && ch.quiz.isPassed
    )) {
      return {
        moduleId: currentModuleId,
        chapterId: 'module-quiz',
        contentId: 'module-quiz'
      };
    }
    
    return null;
  };

  const getPreviousContent = () => {
    if (!currentModule || !currentChapter || !currentContent) return null;
    
    const prevContentIndex = contentIndex - 1;
    if (prevContentIndex >= 0) {
      return {
        moduleId: currentModuleId,
        chapterId: currentChapterId,
        contentId: currentChapter.contents[prevContentIndex].id
      };
    }
    
    // Check previous chapter
    const prevChapterIndex = chapterIndex - 1;
    if (prevChapterIndex >= 0) {
      const prevChapter = currentModule.chapters[prevChapterIndex];
      if (prevChapter.contents.length > 0) {
        return {
          moduleId: currentModuleId,
          chapterId: prevChapter.id,
          contentId: prevChapter.contents[prevChapter.contents.length - 1].id
        };
      }
    }
    
    return null;
  };

  const nextContent = getNextContent();
  const prevContent = getPreviousContent();

  useEffect(() => {
    if (currentContent) {
      setIsCompleted(currentContent.isCompleted);
    }
  }, [currentContent]);

  const handleContentComplete = () => {
    if (currentContent && !currentContent.isCompleted) {
      onContentComplete(currentContent.id);
      setIsCompleted(true);
    }
  };

  const handleNext = () => {
    if (nextContent) {
      if (nextContent.contentId === 'chapter-quiz') {
        onStartQuiz(currentChapter?.quiz?.id || '', 'chapter');
      } else if (nextContent.contentId === 'module-quiz') {
        onStartQuiz(currentModule?.moduleQuiz?.id || '', 'module');
      } else {
        onNavigate(nextContent.moduleId, nextContent.chapterId, nextContent.contentId);
      }
    }
  };

  const handlePrevious = () => {
    if (prevContent) {
      onNavigate(prevContent.moduleId, prevContent.chapterId, prevContent.contentId);
    }
  };

  if (!currentContent) {
    return (
      <div className="flex items-center justify-center h-96">
        <div className="text-center">
          <BookMarked className="h-12 w-12 text-gray-400 mx-auto mb-4" />
          <h3 className="text-lg font-semibold text-gray-900 mb-2">Konten tidak ditemukan</h3>
          <p className="text-gray-600">Silakan pilih konten dari navigasi di sebelah kiri.</p>
        </div>
      </div>
    );
  }

  const getContentIcon = (type: string) => {
    switch (type) {
      case 'video':
        return <Play className="h-5 w-5 text-red-500" />;
      case 'pdf':
        return <FileText className="h-5 w-5 text-red-600" />;
      case 'zoom-recording':
        return <Play className="h-5 w-5 text-blue-500" />;
      default:
        return <BookMarked className="h-5 w-5 text-blue-500" />;
    }
  };

  const getContentTypeLabel = (type: string) => {
    switch (type) {
      case 'video':
        return 'Video';
      case 'pdf':
        return 'PDF';
      case 'zoom-recording':
        return 'Zoom Recording';
      default:
        return 'Materi';
    }
  };

  return (
    <div className="max-w-4xl mx-auto">
      {/* Content Header */}
      <div className="mb-6">
        <div className="flex items-center gap-2 mb-2">
          {getContentIcon(currentContent.type)}
          <Badge variant="outline" className="text-xs">
            {getContentTypeLabel(currentContent.type)}
          </Badge>
          {currentContent.duration && (
            <div className="flex items-center text-sm text-gray-500 ml-2">
              <Clock className="h-4 w-4 mr-1" />
              {currentContent.duration}
            </div>
          )}
        </div>
        <h1 className="text-2xl font-bold text-gray-900 mb-2">
          {currentContent.title}
        </h1>
        <p className="text-gray-600">
          {currentModule?.title} • {currentChapter?.title}
        </p>
      </div>

      {/* Content Body */}
      <div className="bg-white rounded-lg border p-6 mb-6">
        {currentContent.type === 'video' && (
          <div className="aspect-video bg-gray-100 rounded-lg flex items-center justify-center mb-4">
            <div className="text-center">
              <Play className="h-16 w-16 text-gray-400 mx-auto mb-2" />
              <p className="text-gray-600">Video Player</p>
              <p className="text-sm text-gray-500">URL: {currentContent.url}</p>
            </div>
          </div>
        )}
        
        {currentContent.type === 'pdf' && (
          <div className="aspect-[4/3] bg-gray-100 rounded-lg flex items-center justify-center mb-4">
            <div className="text-center">
              <FileText className="h-16 w-16 text-gray-400 mx-auto mb-2" />
              <p className="text-gray-600">PDF Viewer</p>
              <p className="text-sm text-gray-500">URL: {currentContent.url}</p>
            </div>
          </div>
        )}
        
        {currentContent.type === 'zoom-recording' && (
          <div className="aspect-video bg-gray-100 rounded-lg flex items-center justify-center mb-4">
            <div className="text-center">
              <Play className="h-16 w-16 text-gray-400 mx-auto mb-2" />
              <p className="text-gray-600">Zoom Recording</p>
              <p className="text-sm text-gray-500">URL: {currentContent.url}</p>
            </div>
          </div>
        )}

        {currentContent.description && (
          <div className="prose max-w-none">
            <p className="text-gray-700">{currentContent.description}</p>
          </div>
        )}
      </div>

      {/* Content Actions */}
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-4">
          {!isCompleted ? (
            <Button 
              onClick={handleContentComplete}
              className="flex items-center space-x-2"
            >
              <CheckCircle className="h-4 w-4" />
              <span>Tandai Selesai</span>
            </Button>
          ) : (
            <div className="flex items-center text-green-600">
              <CheckCircle className="h-4 w-4 mr-2" />
              <span className="text-sm font-medium">Selesai</span>
            </div>
          )}
        </div>

        <div className="flex items-center space-x-2">
          <Button
            variant="outline"
            onClick={handlePrevious}
            disabled={!prevContent}
            className="flex items-center space-x-2"
          >
            <ChevronLeft className="h-4 w-4" />
            <span>Sebelumnya</span>
          </Button>
          
          <Button
            onClick={handleNext}
            disabled={!nextContent}
            className="flex items-center space-x-2"
          >
            <span>Selanjutnya</span>
            <ChevronRight className="h-4 w-4" />
          </Button>
        </div>
      </div>

      {/* Progress Indicator */}
      <div className="mt-6">
        <div className="flex items-center justify-between text-sm text-gray-600 mb-2">
          <span>Kemajuan Bab</span>
          <span>{Math.round((contentIndex + 1) / (currentChapter?.contents.length || 1) * 100)}%</span>
        </div>
        <Progress 
          value={(contentIndex + 1) / (currentChapter?.contents.length || 1) * 100} 
          className="h-2" 
        />
      </div>
    </div>
  );
};
