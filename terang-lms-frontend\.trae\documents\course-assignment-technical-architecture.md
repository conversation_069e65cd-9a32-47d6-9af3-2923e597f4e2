# Technical Architecture Document - Course Assignment ke Class

## 1. Architecture Design

```mermaid
graph TD
    A[User Browser] --> B[React Frontend Application]
    B --> C[Supabase SDK]
    C --> D[Supabase Service]
    B --> E[Next.js API Routes]
    E --> F[Drizzle ORM]
    F --> G[PostgreSQL Database]

    subgraph "Frontend Layer"
        B
    end

    subgraph "API Layer"
        E
        F
    end

    subgraph "Data Layer"
        G
        D
    end

    subgraph "Authentication"
        H[Supabase Auth]
        C --> H
    end
```

## 2. Technology Description

- **Frontend**: React@18 + Next.js@14 + TypeScript + Tailwind CSS + Shadcn/ui
- **Backend**: Next.js API Routes + Drizzle ORM
- **Database**: Supabase (PostgreSQL)
- **Authentication**: Supabase Auth
- **State Management**: React Query (TanStack Query) + Zustand
- **UI Components**: Shadcn/ui + Radix UI + Lucide React Icons

## 3. Route Definitions

| Route | Purpose |
|-------|---------|
| `/dashboard/teacher` | Teacher dashboard utama dengan navigation ke course assignments |
| `/dashboard/teacher/enrollments` | Course assignment management page dengan table dan assignment dialog |
| `/dashboard/teacher/classes/[id]` | Class detail page dengan assigned courses section |
| `/dashboard/teacher/courses/[id]` | Course detail page dengan assigned classes section |
| `/api/enrollments` | API endpoint untuk course assignment operations (GET, POST) |
| `/api/enrollments/[id]` | API endpoint untuk individual assignment operations (DELETE) |

## 4. API Definitions

### 4.1 Core API

#### Course Assignment Management

**GET /api/enrollments**

Request:
| Param Name | Param Type | isRequired | Description |
|------------|------------|------------|-------------|
| type | string | true | "course" untuk course assignments |
| teacherId | number | true | ID teacher yang meminta data |
| courseId | number | false | Filter berdasarkan course tertentu |
| classId | number | false | Filter berdasarkan class tertentu |

Response:
| Param Name | Param Type | Description |
|------------|------------|-------------|
| enrollments | CourseEnrollmentWithDetails[] | Array course assignments dengan details |
| success | boolean | Status response |

Example Response:
```json
{
  "enrollments": [
    {
      "id": 1,
      "courseId": 5,
      "classId": 3,
      "enrolledAt": "2024-01-15T10:30:00Z",
      "course": {
        "id": 5,
        "name": "Mathematics Grade 10",
        "courseCode": "MATH10A",
        "description": "Advanced mathematics for grade 10"
      },
      "class": {
        "id": 3,
        "name": "Class 10A",
        "studentCount": 25
      }
    }
  ],
  "success": true
}
```

**POST /api/enrollments**

Request:
| Param Name | Param Type | isRequired | Description |
|------------|------------|------------|-------------|
| type | string | true | "course" untuk course assignment |
| courseId | number | true | ID course yang akan di-assign |
| classId | number | true | ID class tujuan assignment |
| teacherId | number | true | ID teacher yang melakukan assignment |

Response:
| Param Name | Param Type | Description |
|------------|------------|-------------|
| enrollment | CourseEnrollment | Data assignment yang baru dibuat |
| message | string | Success message |
| success | boolean | Status response |

Example Request:
```json
{
  "type": "course",
  "courseId": 5,
  "classId": 3,
  "teacherId": 2
}
```

Example Response:
```json
{
  "enrollment": {
    "id": 1,
    "courseId": 5,
    "classId": 3,
    "enrolledAt": "2024-01-15T10:30:00Z"
  },
  "message": "Course enrolled to class successfully",
  "success": true
}
```

**DELETE /api/enrollments/[id]**

Request:
| Param Name | Param Type | isRequired | Description |
|------------|------------|------------|-------------|
| id | number | true | Course enrollment ID (URL parameter) |
| teacherId | number | true | ID teacher untuk authorization |

Response:
| Param Name | Param Type | Description |
|------------|------------|-------------|
| success | boolean | Status penghapusan |
| message | string | Success/error message |

Example Request Body:
```json
{
  "teacherId": 2
}
```

Example Response:
```json
{
  "success": true,
  "message": "Course assignment removed successfully"
}
```

### 4.2 TypeScript Type Definitions

```typescript
// Database Models
export type CourseEnrollment = {
  id: number;
  courseId: number;
  classId: number;
  enrolledAt: Date;
};

export type Course = {
  id: number;
  name: string;
  courseCode: string;
  description: string | null;
  teacherId: number;
  institutionId: number;
  createdAt: Date;
  updatedAt: Date;
};

export type Class = {
  id: number;
  name: string;
  description: string | null;
  teacherId: number;
  institutionId: number;
  createdAt: Date;
  updatedAt: Date;
};

// Extended Types untuk UI
export type CourseEnrollmentWithDetails = CourseEnrollment & {
  course: Course;
  class: Class & {
    studentCount: number;
  };
};

export type ClassWithCourses = Class & {
  courseEnrollments: (CourseEnrollment & {
    course: Course;
  })[];
  studentCount: number;
};

export type CourseWithClasses = Course & {
  courseEnrollments: (CourseEnrollment & {
    class: Class & {
      studentCount: number;
    };
  })[];
};

// API Request/Response Types
export type AssignCourseRequest = {
  type: 'course';
  courseId: number;
  classId: number;
  teacherId: number;
};

export type AssignCourseResponse = {
  enrollment: CourseEnrollment;
  message: string;
  success: boolean;
};

export type GetEnrollmentsResponse = {
  enrollments: CourseEnrollmentWithDetails[];
  success: boolean;
};

export type RemoveAssignmentRequest = {
  teacherId: number;
};

export type RemoveAssignmentResponse = {
  success: boolean;
  message: string;
};

// Form Types
export type CourseAssignmentForm = {
  courseId: string;
  classId: string;
};

// Selection Types untuk Dropdowns
export type CourseOption = {
  value: string;
  label: string;
  courseCode: string;
};

export type ClassOption = {
  value: string;
  label: string;
  studentCount: number;
};
```

## 5. Server Architecture Diagram

```mermaid
graph TD
    A[Client Request] --> B[Next.js API Route Handler]
    B --> C[Authentication Middleware]
    C --> D[Request Validation]
    D --> E[Business Logic Layer]
    E --> F[Data Access Layer - Drizzle ORM]
    F --> G[(PostgreSQL Database)]
    
    subgraph "API Layer"
        B
        C
        D
    end
    
    subgraph "Business Layer"
        E
        H[Authorization Service]
        I[Validation Service]
        E --> H
        E --> I
    end
    
    subgraph "Data Layer"
        F
        G
    end
    
    subgraph "External Services"
        J[Supabase Auth]
        C --> J
    end
```

## 6. Data Model

### 6.1 Data Model Definition

```mermaid
erDiagram
    USERS ||--o{ COURSES : creates
    USERS ||--o{ CLASSES : manages
    INSTITUTIONS ||--o{ USERS : employs
    INSTITUTIONS ||--o{ COURSES : contains
    INSTITUTIONS ||--o{ CLASSES : contains
    COURSES ||--o{ COURSE_ENROLLMENTS : "assigned to"
    CLASSES ||--o{ COURSE_ENROLLMENTS : "receives"
    CLASSES ||--o{ STUDENT_ENROLLMENTS : "has students"
    USERS ||--o{ STUDENT_ENROLLMENTS : "enrolled in"
    COURSES ||--o{ STUDENT_ENROLLMENTS : "taken by"

    USERS {
        int id PK
        string email
        string name
        string role
        int institution_id FK
        timestamp created_at
        timestamp updated_at
    }
    
    INSTITUTIONS {
        int id PK
        string name
        string type
        timestamp created_at
        timestamp updated_at
    }
    
    COURSES {
        int id PK
        string name
        string course_code
        string description
        int teacher_id FK
        int institution_id FK
        timestamp created_at
        timestamp updated_at
    }
    
    CLASSES {
        int id PK
        string name
        string description
        int teacher_id FK
        int institution_id FK
        timestamp created_at
        timestamp updated_at
    }
    
    COURSE_ENROLLMENTS {
        int id PK
        int course_id FK
        int class_id FK
        timestamp enrolled_at
    }
    
    STUDENT_ENROLLMENTS {
        int id PK
        int student_id FK
        int course_id FK
        int class_id FK
        string status
        timestamp enrolled_at
        timestamp completed_at
    }
```

### 6.2 Data Definition Language

#### Course Enrollments Table (sudah ada)
```sql
-- Create course_enrollments table
CREATE TABLE "course_enrollments" (
	"id" serial PRIMARY KEY NOT NULL,
	"course_id" integer NOT NULL,
	"class_id" integer NOT NULL,
	"enrolled_at" timestamp DEFAULT now() NOT NULL
);

-- Add foreign key constraints
ALTER TABLE "course_enrollments" 
ADD CONSTRAINT "course_enrollments_course_id_courses_id_fk" 
FOREIGN KEY ("course_id") REFERENCES "public"."courses"("id") 
ON DELETE CASCADE ON UPDATE CASCADE;

ALTER TABLE "course_enrollments" 
ADD CONSTRAINT "course_enrollments_class_id_classes_id_fk" 
FOREIGN KEY ("class_id") REFERENCES "public"."classes"("id") 
ON DELETE CASCADE ON UPDATE CASCADE;

-- Create indexes for performance
CREATE INDEX idx_course_enrollments_course_id ON course_enrollments(course_id);
CREATE INDEX idx_course_enrollments_class_id ON course_enrollments(class_id);
CREATE INDEX idx_course_enrollments_enrolled_at ON course_enrollments(enrolled_at DESC);

-- Create unique constraint to prevent duplicates
CREATE UNIQUE INDEX idx_course_enrollments_unique 
ON course_enrollments(course_id, class_id);
```

#### Supporting Views untuk Performance
```sql
-- View untuk course assignments dengan details
CREATE VIEW course_assignments_with_details AS
SELECT 
    ce.id,
    ce.course_id,
    ce.class_id,
    ce.enrolled_at,
    c.name as course_name,
    c.course_code,
    c.description as course_description,
    c.teacher_id as course_teacher_id,
    cl.name as class_name,
    cl.description as class_description,
    cl.teacher_id as class_teacher_id,
    COUNT(se.id) as student_count
FROM course_enrollments ce
JOIN courses c ON ce.course_id = c.id
JOIN classes cl ON ce.class_id = cl.id
LEFT JOIN student_enrollments se ON se.class_id = cl.id
GROUP BY ce.id, c.id, cl.id;

-- View untuk teacher course assignment statistics
CREATE VIEW teacher_assignment_stats AS
SELECT 
    u.id as teacher_id,
    u.name as teacher_name,
    COUNT(DISTINCT ce.course_id) as assigned_courses_count,
    COUNT(DISTINCT ce.class_id) as classes_with_assignments_count,
    COUNT(ce.id) as total_assignments_count
FROM users u
JOIN courses c ON u.id = c.teacher_id
JOIN course_enrollments ce ON c.id = ce.course_id
WHERE u.role = 'teacher'
GROUP BY u.id, u.name;
```

#### Database Triggers untuk Data Integrity
```sql
-- Trigger untuk validasi institution matching
CREATE OR REPLACE FUNCTION validate_course_class_institution()
RETURNS TRIGGER AS $$
BEGIN
    -- Check if course and class belong to same institution
    IF NOT EXISTS (
        SELECT 1 FROM courses c
        JOIN classes cl ON c.institution_id = cl.institution_id
        WHERE c.id = NEW.course_id AND cl.id = NEW.class_id
    ) THEN
        RAISE EXCEPTION 'Course and class must belong to the same institution';
    END IF;
    
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

CREATE TRIGGER trigger_validate_course_class_institution
    BEFORE INSERT OR UPDATE ON course_enrollments
    FOR EACH ROW
    EXECUTE FUNCTION validate_course_class_institution();

-- Trigger untuk audit logging
CREATE TABLE course_enrollment_audit (
    id serial PRIMARY KEY,
    enrollment_id integer,
    action varchar(10),
    course_id integer,
    class_id integer,
    teacher_id integer,
    created_at timestamp DEFAULT now()
);

CREATE OR REPLACE FUNCTION audit_course_enrollment()
RETURNS TRIGGER AS $$
DECLARE
    teacher_id_val integer;
BEGIN
    -- Get teacher ID from course
    SELECT teacher_id INTO teacher_id_val 
    FROM courses 
    WHERE id = COALESCE(NEW.course_id, OLD.course_id);
    
    IF TG_OP = 'INSERT' THEN
        INSERT INTO course_enrollment_audit (enrollment_id, action, course_id, class_id, teacher_id)
        VALUES (NEW.id, 'INSERT', NEW.course_id, NEW.class_id, teacher_id_val);
        RETURN NEW;
    ELSIF TG_OP = 'DELETE' THEN
        INSERT INTO course_enrollment_audit (enrollment_id, action, course_id, class_id, teacher_id)
        VALUES (OLD.id, 'DELETE', OLD.course_id, OLD.class_id, teacher_id_val);
        RETURN OLD;
    END IF;
    
    RETURN NULL;
END;
$$ LANGUAGE plpgsql;

CREATE TRIGGER trigger_audit_course_enrollment
    AFTER INSERT OR DELETE ON course_enrollments
    FOR EACH ROW
    EXECUTE FUNCTION audit_course_enrollment();
```

## 7. Component Architecture

### 7.1 Frontend Component Structure

```
src/
├── app/
│   └── dashboard/
│       └── teacher/
│           ├── enrollments/
│           │   └── page.tsx                 # Course Assignment Management Page
│           ├── classes/
│           │   └── [id]/
│           │       └── page.tsx             # Enhanced Class Detail Page
│           └── courses/
│               └── [id]/
│                   └── page.tsx             # Enhanced Course Detail Page
├── components/
│   ├── course-assignment/
│   │   ├── assign-course-dialog.tsx         # Assignment Dialog Component
│   │   ├── course-assignment-table.tsx     # Assignment Table Component
│   │   ├── class-courses-section.tsx       # Class Detail Enhancement
│   │   ├── course-classes-section.tsx      # Course Detail Enhancement
│   │   └── assignment-stats.tsx            # Statistics Component
│   ├── ui/
│   │   ├── dialog.tsx                      # Shadcn Dialog
│   │   ├── table.tsx                       # Shadcn Table
│   │   ├── select.tsx                      # Shadcn Select
│   │   └── button.tsx                      # Shadcn Button
│   └── forms/
│       └── course-assignment-form.tsx      # Assignment Form Logic
├── hooks/
│   ├── use-course-assignments.ts           # React Query hooks
│   ├── use-assign-course.ts                # Assignment mutation hook
│   └── use-remove-assignment.ts            # Removal mutation hook
├── lib/
│   ├── api/
│   │   └── course-assignments.ts           # API client functions
│   ├── validations/
│   │   └── course-assignment.ts            # Zod validation schemas
│   └── utils.ts                            # Utility functions
└── types/
    └── course-assignment.ts                # TypeScript type definitions
```

### 7.2 State Management Architecture

```typescript
// React Query Keys
export const courseAssignmentKeys = {
  all: ['course-assignments'] as const,
  lists: () => [...courseAssignmentKeys.all, 'list'] as const,
  list: (filters: string) => [...courseAssignmentKeys.lists(), { filters }] as const,
  details: () => [...courseAssignmentKeys.all, 'detail'] as const,
  detail: (id: number) => [...courseAssignmentKeys.details(), id] as const,
};

// Zustand Store untuk UI State
interface CourseAssignmentStore {
  // Dialog state
  isAssignDialogOpen: boolean;
  selectedCourseId: string | null;
  selectedClassId: string | null;
  
  // Table state
  searchQuery: string;
  sortBy: 'course' | 'class' | 'date';
  sortOrder: 'asc' | 'desc';
  
  // Actions
  openAssignDialog: () => void;
  closeAssignDialog: () => void;
  setSelectedCourse: (courseId: string | null) => void;
  setSelectedClass: (classId: string | null) => void;
  setSearchQuery: (query: string) => void;
  setSorting: (sortBy: string, sortOrder: 'asc' | 'desc') => void;
  resetForm: () => void;
}
```

## 8. Security Implementation

### 8.1 Authentication Flow

```typescript
// API Route Protection
export async function GET(request: NextRequest) {
  try {
    // 1. Verify Supabase session
    const supabase = createRouteHandlerClient({ cookies });
    const { data: { session }, error } = await supabase.auth.getSession();
    
    if (error || !session) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }
    
    // 2. Get teacher data
    const teacher = await getTeacherByEmail(session.user.email);
    if (!teacher || teacher.role !== 'teacher') {
      return NextResponse.json({ error: 'Forbidden' }, { status: 403 });
    }
    
    // 3. Process request with teacher context
    return await processCourseAssignmentRequest(teacher, request);
    
  } catch (error) {
    return NextResponse.json({ error: 'Internal Server Error' }, { status: 500 });
  }
}
```

### 8.2 Authorization Validation

```typescript
// Course Assignment Authorization
export async function validateCourseAssignment(
  teacherId: number,
  courseId: number,
  classId: number
): Promise<ValidationResult> {
  // 1. Verify teacher owns the course
  const course = await db.query.courses.findFirst({
    where: and(
      eq(courses.id, courseId),
      eq(courses.teacherId, teacherId)
    )
  });
  
  if (!course) {
    return { valid: false, error: 'Course not found or not owned by teacher' };
  }
  
  // 2. Verify teacher manages the class
  const classData = await db.query.classes.findFirst({
    where: and(
      eq(classes.id, classId),
      eq(classes.teacherId, teacherId)
    )
  });
  
  if (!classData) {
    return { valid: false, error: 'Class not found or not managed by teacher' };
  }
  
  // 3. Verify same institution
  if (course.institutionId !== classData.institutionId) {
    return { valid: false, error: 'Course and class must be in same institution' };
  }
  
  // 4. Check for duplicate assignment
  const existingAssignment = await db.query.courseEnrollments.findFirst({
    where: and(
      eq(courseEnrollments.courseId, courseId),
      eq(courseEnrollments.classId, classId)
    )
  });
  
  if (existingAssignment) {
    return { valid: false, error: 'Course already assigned to this class' };
  }
  
  return { valid: true };
}
```

## 9. Performance Optimization

### 9.1 Database Query Optimization

```typescript
// Optimized query dengan joins dan pagination
export async function getCourseAssignments(
  teacherId: number,
  options: {
    courseId?: number;
    classId?: number;
    search?: string;
    page?: number;
    limit?: number;
  } = {}
) {
  const { courseId, classId, search, page = 1, limit = 20 } = options;
  const offset = (page - 1) * limit;
  
  let query = db
    .select({
      id: courseEnrollments.id,
      courseId: courseEnrollments.courseId,
      classId: courseEnrollments.classId,
      enrolledAt: courseEnrollments.enrolledAt,
      courseName: courses.name,
      courseCode: courses.courseCode,
      className: classes.name,
      studentCount: sql<number>`count(${studentEnrollments.id})`
    })
    .from(courseEnrollments)
    .innerJoin(courses, eq(courseEnrollments.courseId, courses.id))
    .innerJoin(classes, eq(courseEnrollments.classId, classes.id))
    .leftJoin(studentEnrollments, eq(classes.id, studentEnrollments.classId))
    .where(eq(courses.teacherId, teacherId))
    .groupBy(
      courseEnrollments.id,
      courses.id,
      classes.id
    )
    .orderBy(desc(courseEnrollments.enrolledAt))
    .limit(limit)
    .offset(offset);
  
  // Add filters
  const conditions = [eq(courses.teacherId, teacherId)];
  
  if (courseId) {
    conditions.push(eq(courseEnrollments.courseId, courseId));
  }
  
  if (classId) {
    conditions.push(eq(courseEnrollments.classId, classId));
  }
  
  if (search) {
    conditions.push(
      or(
        ilike(courses.name, `%${search}%`),
        ilike(classes.name, `%${search}%`),
        ilike(courses.courseCode, `%${search}%`)
      )
    );
  }
  
  if (conditions.length > 1) {
    query = query.where(and(...conditions));
  }
  
  return await query;
}
```

### 9.2 Frontend Performance

```typescript
// React Query dengan optimistic updates
export function useAssignCourse() {
  const queryClient = useQueryClient();
  
  return useMutation({
    mutationFn: assignCourseToClass,
    onMutate: async (variables) => {
      // Cancel outgoing refetches
      await queryClient.cancelQueries({ 
        queryKey: courseAssignmentKeys.lists() 
      });
      
      // Snapshot previous value
      const previousAssignments = queryClient.getQueryData(
        courseAssignmentKeys.list('')
      );
      
      // Optimistically update
      queryClient.setQueryData(
        courseAssignmentKeys.list(''),
        (old: any) => {
          if (!old) return old;
          return {
            ...old,
            enrollments: [
              ...old.enrollments,
              {
                id: Date.now(), // Temporary ID
                courseId: variables.courseId,
                classId: variables.classId,
                enrolledAt: new Date().toISOString(),
                // Add optimistic course and class data
              }
            ]
          };
        }
      );
      
      return { previousAssignments };
    },
    onError: (err, variables, context) => {
      // Rollback on error
      if (context?.previousAssignments) {
        queryClient.setQueryData(
          courseAssignmentKeys.list(''),
          context.previousAssignments
        );
      }
    },
    onSettled: () => {
      // Refetch to ensure consistency
      queryClient.invalidateQueries({ 
        queryKey: courseAssignmentKeys.lists() 
      });
    }
  });
}
```

## 10. Monitoring dan Logging

### 10.1 API Monitoring

```typescript
// API Route dengan logging
export async function POST(request: NextRequest) {
  const startTime = Date.now();
  const requestId = crypto.randomUUID();
  
  try {
    console.log(`[${requestId}] Course assignment request started`);
    
    const result = await processCourseAssignment(request);
    
    const duration = Date.now() - startTime;
    console.log(`[${requestId}] Course assignment completed in ${duration}ms`);
    
    return NextResponse.json(result);
    
  } catch (error) {
    const duration = Date.now() - startTime;
    console.error(`[${requestId}] Course assignment failed after ${duration}ms:`, error);
    
    return NextResponse.json(
      { error: 'Internal Server Error' },
      { status: 500 }
    );
  }
}
```

### 10.2 Performance Metrics

```typescript
// Performance tracking hook
export function usePerformanceTracking() {
  const trackEvent = useCallback((event: string, data?: any) => {
    // Track to analytics service
    if (typeof window !== 'undefined') {
      console.log(`Performance Event: ${event}`, data);
      // Send to analytics service
    }
  }, []);
  
  const trackPageLoad = useCallback((pageName: string) => {
    const loadTime = performance.now();
    trackEvent('page_load', { pageName, loadTime });
  }, [trackEvent]);
  
  const trackUserAction = useCallback((action: string, duration?: number) => {
    trackEvent('user_action', { action, duration });
  }, [trackEvent]);
  
  return { trackPageLoad, trackUserAction };
}
```

Arsitektur ini menyediakan foundation yang solid untuk implementasi course assignment dengan fokus pada performance, security, dan maintainability.