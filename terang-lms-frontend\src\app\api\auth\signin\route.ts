import { NextRequest, NextResponse } from 'next/server';
import bcrypt from 'bcryptjs';
import { query } from '@/lib/db/raw';
import { LoginCredentials, ApiResponse, AuthUser } from '@/types/database';

export async function POST(request: NextRequest) {
  try {
    const body: LoginCredentials = await request.json();
    const { email, password } = body;

    if (!email || !password) {
      return NextResponse.json(
        {
          success: false,
          error: 'Email and password are required'
        } as ApiResponse,
        { status: 400 }
      );
    }

    // Find user by email
    const result = await query`
      SELECT u.*, i.name as institution_name
      FROM users u
      LEFT JOIN institutions i ON u.institution_id = i.id
      WHERE u.email = ${email.toLowerCase()}
    `;
    const user = result[0];

    if (!user) {
      return NextResponse.json(
        { success: false, error: 'Invalid email or password' } as ApiResponse,
        { status: 401 }
      );
    }

    // Verify password
    const isValidPassword = await bcrypt.compare(password, user.password);
    if (!isValidPassword) {
      return NextResponse.json(
        { success: false, error: 'Invalid email or password' } as ApiResponse,
        { status: 401 }
      );
    }

    // Create auth user object (without password)
    const authUser: AuthUser = {
      id: user.id,
      name: user.name,
      email: user.email,
      role: user.role,
      institutionId: user.institution_id || undefined
    };

    return NextResponse.json({
      success: true,
      data: {
        user: authUser,
        institution: user
          ? { id: user.institution_id, name: user.institution_name }
          : undefined
      },
      message: 'Sign in successful'
    } as ApiResponse);
  } catch (error) {
    console.error('Sign in error:', error);
    return NextResponse.json(
      { success: false, error: 'Internal server error' } as ApiResponse,
      { status: 500 }
    );
  }
}
