'use client';

import { useState, useEffect } from 'react';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle
} from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Badge } from '@/components/ui/badge';
import { Skeleton } from '@/components/ui/skeleton';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger
} from '@/components/ui/dropdown-menu';
import {
  BookOpen,
  Plus,
  Search,
  MoreHorizontal,
  Edit,
  Trash2,
  Users,
  Bot,
  Copy
} from 'lucide-react';
import Link from 'next/link';
import { authStorage } from '@/lib/auth';
import { toast } from 'sonner';

interface Course {
  id: number;
  name: string;
  description: string;
  type: string;
  courseCode: string;
  moduleCount: number;
  studentCount: number;
  status: string;
  createdAt: string;
  startDate: string;
  endDate: string;
  coverPicture?: string;
}

export default function CoursesPage() {
  const [searchTerm, setSearchTerm] = useState('');
  const [courses, setCourses] = useState<Course[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [isDeletingCourse, setIsDeletingCourse] = useState<number | null>(null);

  useEffect(() => {
    fetchCourses();
  }, []);

  const fetchCourses = async () => {
    try {
      const user = authStorage.getUser();
      if (!user) {
        toast.error('Please log in to view courses');
        return;
      }

      const response = await fetch(`/api/courses?teacherId=${user.id}`);
      const data = await response.json();

      if (data.success) {
        setCourses(data.courses || []);
      } else {
        toast.error(data.error || 'Failed to fetch courses');
      }
    } catch (error) {
      console.error('Error fetching courses:', error);
      toast.error('Failed to fetch courses');
    } finally {
      setIsLoading(false);
    }
  };

  const handleDeleteCourse = async (courseId: number) => {
    if (!confirm('Are you sure you want to delete this course? This action cannot be undone.')) {
      return;
    }

    setIsDeletingCourse(courseId);
    try {
      const user = authStorage.getUser();
      if (!user) {
        toast.error('Please log in to delete courses');
        return;
      }

      const response = await fetch(`/api/courses/${courseId}?teacherId=${user.id}`, {
        method: 'DELETE'
      });
      const data = await response.json();

      if (data.success) {
        toast.success('Course deleted successfully!');
        fetchCourses();
      } else {
        toast.error(data.error || 'Failed to delete course');
      }
    } catch (error) {
      console.error('Error deleting course:', error);
      toast.error('Failed to delete course');
    } finally {
      setIsDeletingCourse(null);
    }
  };

  const filteredCourses = courses.filter(
    (course) =>
      course.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
      course.description.toLowerCase().includes(searchTerm.toLowerCase()) ||
      course.courseCode.toLowerCase().includes(searchTerm.toLowerCase())
  );

  const copyToClipboard = (text: string) => {
    navigator.clipboard.writeText(text);
    toast.success('Course code copied to clipboard!');
  };

  const LoadingSkeleton = () => (
    <div className='grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6'>
      {[...Array(6)].map((_, i) => (
        <Card key={i} className='overflow-hidden'>
          <div className='aspect-video bg-muted'>
            <Skeleton className='w-full h-full' />
          </div>
          <CardContent className='p-4'>
            <Skeleton className='h-6 w-3/4 mb-2' />
            <Skeleton className='h-4 w-full mb-2' />
            <Skeleton className='h-4 w-2/3 mb-4' />
            <div className='flex items-center justify-between'>
              <div className='flex space-x-4'>
                <Skeleton className='h-4 w-16' />
                <Skeleton className='h-4 w-16' />
              </div>
              <Skeleton className='h-8 w-8 rounded-full' />
            </div>
          </CardContent>
        </Card>
      ))}
    </div>
  );

  if (isLoading) {
    return (
      <div className='space-y-6'>
        <div className='flex items-center justify-between'>
          <div>
            <h1 className='text-3xl font-bold tracking-tight'>My Courses</h1>
            <p className='text-muted-foreground'>
              Create and manage your educational courses
            </p>
          </div>
          <div className='flex space-x-2'>
            <Link href='/dashboard/teacher/courses/new'>
              <Button>
                <Plus className='mr-2 h-4 w-4' />
                Create Course
              </Button>
            </Link>
          </div>
        </div>
        <LoadingSkeleton />
      </div>
    );
  }

  return (
    <div className='space-y-6'>
      <div className='flex items-center justify-between'>
        <div>
          <h1 className='text-3xl font-bold tracking-tight'>My Courses</h1>
          <p className='text-muted-foreground'>
            Create and manage your educational courses
          </p>
        </div>
        <div className='flex space-x-2'>
          <Link href='/dashboard/teacher/courses/generate'>
            <Button variant='outline'>
              <Bot className='mr-2 h-4 w-4' />
              AI Generator
            </Button>
          </Link>
          <Link href='/dashboard/teacher/courses/new'>
            <Button>
              <Plus className='mr-2 h-4 w-4' />
              Create Course
            </Button>
          </Link>
        </div>
      </div>

      <div className='space-y-6'>
        <div className='flex items-center space-x-2'>
          <div className='relative flex-1'>
            <Search className='text-muted-foreground absolute top-2.5 left-2 h-4 w-4' />
            <Input
              placeholder='Search courses...'
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className='pl-8'
            />
          </div>
        </div>

        <div className='grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6'>
          {filteredCourses.map((course) => (
            <Card key={course.id} className='overflow-hidden hover:shadow-lg transition-shadow'>
              <CardHeader className='p-0'>
                {/* Cover Image Section */}
                <div className="p-6 pb-0">
                  <div className="h-48 w-full overflow-hidden rounded-lg relative">
                    {course.coverPicture ? (
                      <img
                        src={course.coverPicture}
                        alt={course.name}
                        loading='lazy'
                        className='h-full w-full object-cover'
                      />
                    ) : (
                      <div className='h-full w-full bg-gradient-to-br from-blue-50 to-indigo-100 flex items-center justify-center'>
                        <BookOpen className='h-12 w-12 text-gray-400' />
                      </div>
                    )}
                    <div className='absolute top-2 right-2'>
                      <Badge
                        variant={
                          course.status === 'published' ? 'default' : 'outline'
                        }
                      >
                        {course.status}
                      </Badge>
                    </div>
                  </div>
                </div>
              </CardHeader>
              <CardContent className='p-6 pt-4'>
                <div className='space-y-3'>
                  <div>
                    <h3 className='font-semibold text-lg line-clamp-1'>{course.name}</h3>
                    <p className='text-muted-foreground text-sm line-clamp-2 mt-1'>
                      {course.description}
                    </p>
                  </div>
                  
                  <div className='flex items-center justify-between text-sm'>
                    <div className='flex items-center space-x-2'>
                      <code className='bg-muted rounded px-2 py-1 text-xs'>
                        {course.courseCode}
                      </code>
                      <Button
                        variant='ghost'
                        size='sm'
                        onClick={() => copyToClipboard(course.courseCode)}
                        className='h-6 w-6 p-0'
                      >
                        <Copy className='h-3 w-3' />
                      </Button>
                    </div>
                    <Badge
                      variant={
                        course.type === 'verified' ? 'default' : 'secondary'
                      }
                    >
                      {course.type}
                    </Badge>
                  </div>
                  
                  <div className='flex items-center justify-between text-sm text-muted-foreground'>
                    <div className='flex items-center space-x-1'>
                      <BookOpen className='h-4 w-4' />
                      <span>{course.moduleCount} modules</span>
                    </div>
                    <div className='flex items-center space-x-1'>
                      <Users className='h-4 w-4' />
                      <span>{course.studentCount} students</span>
                    </div>
                  </div>
                  
                  <div className='flex items-center justify-between pt-2'>
                    <div className='flex space-x-1'>
                      <Link href={`/dashboard/teacher/courses/${course.id}`}>
                        <Button variant='outline' size='sm'>
                          <Edit className='h-3 w-3 mr-1' />
                          Edit
                        </Button>
                      </Link>
                    </div>
                    <DropdownMenu>
                      <DropdownMenuTrigger asChild>
                        <Button variant='ghost' size='sm' className='h-8 w-8 p-0'>
                          <MoreHorizontal className='h-4 w-4' />
                        </Button>
                      </DropdownMenuTrigger>
                      <DropdownMenuContent align='end'>
                        <DropdownMenuItem asChild>
                          <Link
                            href={`/dashboard/teacher/courses/${course.id}/students`}
                          >
                            <Users className='mr-2 h-4 w-4' />
                            View Students
                          </Link>
                        </DropdownMenuItem>
                        <DropdownMenuItem 
                          className='text-red-600'
                          onClick={() => handleDeleteCourse(course.id)}
                          disabled={isDeletingCourse === course.id}
                        >
                          {isDeletingCourse === course.id ? (
                            <div className='mr-2 h-4 w-4 animate-spin rounded-full border-2 border-red-600 border-t-transparent' />
                          ) : (
                            <Trash2 className='mr-2 h-4 w-4' />
                          )}
                          {isDeletingCourse === course.id ? 'Deleting...' : 'Delete'}
                        </DropdownMenuItem>
                      </DropdownMenuContent>
                    </DropdownMenu>
                  </div>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>

        {filteredCourses.length === 0 && (
          <div className='py-16 text-center'>
            <BookOpen className='text-muted-foreground mx-auto h-16 w-16' />
            <h3 className='mt-4 text-lg font-semibold'>No courses found</h3>
            <p className='text-muted-foreground mt-2 text-sm max-w-sm mx-auto'>
              {searchTerm
                ? 'Try adjusting your search terms to find the courses you\'re looking for.'
                : 'Get started by creating your first course using our intuitive wizard or AI generator.'}
            </p>
            {!searchTerm && (
              <div className='mt-8 flex justify-center space-x-3'>
                <Link href='/dashboard/teacher/courses/generate'>
                  <Button variant='outline' size='lg'>
                    <Bot className='mr-2 h-4 w-4' />
                    AI Generator
                  </Button>
                </Link>
                <Link href='/dashboard/teacher/courses/new'>
                  <Button size='lg'>
                    <Plus className='mr-2 h-4 w-4' />
                    Create Course
                  </Button>
                </Link>
              </div>
            )}
          </div>
        )}
      </div>
    </div>
  );
}
