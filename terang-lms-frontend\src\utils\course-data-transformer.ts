import { CourseData, ModuleData, ChapterData, QuizData, QuestionData } from '@/components/course/course-creation-wizard';

// Type definitions for API response data
export interface ApiCourse {
  id: number;
  name: string;
  description: string;
  instructor?: string;
  courseCode: string;
  type: 'self_paced' | 'verified';
  enrollmentType?: 'code' | 'invitation' | 'both' | 'purchase';
  startDate?: string;
  endDate?: string;
  coverImage?: string;
  coverPicture?: string;
  isPurchasable?: boolean;
  price?: number;
  currency?: string;
  previewMode?: boolean;
  teacherId: number;
  createdAt: string;
  updatedAt: string;
  modules: ApiModule[];
  moduleQuizzes?: ApiQuiz[];
  // Related course data
  admissions?: {
    requirements: string[];
    applicationDeadline?: string;
    prerequisites: string[];
  };
  academics?: {
    credits: number;
    workload: string;
    assessment: string[];
  };
  tuitionAndFinancing?: {
    totalCost: number;
    paymentOptions: string[];
    scholarships: string[];
  };
  careers?: {
    outcomes: string[];
    industries: string[];
    averageSalary?: string;
  };
  studentExperience?: {
    testimonials: { name: string; feedback: string }[];
    facilities: string[];
    support: string[];
  };
}

export interface ApiModule {
  id: number;
  name: string;
  description: string;
  orderIndex: number;
  courseId: number;
  createdAt: string;
  updatedAt: string;
  chapters: ApiChapter[];
}

export interface ApiChapter {
  id: number;
  name: string;
  content: string;
  orderIndex: number;
  moduleId: number;
  createdAt: string;
  updatedAt: string;
  quizzes?: ApiQuiz[];
}

export interface ApiQuiz {
  id: number;
  name: string;
  description: string;
  quizType: 'chapter' | 'module' | 'final';
  timeLimit?: number;
  minimumScore: string;
  isActive: boolean;
  chapterId?: number;
  moduleId?: number;
  courseId?: number;
  teacherId: number;
  createdAt: string;
  updatedAt: string;
  questions?: ApiQuestion[];
}

export interface ApiQuestion {
  id: number;
  type: 'multiple_choice' | 'true_false' | 'essay';
  question: any[]; // JSON content
  options?: { content: any[]; isCorrect: boolean }[] | null; // JSON content
  essayAnswer?: string | null; // Renamed from correctAnswer
  explanation?: any[] | null; // New column
  points: number;
  orderIndex: number;
  quizId: number;
  createdAt: string;
  updatedAt: string;
}

/**
 * Transform API question data to CourseData question format
 */
function transformQuestion(apiQuestion: ApiQuestion): QuestionData {
  return {
    id: apiQuestion.id.toString(),
    type: apiQuestion.type,
    question: apiQuestion.question || [{ type: 'text', value: '' }],
    options: apiQuestion.options || undefined,
    essayAnswer: apiQuestion.essayAnswer,
    explanation: apiQuestion.explanation || undefined,
    points: apiQuestion.points,
    orderIndex: apiQuestion.orderIndex
  };
}

/**
 * Transform API quiz data to CourseData quiz format
 */
function transformQuiz(apiQuiz: ApiQuiz): QuizData {
  return {
    id: apiQuiz.id.toString(),
    name: apiQuiz.name,
    description: apiQuiz.description,
    questions: apiQuiz.questions ? apiQuiz.questions.map(transformQuestion) : [],
    timeLimit: apiQuiz.timeLimit,
    minimumScore: parseInt(apiQuiz.minimumScore)
  };
}

/**
 * Transform API chapter data to CourseData chapter format
 */
function transformChapter(apiChapter: ApiChapter): ChapterData {
  const chapterQuiz = apiChapter.quizzes?.find(quiz => quiz.quizType === 'chapter');

  // Handle content - it might be a JSON string or already parsed object
  let content = [];
  if (apiChapter.content) {
    if (typeof apiChapter.content === 'string') {
      try {
        content = JSON.parse(apiChapter.content);
      } catch (error) {
        console.error('Error parsing chapter content:', error);
        content = [];
      }
    } else {
      // Content is already an object/array
      content = apiChapter.content;
    }
  }

  return {
    id: apiChapter.id.toString(),
    name: apiChapter.name,
    content: content,
    orderIndex: apiChapter.orderIndex,
    hasChapterQuiz: !!chapterQuiz,
    chapterQuiz: chapterQuiz ? transformQuiz(chapterQuiz) : undefined
  };
}

/**
 * Transform API module data to CourseData module format
 * FIXED: Now properly handles module quizzes from the moduleQuizzes array
 */
function transformModule(apiModule: ApiModule, allQuizzes: ApiQuiz[] = []): ModuleData {
  // Find module quiz specifically for this module
  const moduleQuiz = allQuizzes.find(quiz => 
    quiz.moduleId === apiModule.id && quiz.quizType === 'module'
  );
  
  console.log(`Module ${apiModule.id}: Found module quiz:`, moduleQuiz); // Debug log
  
  return {
    id: apiModule.id.toString(),
    name: apiModule.name,
    description: apiModule.description,
    orderIndex: apiModule.orderIndex,
    chapters: apiModule.chapters.map(transformChapter),
    hasModuleQuiz: !!moduleQuiz,
    moduleQuiz: moduleQuiz ? transformQuiz(moduleQuiz) : undefined
  };
}

/**
 * Transform API course data to CourseData format for the wizard
 * FIXED: Better handling of quizzes and debugging
 */
export function transformApiCourseToWizardData(apiCourse: ApiCourse, moduleQuizzes: ApiQuiz[] = [], finalExam?: ApiQuiz): CourseData {
  console.log('Transforming API course data:', {
    courseId: apiCourse.id,
    moduleQuizzesCount: moduleQuizzes.length,
    finalExam: finalExam ? finalExam.id : 'none',
    allQuizzes: moduleQuizzes.map(q => ({ id: q.id, type: q.quizType, moduleId: q.moduleId, courseId: q.courseId }))
  });

  // Filter quizzes properly
  const actualModuleQuizzes = moduleQuizzes.filter(quiz => quiz.quizType === 'module');
  const actualFinalExam = finalExam || moduleQuizzes.find(quiz => quiz.quizType === 'final');

  console.log('Filtered quizzes:', {
    moduleQuizzes: actualModuleQuizzes.length,
    finalExam: actualFinalExam ? actualFinalExam.id : 'none'
  });

  return {
    name: apiCourse.name,
    description: apiCourse.description,
    instructor: apiCourse.instructor || '', 
    courseCode: apiCourse.courseCode,
    type: apiCourse.type,
    enrollmentType: apiCourse.enrollmentType || 'code', 
    startDate: apiCourse.startDate ? new Date(apiCourse.startDate) : undefined,
    endDate: apiCourse.endDate ? new Date(apiCourse.endDate) : undefined,
    coverImagePreview: apiCourse.coverImage || apiCourse.coverPicture,
    isPurchasable: apiCourse.isPurchasable,
    price: apiCourse.price,
    currency: apiCourse.currency,
    previewMode: apiCourse.previewMode,
    modules: apiCourse.modules.map(module => transformModule(module, moduleQuizzes)), // Pass ALL quizzes
    isPublished: true, 
    assignedClasses: [], 
    finalExam: actualFinalExam ? transformQuiz(actualFinalExam) : undefined,
    // Add related course data
    admissions: apiCourse.admissions ? {
      requirements: apiCourse.admissions.requirements || [],
      applicationDeadline: apiCourse.admissions.applicationDeadline || '',
      prerequisites: apiCourse.admissions.prerequisites || []
    } : undefined,
    academics: apiCourse.academics ? {
      credits: apiCourse.academics.credits || 0,
      workload: apiCourse.academics.workload || '',
      assessment: apiCourse.academics.assessment || []
    } : undefined,
    tuitionAndFinancing: apiCourse.tuitionAndFinancing ? {
      totalCost: apiCourse.tuitionAndFinancing.totalCost || 0,
      paymentOptions: apiCourse.tuitionAndFinancing.paymentOptions || [],
      scholarships: apiCourse.tuitionAndFinancing.scholarships || []
    } : undefined,
    careers: apiCourse.careers ? {
      outcomes: apiCourse.careers.outcomes || [],
      industries: apiCourse.careers.industries || [],
      averageSalary: apiCourse.careers.averageSalary || ''
    } : undefined,
    studentExperience: apiCourse.studentExperience ? {
      testimonials: apiCourse.studentExperience.testimonials || [],
      facilities: apiCourse.studentExperience.facilities || [],
      support: apiCourse.studentExperience.support || []
    } : undefined
  };
}

/**
 * Transform CourseData back to API format for updates
 * FIXED: Better handling of quiz transformations and ID parsing
 */
export function transformWizardDataToApiUpdate(courseData: CourseData, courseId: string) {
  console.log('Transforming wizard data to API update:', {
    courseId,
    modulesCount: courseData.modules.length,
    finalExamExists: !!courseData.finalExam,
    moduleQuizzes: courseData.modules.filter(m => m.hasModuleQuiz).length
  });

  return {
    courseId: parseInt(courseId),
    name: courseData.name,
    description: courseData.description,
    courseCode: courseData.courseCode,
    type: courseData.type,
    enrollmentType: courseData.enrollmentType,
    price: courseData.price,
    currency: courseData.currency,
    isPurchasable: courseData.enrollmentType === 'purchase' || courseData.enrollmentType === 'both',
    startDate: courseData.startDate?.toISOString(),
    endDate: courseData.endDate?.toISOString(),
    modules: courseData.modules.map(module => {
      console.log(`Transforming module ${module.name}:`, {
        id: module.id,
        hasModuleQuiz: module.hasModuleQuiz,
        moduleQuizId: module.moduleQuiz?.id
      });

      return {
        id: module.id && !isNaN(parseInt(module.id)) ? parseInt(module.id) : undefined,
        name: module.name,
        description: module.description,
        orderIndex: module.orderIndex,
        chapters: module.chapters.map(chapter => ({
          id: chapter.id && !isNaN(parseInt(chapter.id)) ? parseInt(chapter.id) : undefined,
          name: chapter.name,
          content: chapter.content, 
          orderIndex: chapter.orderIndex,
          quiz: chapter.hasChapterQuiz && chapter.chapterQuiz ? {
            id: chapter.chapterQuiz.id && !isNaN(parseInt(chapter.chapterQuiz.id)) ? parseInt(chapter.chapterQuiz.id) : undefined,
            name: chapter.chapterQuiz.name,
            description: chapter.chapterQuiz.description,
            quizType: 'chapter' as const,
            timeLimit: chapter.chapterQuiz.timeLimit,
            minimumScore: chapter.chapterQuiz.minimumScore,
            questions: chapter.chapterQuiz.questions.map(question => ({
              id: question.id && !isNaN(parseInt(question.id)) ? parseInt(question.id) : undefined,
              type: question.type,
              question: question.question, 
              options: question.options || null,
              essayAnswer: question.type === 'essay' ? question.essayAnswer : null,
              explanation: question.explanation || null,
              points: question.points,
              orderIndex: question.orderIndex
            }))
          } : undefined
        })),
        quiz: module.hasModuleQuiz && module.moduleQuiz ? {
          id: module.moduleQuiz.id && !isNaN(parseInt(module.moduleQuiz.id)) ? parseInt(module.moduleQuiz.id) : undefined,
          name: module.moduleQuiz.name,
          description: module.moduleQuiz.description,
          quizType: 'module' as const,
          timeLimit: module.moduleQuiz.timeLimit,
          minimumScore: module.moduleQuiz.minimumScore,
          questions: module.moduleQuiz.questions.map(question => ({
            id: question.id && !isNaN(parseInt(question.id)) ? parseInt(question.id) : undefined,
            type: question.type,
            question: question.question, 
            options: question.options || null,
            essayAnswer: question.type === 'essay' ? question.essayAnswer : null,
            explanation: question.explanation || null,
            points: question.points,
            orderIndex: question.orderIndex
          }))
        } : undefined
      };
    }),
    finalExam: courseData.finalExam ? {
      id: courseData.finalExam.id && !isNaN(parseInt(courseData.finalExam.id)) ? parseInt(courseData.finalExam.id) : undefined,
      name: courseData.finalExam.name,
      description: courseData.finalExam.description,
      quizType: 'final' as const,
      timeLimit: courseData.finalExam.timeLimit,
      minimumScore: courseData.finalExam.minimumScore,
      questions: courseData.finalExam.questions.map(question => ({
        id: question.id && !isNaN(parseInt(question.id)) ? parseInt(question.id) : undefined,
        type: question.type,
        question: question.question, 
        options: question.options || null,
        essayAnswer: question.type === 'essay' ? question.essayAnswer : null,
        explanation: question.explanation || null,
        points: question.points,
        orderIndex: question.orderIndex
      }))
    } : undefined,
    // Add related course data
    admissions: courseData.admissions,
    academics: courseData.academics,
    tuitionAndFinancing: courseData.tuitionAndFinancing,
    careers: courseData.careers,
    studentExperience: courseData.studentExperience
  };
}