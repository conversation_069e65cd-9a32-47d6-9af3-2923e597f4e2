'use client';

import { useState } from 'react';
import { useRouter } from 'next/navigation';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle
} from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue
} from '@/components/ui/select';
import { ArrowLeft, Save } from 'lucide-react';
import Link from 'next/link';
import { institutionTypes, subscriptionPlans } from '@/config/subscriptions';
import { useToast } from '@/hooks/use-toast';

export default function NewInstitutionPage() {
  const router = useRouter();
  const { toast } = useToast();
  const [isLoading, setIsLoading] = useState(false);
  const [formData, setFormData] = useState({
    name: '',
    type: '',
    subscriptionPlan: 'basic',
    billingCycle: 'monthly',
    studentCount: 0,
    teacherCount: 0,
    paymentStatus: 'unpaid'
  });

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsLoading(true);

    try {
      const response = await fetch('/api/institutions', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(formData)
      });

      const data = await response.json();

      if (data.success) {
        toast({
          title: 'Success',
          description: 'Institution created successfully'
        });
        router.push('/dashboard/admin/institutions');
      } else {
        toast({
          title: 'Error',
          description: data.error || 'Failed to create institution',
          variant: 'destructive'
        });
      }
    } catch (error) {
      console.error('Error creating institution:', error);
      toast({
        title: 'Error',
        description: 'Failed to create institution',
        variant: 'destructive'
      });
    } finally {
      setIsLoading(false);
    }
  };

  const handleInputChange = (field: string, value: any) => {
    setFormData((prev) => ({
      ...prev,
      [field]: value
    }));
  };

  return (
    <div className='space-y-6'>
      <div className='flex items-center space-x-4'>
        <Link href='/dashboard/admin/institutions'>
          <Button variant='outline' size='sm'>
            <ArrowLeft className='mr-2 h-4 w-4' />
            Back
          </Button>
        </Link>
        <div>
          <h1 className='text-3xl font-bold tracking-tight'>
            Add New Institution
          </h1>
          <p className='text-muted-foreground'>
            Create a new educational institution on the platform
          </p>
        </div>
      </div>

      <Card>
        <CardHeader>
          <CardTitle>Institution Details</CardTitle>
          <CardDescription>
            Enter the basic information for the new institution
          </CardDescription>
        </CardHeader>
        <CardContent>
          <form onSubmit={handleSubmit} className='space-y-6'>
            <div className='grid grid-cols-1 gap-6 md:grid-cols-2'>
              <div className='space-y-2'>
                <Label htmlFor='name'>Institution Name</Label>
                <Input
                  id='name'
                  value={formData.name}
                  onChange={(e) => handleInputChange('name', e.target.value)}
                  placeholder='Enter institution name'
                  required
                />
              </div>

              <div className='space-y-2'>
                <Label htmlFor='type'>Institution Type</Label>
                <Select
                  value={formData.type}
                  onValueChange={(value) => handleInputChange('type', value)}
                  required
                >
                  <SelectTrigger>
                    <SelectValue placeholder='Select institution type' />
                  </SelectTrigger>
                  <SelectContent>
                    {institutionTypes.map((type) => (
                      <SelectItem key={type.value} value={type.value}>
                        {type.label}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>

              <div className='space-y-2'>
                <Label htmlFor='subscriptionPlan'>Subscription Plan</Label>
                <Select
                  value={formData.subscriptionPlan}
                  onValueChange={(value) =>
                    handleInputChange('subscriptionPlan', value)
                  }
                >
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    {Object.entries(subscriptionPlans).map(([key, plan]) => (
                      <SelectItem key={key} value={key}>
                        {plan.name} - Rp{' '}
                        {plan.pricePerStudent.monthly.toLocaleString()}
                        /student/month
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>

              <div className='space-y-2'>
                <Label htmlFor='billingCycle'>Billing Cycle</Label>
                <Select
                  value={formData.billingCycle}
                  onValueChange={(value) =>
                    handleInputChange('billingCycle', value)
                  }
                >
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value='monthly'>Monthly</SelectItem>
                    <SelectItem value='yearly'>
                      Yearly (25% discount)
                    </SelectItem>
                  </SelectContent>
                </Select>
              </div>

              <div className='space-y-2'>
                <Label htmlFor='studentCount'>Number of Students</Label>
                <Input
                  id='studentCount'
                  type='number'
                  value={formData.studentCount}
                  onChange={(e) =>
                    handleInputChange(
                      'studentCount',
                      parseInt(e.target.value) || 0
                    )
                  }
                  placeholder='0'
                  min='0'
                />
              </div>

              <div className='space-y-2'>
                <Label htmlFor='teacherCount'>Number of Teachers</Label>
                <Input
                  id='teacherCount'
                  type='number'
                  value={formData.teacherCount}
                  onChange={(e) =>
                    handleInputChange(
                      'teacherCount',
                      parseInt(e.target.value) || 0
                    )
                  }
                  placeholder='0'
                  min='0'
                />
              </div>

              <div className='space-y-2'>
                <Label htmlFor='paymentStatus'>Payment Status</Label>
                <Select
                  value={formData.paymentStatus}
                  onValueChange={(value) =>
                    handleInputChange('paymentStatus', value)
                  }
                >
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value='paid'>Paid</SelectItem>
                    <SelectItem value='unpaid'>Unpaid</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </div>

            {/* Subscription Plan Preview */}
            {formData.subscriptionPlan && (
              <Card>
                <CardHeader>
                  <CardTitle className='text-lg'>
                    {
                      subscriptionPlans[
                        formData.subscriptionPlan as keyof typeof subscriptionPlans
                      ].name
                    }{' '}
                    Plan Preview
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <div className='grid grid-cols-1 gap-4 md:grid-cols-2'>
                    <div>
                      <h4 className='mb-2 font-semibold'>Features:</h4>
                      <ul className='space-y-1 text-sm'>
                        {subscriptionPlans[
                          formData.subscriptionPlan as keyof typeof subscriptionPlans
                        ].features
                          .slice(0, 5)
                          .map((feature, index) => (
                            <li key={index} className='flex items-center'>
                              <span className='mr-2 h-2 w-2 rounded-full bg-green-500'></span>
                              {feature}
                            </li>
                          ))}
                      </ul>
                    </div>
                    <div>
                      <h4 className='mb-2 font-semibold'>Pricing:</h4>
                      <div className='space-y-1 text-sm'>
                        <p>
                          Monthly: Rp{' '}
                          {subscriptionPlans[
                            formData.subscriptionPlan as keyof typeof subscriptionPlans
                          ].pricePerStudent.monthly.toLocaleString()}
                          /student
                        </p>
                        <p>
                          Yearly: Rp{' '}
                          {subscriptionPlans[
                            formData.subscriptionPlan as keyof typeof subscriptionPlans
                          ].pricePerStudent.yearly.toLocaleString()}
                          /student
                        </p>
                        <p className='text-muted-foreground'>
                          Student Range:{' '}
                          {
                            subscriptionPlans[
                              formData.subscriptionPlan as keyof typeof subscriptionPlans
                            ].minStudents
                          }{' '}
                          -{' '}
                          {
                            subscriptionPlans[
                              formData.subscriptionPlan as keyof typeof subscriptionPlans
                            ].maxStudents
                          }
                        </p>
                      </div>
                    </div>
                  </div>
                </CardContent>
              </Card>
            )}

            <div className='flex justify-end space-x-4'>
              <Link href='/dashboard/admin/institutions'>
                <Button variant='outline' type='button'>
                  Cancel
                </Button>
              </Link>
              <Button type='submit' disabled={isLoading}>
                <Save className='mr-2 h-4 w-4' />
                {isLoading ? 'Creating...' : 'Create Institution'}
              </Button>
            </div>
          </form>
        </CardContent>
      </Card>
    </div>
  );
}
