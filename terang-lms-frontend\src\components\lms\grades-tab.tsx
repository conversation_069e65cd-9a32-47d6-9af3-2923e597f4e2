'use client';

import React from 'react';
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON><PERSON>er, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';
import {
  GameIcon as GradeIcon,
  CheckmarkCircle01Icon as CheckCircleIcon,
  Cancel01Icon as XCircleIcon,
  Clock01Icon as ClockIcon
} from 'hugeicons-react';
import { Course } from '@/types/lms';

interface GradesTabProps {
  courseData: Course;
}

const GradesTab: React.FC<GradesTabProps> = ({ courseData }) => {
  const getGradeColor = (score: number, minimumScore: number) => {
    if (score >= minimumScore) {
      return 'text-green-600';
    } else if (score >= minimumScore * 0.7) {
      return 'text-yellow-600';
    } else {
      return 'text-red-600';
    }
  };

  const getGradeBadgeVariant = (score: number, minimumScore: number) => {
    if (score >= minimumScore) {
      return 'default';
    } else if (score >= minimumScore * 0.7) {
      return 'secondary';
    } else {
      return 'destructive';
    }
  };

  const getStatusIcon = (isPassed: boolean, attempts: number, maxAttempts: number) => {
    if (isPassed) {
      return <CheckCircleIcon className="h-4 w-4 text-green-600" />;
    } else if (attempts >= maxAttempts) {
      return <XCircleIcon className="h-4 w-4 text-red-600" />;
    } else {
      return <ClockIcon className="h-4 w-4 text-yellow-600" />;
    }
  };

  const getStatusText = (isPassed: boolean, attempts: number, maxAttempts: number) => {
    if (isPassed) {
      return 'Lulus';
    } else if (attempts >= maxAttempts) {
      return 'Tidak Lulus';
    } else {
      return 'Belum Selesai';
    }
  };

  return (
    <div className="space-y-6">
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <GradeIcon className="h-5 w-5" />
            Nilai Kursus
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-6">
          {/* Overall Progress */}
          <div>
            <h3 className="text-lg font-semibold mb-4">Kemajuan Keseluruhan</h3>
            <div className="space-y-4">
              <div className="flex items-center justify-between">
                <span className="text-sm font-medium">Progress Keseluruhan</span>
                <span className="text-sm text-gray-500">
                  {Math.round(courseData.totalProgress)}%
                </span>
              </div>
              <Progress value={courseData.totalProgress} className="h-3" />
            </div>
          </div>

          {/* Chapter Quizzes */}
          <div>
            <h3 className="text-lg font-semibold mb-4">Kuis Bab</h3>
            <div className="space-y-3">
              {courseData.modules.map((module) =>
                module.chapters.map((chapter) => (
                  <Card key={chapter.id} className="p-4">
                    <div className="flex items-center justify-between">
                      <div className="flex-1">
                        <div className="flex items-center gap-2 mb-1">
                          {getStatusIcon(
                            chapter.quiz.isPassed,
                            chapter.quiz.attempts,
                            chapter.quiz.maxAttempts
                          )}
                          <span className="font-medium">{chapter.title}</span>
                          <Badge variant="outline" className="text-xs">
                            {module.title}
                          </Badge>
                        </div>
                        <div className="text-sm text-gray-600">
                          Percobaan: {chapter.quiz.attempts}/{chapter.quiz.maxAttempts}
                        </div>
                      </div>
                      <div className="text-right">
                        <div className={`text-lg font-bold ${getGradeColor(
                          chapter.quiz.lastScore || 0,
                          chapter.quiz.minimumScore
                        )}`}>
                          {chapter.quiz.lastScore || 0}%
                        </div>
                        <Badge
                          variant={getGradeBadgeVariant(
                            chapter.quiz.lastScore || 0,
                            chapter.quiz.minimumScore
                          )}
                          className="text-xs"
                        >
                          {getStatusText(
                            chapter.quiz.isPassed,
                            chapter.quiz.attempts,
                            chapter.quiz.maxAttempts
                          )}
                        </Badge>
                      </div>
                    </div>
                  </Card>
                ))
              )}
            </div>
          </div>

          {/* Module Quizzes */}
          <div>
            <h3 className="text-lg font-semibold mb-4">Kuis Modul</h3>
            <div className="space-y-3">
              {courseData.modules.map((module) => (
                <Card key={module.id} className="p-4">
                  <div className="flex items-center justify-between">
                    <div className="flex-1">
                      <div className="flex items-center gap-2 mb-1">
                        {getStatusIcon(
                          module.moduleQuiz.isPassed,
                          module.moduleQuiz.attempts,
                          module.moduleQuiz.maxAttempts
                        )}
                        <span className="font-medium">{module.title}</span>
                      </div>
                      <div className="text-sm text-gray-600">
                        Percobaan: {module.moduleQuiz.attempts}/{module.moduleQuiz.maxAttempts}
                      </div>
                    </div>
                    <div className="text-right">
                      <div className={`text-lg font-bold ${getGradeColor(
                        module.moduleQuiz.lastScore || 0,
                        module.moduleQuiz.minimumScore
                      )}`}>
                        {module.moduleQuiz.lastScore || 0}%
                      </div>
                      <Badge
                        variant={getGradeBadgeVariant(
                          module.moduleQuiz.lastScore || 0,
                          module.moduleQuiz.minimumScore
                        )}
                        className="text-xs"
                      >
                        {getStatusText(
                          module.moduleQuiz.isPassed,
                          module.moduleQuiz.attempts,
                          module.moduleQuiz.maxAttempts
                        )}
                      </Badge>
                    </div>
                  </div>
                </Card>
              ))}
            </div>
          </div>

          {/* Final Exam */}
          <div>
            <h3 className="text-lg font-semibold mb-4">Ujian Akhir</h3>
            <Card className="p-4">
              <div className="flex items-center justify-between">
                <div className="flex-1">
                  <div className="flex items-center gap-2 mb-1">
                    {getStatusIcon(
                      courseData.finalExam.isPassed,
                      courseData.finalExam.attempts,
                      courseData.finalExam.maxAttempts
                    )}
                    <span className="font-medium">Ujian Akhir</span>
                  </div>
                  <div className="text-sm text-gray-600">
                    Percobaan: {courseData.finalExam.attempts}/{courseData.finalExam.maxAttempts}
                  </div>
                </div>
                <div className="text-right">
                  <div className={`text-lg font-bold ${getGradeColor(
                    courseData.finalExam.lastScore || 0,
                    courseData.finalExam.minimumScore
                  )}`}>
                    {courseData.finalExam.lastScore || 0}%
                  </div>
                  <Badge
                    variant={getGradeBadgeVariant(
                      courseData.finalExam.lastScore || 0,
                      courseData.finalExam.minimumScore
                    )}
                    className="text-xs"
                  >
                    {getStatusText(
                      courseData.finalExam.isPassed,
                      courseData.finalExam.attempts,
                      courseData.finalExam.maxAttempts
                    )}
                  </Badge>
                </div>
              </div>
            </Card>
          </div>

          {/* Certificate Status */}
          <div>
            <h3 className="text-lg font-semibold mb-4">Status Sertifikat</h3>
            <Card className="p-4">
              <div className="flex items-center justify-between">
                <div className="flex items-center gap-2">
                  {courseData.certificate.isEligible ? (
                    <CheckCircleIcon className="h-5 w-5 text-green-600" />
                  ) : (
                    <XCircleIcon className="h-5 w-5 text-gray-400" />
                  )}
                  <span className="font-medium">Sertifikat</span>
                </div>
                <Badge
                  variant={courseData.certificate.isEligible ? 'default' : 'secondary'}
                >
                  {courseData.certificate.isEligible ? 'Eligible' : 'Not Eligible'}
                </Badge>
              </div>
            </Card>
          </div>
        </CardContent>
      </Card>
    </div>
  );
};

export { GradesTab };
