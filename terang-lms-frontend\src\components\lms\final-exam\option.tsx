import React from 'react';
import { ContentBlock } from '@/components/dynamic-content-editor';

interface OptionProps {
  option: string | { content: ContentBlock[]; isCorrect: boolean };
  index: number;
  questionId: string;
  selectedAnswer: number | string | number[] | undefined;
  onAnswerChange: (questionId: string, answer: number | string | number[]) => void;
  type: 'radio' | 'checkbox';
  disabled?: boolean;
  showResults?: boolean;
  correctAnswer?: number | string | number[];
  isCorrect?: boolean;
}

export const Option: React.FC<OptionProps> = ({
  option,
  index,
  questionId,
  selectedAnswer,
  onAnswerChange,
  type = 'radio',
  disabled = false,
  showResults = false,
  correctAnswer,
  isCorrect
}) => {
  const isSelected = type === 'radio' 
    ? selectedAnswer === index 
    : Array.isArray(selectedAnswer) && selectedAnswer.includes(index);

  const isCorrectOption = type === 'radio' 
    ? correctAnswer === index 
    : Array.isArray(correctAnswer) && correctAnswer.includes(index);

  const handleChange = () => {
    if (disabled) return;
    
    if (type === 'radio') {
      onAnswerChange(questionId, index);
    } else {
      // Handle checkbox logic for multiple selection
      const currentAnswers = Array.isArray(selectedAnswer) ? selectedAnswer : [];
      const newAnswers = isSelected 
        ? currentAnswers.filter((ans) => ans !== index)
        : [...currentAnswers, index];
      onAnswerChange(questionId, newAnswers);
    }
  };

  const getOptionStyles = () => {
    if (!showResults) {
      // Normal mode - just show selected state
      return isSelected 
        ? 'border-blue-500 bg-blue-50' 
        : 'border-gray-200 hover:border-gray-300 hover:bg-gray-50';
    }

    // Results mode - show correct/incorrect highlighting
    if (isCorrectOption) {
      // This is the correct answer - always highlight green
      return 'border-green-500 bg-green-50';
    } else if (isSelected) {
      // User selected this wrong answer - highlight red
      return 'border-red-500 bg-red-50';
    } else {
      // Not selected, not correct - neutral
      return 'border-gray-200 bg-gray-50';
    }
  };

  return (
    <label 
      className={`
        flex cursor-pointer items-start space-x-3 p-3 rounded-lg border-2 transition-all
        ${getOptionStyles()}
        ${disabled ? 'opacity-50 cursor-not-allowed' : ''}
        ${showResults && isCorrectOption ? 'ring-2 ring-green-200' : ''}
        ${showResults && isSelected && !isCorrectOption ? 'ring-2 ring-red-200' : ''}
      `}
    >
      <input
        type={type}
        name={questionId}
        value={index}
        checked={isSelected}
        onChange={handleChange}
        disabled={disabled}
        className={`
          mt-0.5 h-4 w-4 shrink-0
          ${type === 'radio' ? 'text-blue-600' : 'text-blue-600 rounded'}
          ${disabled ? 'cursor-not-allowed' : 'cursor-pointer'}
        `}
      />
      <span className={`text-sm leading-relaxed flex-1 ${disabled ? 'text-gray-500' : 'text-gray-900'}`}>
        <span className="font-medium mr-2">
          {String.fromCharCode(65 + index)}.
        </span>
        {typeof option === 'string' ? (
          option
        ) : (
          option.content.map((block, blockIndex) => (
            <React.Fragment key={blockIndex}>
              {block.type === 'text' && <span>{block.value}</span>}
              {block.type === 'image' && block.value && (
                <img src={block.value} alt={`Option image ${blockIndex}`} className="inline-block max-h-8 object-contain ml-1" />
              )}
              {block.type === 'video' && <span>[Video: {block.value}]</span>}
              {block.type === 'pdf' && <span>[PDF: {block.value}]</span>}
              {block.type === 'zoom-recording' && <span>[Recording: {block.value}]</span>}
            </React.Fragment>
          ))
        )}
      </span>
      
      {/* Show correct/incorrect indicators in results mode */}
      {showResults && (
        <div className="flex items-center ml-2">
          {isCorrectOption && (
            <span className="text-green-600 font-medium text-xs bg-green-100 px-2 py-1 rounded-full">
              ✓ Benar
            </span>
          )}
          {isSelected && !isCorrectOption && (
            <span className="text-red-600 font-medium text-xs bg-red-100 px-2 py-1 rounded-full">
              ✗ Salah
            </span>
          )}
        </div>
      )}
    </label>
  );
};