import express from 'express';
import puppeteer from 'puppeteer';
import fs from 'fs/promises';
import path from 'path';

const app = express();
const PORT = process.env.PORT || 3000;

app.use(express.json({ limit: '10mb' })); // Increase payload limit for HTML content

let browser = null;

async function initBrowser() {
  if (!browser) {
    browser = await puppeteer.launch({
      headless: 'new',
      args: ['--no-sandbox', '--disable-setuid-sandbox']
    });
  }
  return browser;
}

app.post('/screenshot', async (req, res) => {
  try {
    const { url, filename, width = 1080, height = 1024, fullPage = false } = req.body;
    
    if (!url) {
      return res.status(400).json({ error: 'URL is required' });
    }

    await initBrowser();
    const page = await browser.newPage();
    
    await page.setViewport({ width, height });
    await page.goto(url, { waitUntil: 'networkidle2' });
    
    const screenshotPath = `screenshots/${filename || `screenshot-${Date.now()}.png`}`;
    
    await fs.mkdir('screenshots', { recursive: true });
    await page.screenshot({ 
      path: screenshotPath,
      fullPage: fullPage 
    });
    
    await page.close();
    
    res.json({ 
      success: true, 
      message: 'Screenshot taken successfully',
      path: screenshotPath,
      url: url
    });
    
  } catch (error) {
    console.error('Screenshot error:', error);
    res.status(500).json({ 
      error: 'Failed to take screenshot',
      details: error.message 
    });
  }
});

app.post('/pdf', async (req, res) => {
  try {
    const { html, format = 'A3', landscape = true } = req.body;
    
    if (!html) {
      return res.status(400).json({ error: 'HTML content is required' });
    }

    await initBrowser();
    const page = await browser.newPage();
    
    // Set content
    await page.setContent(html, { waitUntil: 'networkidle0' });
    
    // Generate PDF
    const pdfBuffer = await page.pdf({
      format: 'A3',
      landscape: true,
      printBackground: true,
    });
    
    await page.close();
    
    res.setHeader('Content-Type', 'application/pdf');
    res.setHeader('Content-Disposition', 'attachment; filename="certificate.pdf"');
    res.send(pdfBuffer);
    
  } catch (error) {
    console.error('PDF generation error:', error);
    res.status(500).json({ 
      error: 'Failed to generate PDF',
      details: error.message 
    });
  }
});

app.get('/screenshot/:filename', async (req, res) => {
  try {
    const filename = req.params.filename;
    const filePath = path.join('screenshots', filename);
    
    await fs.access(filePath);
    res.sendFile(path.resolve(filePath));
  } catch (error) {
    res.status(404).json({ error: 'Screenshot not found' });
  }
});

app.get('/health', (req, res) => {
  res.json({ status: 'OK', timestamp: new Date().toISOString() });
});

process.on('SIGTERM', async () => {
  if (browser) {
    await browser.close();
  }
  process.exit(0);
});

app.listen(PORT, () => {
  console.log(`Screenshot API server running on port ${PORT}`);
  console.log(`Health check: http://localhost:${PORT}/health`);
  console.log(`Take screenshot: POST http://localhost:${PORT}/screenshot`);
  console.log(`Generate PDF: POST http://localhost:${PORT}/pdf`);
});
