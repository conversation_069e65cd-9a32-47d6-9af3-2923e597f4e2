import { NextRequest, NextResponse } from 'next/server';
import { db } from '@/lib/db';
import { classEnrollments, users, classes } from '@/lib/db/schema';
import { eq, and } from 'drizzle-orm';

// GET - Get all class enrollments or filter by classId/studentId
export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const classId = searchParams.get('classId');
    const studentId = searchParams.get('studentId');

    // Build where conditions
    let whereConditions;
    if (classId && studentId) {
      whereConditions = and(
        eq(classEnrollments.classId, parseInt(classId)),
        eq(classEnrollments.studentId, parseInt(studentId))
      );
    } else if (classId) {
      whereConditions = eq(classEnrollments.classId, parseInt(classId));
    } else if (studentId) {
      whereConditions = eq(classEnrollments.studentId, parseInt(studentId));
    }

    // Execute query with or without where clause
    const enrollments = whereConditions
      ? await db
          .select({
            id: classEnrollments.id,
            studentId: classEnrollments.studentId,
            classId: classEnrollments.classId,
            enrolledAt: classEnrollments.enrolledAt,
            status: classEnrollments.status,
            createdAt: classEnrollments.createdAt,
            updatedAt: classEnrollments.updatedAt,
            studentName: users.name,
            studentEmail: users.email,
            className: classes.name
          })
          .from(classEnrollments)
          .leftJoin(users, eq(classEnrollments.studentId, users.id))
          .leftJoin(classes, eq(classEnrollments.classId, classes.id))
          .where(whereConditions)
      : await db
          .select({
            id: classEnrollments.id,
            studentId: classEnrollments.studentId,
            classId: classEnrollments.classId,
            enrolledAt: classEnrollments.enrolledAt,
            status: classEnrollments.status,
            createdAt: classEnrollments.createdAt,
            updatedAt: classEnrollments.updatedAt,
            studentName: users.name,
            studentEmail: users.email,
            className: classes.name
          })
          .from(classEnrollments)
          .leftJoin(users, eq(classEnrollments.studentId, users.id))
          .leftJoin(classes, eq(classEnrollments.classId, classes.id));

    return NextResponse.json({
      success: true,
      data: enrollments
    });
  } catch (error) {
    console.error('Error fetching class enrollments:', error);
    return NextResponse.json(
      {
        success: false,
        error: 'Failed to fetch class enrollments'
      },
      { status: 500 }
    );
  }
}

// POST - Create new class enrollment
export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const { studentId, classId, status = 'active' } = body;

    // Validate required fields
    if (!studentId || !classId) {
      return NextResponse.json(
        {
          success: false,
          error: 'Student ID and Class ID are required'
        },
        { status: 400 }
      );
    }

    // Check if enrollment already exists
    const existingEnrollment = await db
      .select()
      .from(classEnrollments)
      .where(
        and(
          eq(classEnrollments.studentId, studentId),
          eq(classEnrollments.classId, classId)
        )
      )
      .limit(1);

    if (existingEnrollment.length > 0) {
      return NextResponse.json(
        {
          success: false,
          error: 'Student is already enrolled in this class'
        },
        { status: 409 }
      );
    }

    // Verify student exists and has student role
    const student = await db
      .select()
      .from(users)
      .where(and(eq(users.id, studentId), eq(users.role, 'student')))
      .limit(1);

    if (student.length === 0) {
      return NextResponse.json(
        {
          success: false,
          error: 'Student not found or user is not a student'
        },
        { status: 404 }
      );
    }

    // Verify class exists
    const classExists = await db
      .select()
      .from(classes)
      .where(eq(classes.id, classId))
      .limit(1);

    if (classExists.length === 0) {
      return NextResponse.json(
        {
          success: false,
          error: 'Class not found'
        },
        { status: 404 }
      );
    }

    // Create enrollment
    const newEnrollment = await db
      .insert(classEnrollments)
      .values({
        studentId,
        classId,
        status,
        enrolledAt: new Date(),
        createdAt: new Date(),
        updatedAt: new Date()
      })
      .returning();

    return NextResponse.json(
      {
        success: true,
        data: newEnrollment[0],
        message: 'Student enrolled successfully'
      },
      { status: 201 }
    );
  } catch (error) {
    console.error('Error creating class enrollment:', error);
    return NextResponse.json(
      {
        success: false,
        error: 'Failed to create class enrollment'
      },
      { status: 500 }
    );
  }
}