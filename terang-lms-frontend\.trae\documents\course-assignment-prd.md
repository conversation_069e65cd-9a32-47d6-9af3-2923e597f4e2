# Product Requirements Document - Course Assignment ke Class

## 1. Product Overview

Fitur Course Assignment memungkinkan guru untuk menugaskan course (mata pelajaran) ke class (kelas) yang mereka kelola dalam sistem LMS Terang. Fitur ini melengkapi sistem enrollment yang sudah ada dengan memberikan kontrol penuh kepada guru untuk mengatur distribusi materi pembelajaran.

Fitur ini menyelesaikan masalah pengorganisasian pembelajaran dimana guru perlu mengatur course mana yang diajarkan di kelas mana, memudahkan manajemen kurikulum dan tracking progress siswa per kelas.

## 2. Core Features

### 2.1 User Roles

| Role    | Registration Method                         | Core Permissions                                                                      |
| ------- | ------------------------------------------- | ------------------------------------------------------------------------------------- |
| Teacher | Email registration + institution assignment | Dapat assign/remove courses ke classes yang mereka kelola dalam institution yang sama |
| Admin   | System invitation                           | Dapat melihat semua course assignments dalam institution                              |

### 2.2 Feature Module

Fitur Course Assignment terdiri dari halaman-halaman utama berikut:

1. **Course Assignment Management Page**: interface utama untuk mengelola assignment, search dan filter assignments
2. **Class Detail Enhancement**: section tambahan di halaman detail class untuk melihat dan mengelola assigned courses
3. **Course Detail Enhancement**: section tambahan di halaman detail course untuk melihat assigned classes
4. **Assignment Dialog**: modal untuk melakukan assignment baru dengan dropdown selection

### 2.3 Page Details

| Page Name                    | Module Name              | Feature description                                                                                        |
| ---------------------------- | ------------------------ | ---------------------------------------------------------------------------------------------------------- |
| Course Assignment Management | Assignment Table         | Display active course-class assignments dengan course name, class name, assignment date, dan remove action |
| Course Assignment Management | Search & Filter          | Search assignments by course/class name, filter by date range atau status                                  |
| Course Assignment Management | Assign Course Dialog     | Modal dengan dropdown selection untuk course dan class, validation, dan assignment creation                |
| Class Detail Enhancement     | Assigned Courses Section | Display courses yang assigned ke class ini dengan course details dan quick remove action                   |
| Class Detail Enhancement     | Quick Assign Button      | Button untuk membuka assignment dialog langsung untuk class ini                                            |
| Course Detail Enhancement    | Assigned Classes Section | Display classes dimana course ini di-assign dengan class details dan assignment statistics                 |
| Assignment Dialog            | Course Selection         | Dropdown menampilkan courses yang dimiliki teacher dengan search functionality                             |
| Assignment Dialog            | Class Selection          | Dropdown menampilkan classes yang dimiliki teacher dengan student count info                               |
| Assignment Dialog            | Validation & Submit      | Form validation, duplicate check, dan assignment creation dengan success/error feedback                    |

## 3. Core Process

### Teacher Flow - Course Assignment

1. Teacher login dan masuk ke dashboard
2. Teacher navigasi ke "Course Assignments" atau ke detail class/course
3. Teacher klik "Assign Course" button
4. System menampilkan assignment dialog dengan dropdown courses dan classes
5. Teacher pilih course dan class yang ingin di-assign
6. System validasi ownership, institution matching, dan duplicate prevention
7. System create assignment dan update UI dengan success message
8. Teacher dapat melihat assignment baru di table/list

### Teacher Flow - Remove Assignment

1. Teacher melihat list active assignments
2. Teacher klik "Remove" button pada assignment tertentu
3. System menampilkan confirmation dialog
4. Teacher confirm removal
5. System hapus assignment dan update UI

```mermaid
graph TD
    A[Teacher Dashboard] --> B[Course Assignments Page]
    A --> C[Class Detail Page]
    A --> D[Course Detail Page]
    
    B --> E[Assignment Table]
    B --> F[Assign Course Dialog]
    
    C --> G[Assigned Courses Section]
    C --> F
    
    D --> H[Assigned Classes Section]
    
    F --> I[Course Selection]
    F --> J[Class Selection]
    F --> K[Submit Assignment]
    
    E --> L[Remove Assignment]
    G --> L
    
    K --> M[Success/Error Feedback]
    L --> N[Confirmation Dialog]
```

## 4. User Interface Design

### 4.1 Design Style

* **Primary Colors**: Blue (#3B82F6) untuk primary actions, Green (#10B981) untuk success states

* **Secondary Colors**: Gray (#6B7280) untuk secondary text, Red (#EF4444) untuk destructive actions

* **Button Style**: Rounded corners (rounded-md), solid primary buttons, outline secondary buttons

* **Font**: Inter font family, 14px base size, 16px untuk headings, 12px untuk captions

* **Layout Style**: Card-based layout dengan clean spacing, consistent padding (p-4, p-6)

* **Icons**: Lucide React icons dengan 16px size, Plus icon untuk add actions, Trash icon untuk remove

### 4.2 Page Design Overview

| Page Name                    | Module Name           | UI Elements                                                                                                                                |
| ---------------------------- | --------------------- | ------------------------------------------------------------------------------------------------------------------------------------------ |
| Course Assignment Management | Header Section        | Page title "Course Assignments", search input dengan search icon, "Assign Course" primary button dengan plus icon                          |
| Course Assignment Management | Assignment Table      | Table dengan columns: Course Name (bold), Class Name, Assigned Date (formatted), Actions (remove button). Hover effects dan zebra striping |
| Course Assignment Management | Empty State           | Illustration dengan text "No course assignments yet" dan "Assign Course" call-to-action button                                             |
| Assignment Dialog            | Modal Layout          | White background, rounded corners, shadow overlay. Header dengan title dan close button                                                    |
| Assignment Dialog            | Form Elements         | Label + Select dropdowns dengan search functionality, disabled state handling, loading spinners                                            |
| Assignment Dialog            | Action Buttons        | Cancel (outline) dan "Assign Course" (primary) buttons, loading state dengan spinner                                                       |
| Class Detail Enhancement     | Assigned Courses Card | Card layout dengan header "Assigned Courses", course list dengan course name, code, dan remove button                                      |
| Course Detail Enhancement    | Assigned Classes Grid | Grid layout menampilkan class cards dengan class name, student count, dan assignment date                                                  |

### 4.3 Responsiveness

Design menggunakan mobile-first approach dengan breakpoints:

* **Mobile (< 768px)**: Single column layout, stacked form elements, full-width buttons

* **Tablet (768px - 1024px)**: Two-column grid untuk cards, responsive table dengan horizontal scroll

* **Desktop (> 1024px)**: Full table layout, multi-column grids, side-by-side form elements

Touch interaction optimization untuk mobile dengan larger touch targets (min 44px) dan appropriate spacing untuk finger navigation.

## 5. Technical Requirements

### 5.1 Performance Requirements

* Assignment table harus load dalam < 2 detik untuk 100+ assignments

* Search functionality harus responsive dengan debouncing 300ms

* Dialog open/close animations harus smooth (< 200ms)

### 5.2 Data Requirements

* Support untuk 1000+ course assignments per teacher

* Real-time updates ketika assignments berubah

* Proper error handling untuk network failures

### 5.3 Security Requirements

* Teacher authorization untuk semua assignment operations

* Institution-level data isolation

* Input validation dan sanitization

## 6. Success Metrics

### 6.1 User Adoption

* 80% teachers menggunakan course assignment dalam 30 hari pertama

* Average 5+ assignments per teacher per bulan

* < 5% error rate dalam assignment operations

### 6.2 User Experience

* Task completion rate > 95% untuk assignment creation

* Average time < 30 detik untuk create assignment

* User satisfaction score > 4.5/5

### 6.3 System Performance

* Page load time < 2 detik

* API response time < 500ms

* 99.9% uptime untuk assignment features

## 7. User Stories

### 7.1 Core User Stories

**As a teacher, I want to assign a course to a class so that students in that class can access the course materials.**

* Acceptance Criteria:

  * I can select from my available courses

  * I can select from my managed classes

  * System prevents duplicate assignments

  * I receive confirmation of successful assignment

**As a teacher, I want to see all my course assignments in one place so that I can manage them efficiently.**

* Acceptance Criteria:

  * I can view all active assignments in a table

  * I can search assignments by course or class name

  * I can see assignment dates and details

  * I can remove assignments when needed

**As a teacher, I want to quickly assign courses when viewing a class detail so that I can manage class curriculum efficiently.**

* Acceptance Criteria:

  * I can see currently assigned courses in class detail

  * I can assign new courses directly from class page

  * I can remove course assignments from class page

  * Changes are reflected immediately

### 7.2 Edge Case Stories

**As a teacher, I want to be prevented from assigning courses I don't own so that data integrity is maintained.**

* Acceptance Criteria:

  * Only my courses appear in selection dropdown

  * System validates ownership on submission

  * Clear error message if validation fails

**As a teacher, I want to be prevented from duplicate assignments so that my data stays clean.**

* Acceptance Criteria:

  * System checks for existing assignments

  * Clear warning message for duplicates

  * Option to view existing assignment

## 8. Implementation Priority

### 8.1 MVP (Must Have)

* Course assignment creation dan removal

* Basic assignment table dengan search

* Teacher authorization dan validation

* Assignment dialog dengan course/class selection

### 8.2 Phase 2 (Should Have)

* Enhanced class detail dengan assigned courses section

* Enhanced course detail dengan assigned classes section

* Improved search dan filtering

* Better error handling dan user feedback

### 8.3 Phase 3 (Nice to Have)

* Bulk assignment operations

* Assignment analytics dan reporting

* Advanced filtering options

* Export/import functionality

## 9. Risk Assessment

### 9.1 Technical Risks

* **Database Performance**: Large number of assignments might slow queries

  * Mitigation: Proper indexing dan pagination

* **UI Complexity**: Multiple selection states might confuse users

  * Mitigation: Clear UI patterns dan user testing

### 9.2 User Experience Risks

* **Learning Curve**: New interface might be confusing

  * Mitigation: Intuitive design dan onboarding tooltips

* **Data Loss**: Accidental assignment removal

  * Mitigation: Confirmation dialogs dan undo functionality

### 9.3 Business Risks

* **Low Adoption**: Teachers might not use the feature

  * Mitigation: Clear value proposition dan training materials

* **Support Load**: Complex feature might increase support requests

  * Mitigation: Comprehensive documentation dan error messages

## 10. Launch Plan

### 10.1 Development Phase (2 weeks)

* Week 1: Core assignment functionality

* Week 2: UI enhancements dan testing

### 10.2 Testing Phase (1 week)

* Unit testing untuk all components

* Integration testing untuk assignment flow

* User acceptance testing dengan sample teachers

### 10.3 Launch Phase (1 week)

* Gradual rollout ke selected institutions

* Monitor performance dan user feedback

* Quick fixes untuk critical issues

### 10.4 Post-Launch (Ongoing)

* Weekly performance monitoring

* Monthly user feedback collection

* Quarterly feature enhancement planning

