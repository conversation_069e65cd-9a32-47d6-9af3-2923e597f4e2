import { NextRequest, NextResponse } from 'next/server';
import { db } from '@/lib/db';
import { courseEnrollments, studentEnrollments, courses, users } from '@/lib/db/schema';
import { eq, and } from 'drizzle-orm';

// GET /api/enrollments/[id] - Get a specific enrollment
export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const { id } = await params;
    const enrollmentId = parseInt(id);
    const searchParams = request.nextUrl.searchParams;
    const type = searchParams.get('type'); // 'course' or 'student'
    
    if (isNaN(enrollmentId)) {
      return NextResponse.json({ error: 'Invalid enrollment ID' }, { status: 400 });
    }

    if (type === 'course') {
      // Get course enrollment
      const enrollment = await db
        .select({
          id: courseEnrollments.id,
          courseId: courseEnrollments.courseId,
          classId: courseEnrollments.classId,
          enrolledAt: courseEnrollments.enrolledAt,
          courseName: courses.name,
          courseCode: courses.courseCode
        })
        .from(courseEnrollments)
        .leftJoin(courses, eq(courseEnrollments.courseId, courses.id))
        .where(eq(courseEnrollments.id, enrollmentId))
        .limit(1);

      if (enrollment.length === 0) {
        return NextResponse.json({ error: 'Course enrollment not found' }, { status: 404 });
      }

      return NextResponse.json({ enrollment: enrollment[0] });
    } else if (type === 'student') {
      // Get student enrollment
      const enrollment = await db
        .select({
          id: studentEnrollments.id,
          studentId: studentEnrollments.studentId,
          courseId: studentEnrollments.courseId,
          enrolledAt: studentEnrollments.enrolledAt,
          completedAt: studentEnrollments.completedAt,
          finalScore: studentEnrollments.finalScore,
          certificateGenerated: studentEnrollments.certificateGenerated,
          studentName: users.name,
          studentEmail: users.email,
          courseName: courses.name,
          courseCode: courses.courseCode
        })
        .from(studentEnrollments)
        .leftJoin(users, eq(studentEnrollments.studentId, users.id))
        .leftJoin(courses, eq(studentEnrollments.courseId, courses.id))
        .where(eq(studentEnrollments.id, enrollmentId))
        .limit(1);

      if (enrollment.length === 0) {
        return NextResponse.json({ error: 'Student enrollment not found' }, { status: 404 });
      }

      return NextResponse.json({ success: true, enrollment: enrollment[0] });
    } else {
      return NextResponse.json({ error: 'Type parameter required (course or student)' }, { status: 400 });
    }
  } catch (error) {
    console.error('Error fetching enrollment:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}

// DELETE /api/enrollments/[id] - Remove an enrollment
export async function DELETE(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const { id } = await params;
    const enrollmentId = parseInt(id);
    const searchParams = request.nextUrl.searchParams;
    const type = searchParams.get('type'); // 'course' or 'student'
    const teacherId = searchParams.get('teacherId');
    
    if (isNaN(enrollmentId)) {
      return NextResponse.json({ error: 'Invalid enrollment ID' }, { status: 400 });
    }

    if (!teacherId) {
      return NextResponse.json({ error: 'Teacher ID required' }, { status: 400 });
    }

    if (type === 'course') {
      // Remove course enrollment (course from class)
      const enrollment = await db
        .select({
          id: courseEnrollments.id,
          courseId: courseEnrollments.courseId,
          classId: courseEnrollments.classId,
          teacherId: courses.teacherId
        })
        .from(courseEnrollments)
        .leftJoin(courses, eq(courseEnrollments.courseId, courses.id))
        .where(eq(courseEnrollments.id, enrollmentId))
        .limit(1);

      if (enrollment.length === 0) {
        return NextResponse.json({ error: 'Course enrollment not found' }, { status: 404 });
      }

      // Verify teacher has permission
      if (enrollment[0].teacherId !== parseInt(teacherId)) {
        return NextResponse.json(
          { error: 'Not authorized to remove this enrollment' },
          { status: 403 }
        );
      }

      // Remove the course enrollment
      await db.delete(courseEnrollments).where(eq(courseEnrollments.id, enrollmentId));

      return NextResponse.json({ success : true, message: 'Course enrollment removed successfully' });
    } else if (type === 'student') {
      // Remove student enrollment (student from course)
      const enrollment = await db
        .select({
          id: studentEnrollments.id,
          studentId: studentEnrollments.studentId,
          courseId: studentEnrollments.courseId,
          teacherId: courses.teacherId
        })
        .from(studentEnrollments)
        .leftJoin(courses, eq(studentEnrollments.courseId, courses.id))
        .where(eq(studentEnrollments.id, enrollmentId))
        .limit(1);

      if (enrollment.length === 0) {
        return NextResponse.json({ error: 'Student enrollment not found' }, { status: 404 });
      }

      // Verify teacher has permission
      if (enrollment[0].teacherId !== parseInt(teacherId)) {
        return NextResponse.json(
          { error: 'Not authorized to remove this enrollment' },
          { status: 403 }
        );
      }

      // Remove the student enrollment
      await db.delete(studentEnrollments).where(eq(studentEnrollments.id, enrollmentId));

      return NextResponse.json({ success : true, message: 'Student enrollment removed successfully' });
    } else {
      return NextResponse.json({ error: 'Type parameter required (course or student)' }, { status: 400 });
    }
  } catch (error) {
    console.error('Error removing enrollment:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}