import React from 'react';
import { ChevronDown, ChevronRight, Lock, CheckCircle2 } from 'lucide-react';
import { TreeNodeProps } from '@/types/lms';
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from '@/components/ui/tooltip';

export const TreeNode: React.FC<TreeNodeProps> = ({
  title,
  icon,
  isUnlocked,
  isCompleted = false,
  children,
  level,
  isExpanded = false,
  onToggle,
  onClick,
  hasChildren = false,
  isActive = false
}) => {
  // Check if text might be truncated (rough estimate)
  const mightBeTruncated = title.length > 20;

  return (
    <div className='select-none'>
      <TooltipProvider>
        <Tooltip delayDuration={500}>
          <TooltipTrigger asChild>
            <button
              onClick={() => {
                if (hasChildren && onToggle) {
                  onToggle();
                } else if (onClick) {
                  onClick();
                }
              }}
              className={`flex w-full items-center space-x-3 rounded-lg px-4 py-3 text-left text-base transition-colors ${
                isUnlocked
                  ? isActive
                    ? 'bg-blue-100 text-blue-800 border-2 border-blue-300 hover:bg-blue-150'
                    : 'text-blue-700 hover:bg-blue-50'
                  : 'cursor-not-allowed text-gray-400'
              }`}
              style={{ paddingLeft: `${level * 20 + 16}px` }}
              disabled={!isUnlocked}
            >
              {hasChildren && (
                <span className='flex h-5 w-5 items-center justify-center'>
                  {isExpanded ? (
                    <ChevronDown className='h-4 w-4' />
                  ) : (
                    <ChevronRight className='h-4 w-4' />
                  )}
                </span>
              )}
              <span className='flex h-5 w-5 items-center justify-center'>{icon}</span>
              <span className={`flex-1 font-medium ${title.length > 35 ? 'text-sm leading-tight' : 'truncate'}`}>
                {title.length > 35 ? (
                  <span className="block">{title}</span>
                ) : (
                  title
                )}
              </span>
              {!isUnlocked && <Lock className='h-4 w-4' />}
              {isCompleted && <CheckCircle2 className='h-4 w-4 text-green-500' />}
            </button>
          </TooltipTrigger>
          {mightBeTruncated && (
            <TooltipContent side="right" className="max-w-xs">
              <p>{title}</p>
            </TooltipContent>
          )}
        </Tooltip>
      </TooltipProvider>
      {isExpanded && children && <div>{children}</div>}
    </div>
  );
};
