import { GoogleGenAI, Type } from '@google/genai';
import { AIGenerationError } from './types';

// Export Type for external use
export { Type };

// Schema configuration interface for structured output
export interface SchemaConfig {
  responseMimeType: 'application/json';
  responseSchema: any;
}

// Chat history interface
export interface ChatMessage {
  role: 'user' | 'model';
  parts: Array<{ text?: string; inlineData?: { mimeType: string; data: string } }>;
}

// Chat session interface
export interface ChatSession {
  id: string;
  chat: any;
  history: ChatMessage[];
  createdAt: Date;
}

// Singleton instance for Gemini AI client
let geminiInstance: GoogleGenAI | null = null;

// Chat sessions storage
const chatSessions = new Map<string, ChatSession>();

/**
 * Get or create Gemini AI client instance
 * @returns GoogleGenAI instance
 * @throws AIGenerationError if API key is not configured
 */
export const getGeminiClient = (): GoogleGenAI => {
  if (geminiInstance) {
    return geminiInstance;
  }

  const apiKey = process.env.NEXT_PUBLIC_GEMINI_API_KEY;
  if (!apiKey) {
    throw new AIGenerationError(
      'Gemini API key is not configured. Please set NEXT_PUBLIC_GEMINI_API_KEY environment variable.',
      'MISSING_API_KEY'
    );
  }

  try {
    geminiInstance = new GoogleGenAI({ apiKey });
    return geminiInstance;
  } catch (error) {
    throw new AIGenerationError(
      'Failed to initialize Gemini AI client',
      'CLIENT_INIT_ERROR',
      error
    );
  }
};

/**
 * Convert file to base64 for Gemini API
 * @param file File to convert
 * @returns Promise<string> Base64 encoded file content
 */
export const fileToBase64 = async (file: File): Promise<string> => {
  try {
    const arrayBuffer = await file.arrayBuffer();
    return Buffer.from(arrayBuffer).toString('base64');
  } catch (error) {
    throw new AIGenerationError(
      'Failed to convert file to base64',
      'FILE_CONVERSION_ERROR',
      error
    );
  }
};

/**
 * Generate content with Gemini API using structured output
 * @param contents Content array for Gemini API
 * @param schema Schema configuration for structured output
 * @param model Model to use (default: gemini-2.5-flash)
 * @returns Promise<T> Parsed JSON object
 */
export const generateWithGeminiStructured = async <T>(
  contents: any[],
  schema: SchemaConfig,
  model: string = 'gemini-2.5-flash'
): Promise<T> => {
  try {
    const client = getGeminiClient();
    const response = await client.models.generateContent({
      model,
      contents,
      config: {
        ...schema,
        thinkingConfig: {
          thinkingBudget: 0 // Disable thinking for faster responses
        }
      }
    });
    const text = response.text || '';
    if (!text.trim()) {
      throw new Error('Empty response from Gemini API');
    }
    
    // With structured output, the response should already be valid JSON
    try {
      const parsed = JSON.parse(text) as T;
      // 🔍 Log hasil JSON setelah parsing
      console.log('[Gemini Structured Output - Parsed]', parsed);
      return parsed;
    } catch (parseError) {
      throw new Error(`Failed to parse structured output: ${parseError}`);
    }
  } catch (error) {
    throw new AIGenerationError(
      'Failed to generate structured content with Gemini API',
      'STRUCTURED_GENERATION_ERROR',
      error
    );
  }
};

/**
 * Generate content with Gemini API using streaming for live preview
 * @param contents Content array for Gemini API
 * @param schema Schema configuration for structured output
 * @param onChunk Callback for streaming chunks
 * @param model Model to use (default: gemini-2.5-flash)
 * @returns Promise<T> Final parsed JSON object
 */
export const generateWithGeminiStructuredStream = async <T>(
  contents: any[],
  schema: SchemaConfig,
  onChunk?: (chunk: string) => void,
  model: string = 'gemini-2.5-flash'
): Promise<T> => {
  try {
    const client = getGeminiClient();
    const stream = await client.models.generateContentStream({
      model,
      contents,
      config: {
        ...schema,
        thinkingConfig: {
          thinkingBudget: 0 // Disable thinking for faster responses
        }
      }
    });
    
    let fullText = '';
    
    // Process streaming chunks
    for await (const chunk of stream) {
      const chunkText = chunk.text || '';
      if (chunkText) {
        fullText += chunkText;
        if (onChunk) {
          onChunk(chunkText);
        }
      }
    }
    
    if (!fullText.trim()) {
      throw new Error('Empty response from Gemini API');
    }
    
    // With structured output, the response should already be valid JSON
    try {
      const parsed = JSON.parse(fullText) as T;
      console.log('[Gemini Structured Stream - Final Parsed]', parsed);
      return parsed;
    } catch (parseError) {
      throw new Error(`Failed to parse structured output: ${parseError}`);
    }
  } catch (error) {
    throw new AIGenerationError(
      'Failed to generate structured content with Gemini API streaming',
      'STRUCTURED_STREAM_ERROR',
      error
    );
  }
};

/**
 * Create a new chat session with Gemini
 * @param sessionId Unique identifier for the chat session
 * @param initialHistory Optional initial chat history
 * @param model Model to use (default: gemini-2.5-flash)
 * @returns ChatSession object
 */
export const createChatSession = (sessionId: string, initialHistory: ChatMessage[] = [], model: string = 'gemini-2.5-flash'): ChatSession => {
  try {
    const client = getGeminiClient();
    
    const chat = client.chats.create({
      model,
      history: initialHistory
    });

    const session: ChatSession = {
      id: sessionId,
      chat,
      history: [...initialHistory],
      createdAt: new Date()
    };

    chatSessions.set(sessionId, session);
    return session;
  } catch (error) {
    throw new AIGenerationError(
      'Failed to create chat session',
      'CHAT_SESSION_ERROR',
      error
    );
  }
};

/**
 * Get existing chat session or create a new one
 * @param sessionId Unique identifier for the chat session
 * @param initialHistory Optional initial chat history (only used if creating new session)
 * @param model Model to use (default: gemini-2.5-flash)
 * @returns ChatSession object
 */
export const getChatSession = (sessionId: string, initialHistory: ChatMessage[] = [], model: string = 'gemini-2.5-flash'): ChatSession => {
  const existingSession = chatSessions.get(sessionId);
  if (existingSession) {
    return existingSession;
  }
  return createChatSession(sessionId, initialHistory, model);
};


/**
 * Send structured or unstructured message to chat session based on schema parameter
 * @param sessionId Chat session ID
 * @param message Message to send
 * @param schema Schema configuration for structured output (null for unstructured)
 * @param onChunk Optional callback for streaming chunks (works with both schema and non-schema modes)
 * @returns Promise<T | string> Parsed JSON response if schema provided, raw text if not
 */
export const sendStructuredChatMessage = async <T>(
  sessionId: string,
  message: ChatMessage,
  schema: SchemaConfig | null,
  onChunk?: (chunk: string) => void
): Promise<T | string> => {
  try {
    // LOG: Mulai grup log untuk panggilan fungsi ini agar rapi
    console.group(`[sendStructuredChatMessage] Session ID: ${sessionId}, Schema: ${schema ? 'YES' : 'NO'}`);

    const session = chatSessions.get(sessionId);
    if (!session) {
      throw new AIGenerationError(
        `Chat session not found: ${sessionId}`,
        'SESSION_NOT_FOUND'
      );
    }

    // 1. Add user message to history
    session.history.push(message);

    // 2. Determine which generation method to use
    const fullHistory = session.history;
    
    // LOG: Tampilkan riwayat lengkap yang akan dikirim ke API
    console.log("--> 📩 Sending history to Gemini:");
    console.table(fullHistory);

    let responseText: string;
    let parsedResponse: T | string;

    if (schema) {
      // Use structured output with optional streaming
      if (onChunk) {
        console.log("🔧 Using STRUCTURED STREAMING output with schema");
        const structuredResult = await generateWithGeminiStructuredStream<T>(
          fullHistory,
          schema,
          onChunk
        );
        
        responseText = JSON.stringify(structuredResult);
        parsedResponse = structuredResult;
        
        console.log("<-- 📄 Received parsed structured streaming response from Gemini:");
        console.log(structuredResult);
      } else {
        console.log("🔧 Using STRUCTURED output with schema");
        const structuredResult = await generateWithGeminiStructured<T>(
          fullHistory,
          schema
        );
        
        responseText = JSON.stringify(structuredResult);
        parsedResponse = structuredResult;
        
        console.log("<-- 📄 Received parsed structured response from Gemini:");
        console.log(structuredResult);
      }
    } else {
      // Use regular text generation with optional streaming
      if (onChunk) {
        console.log("📝 Using REGULAR STREAMING text output (no schema)");
        const client = getGeminiClient();
        const stream = await client.models.generateContentStream({
          model: 'gemini-2.5-flash',
          contents: fullHistory,
          config: {
            thinkingConfig: {
              thinkingBudget: 0
            }
          }
        });
        
        let fullText = '';
        
        // Process streaming chunks
        for await (const chunk of stream) {
          const chunkText = chunk.text || '';
          if (chunkText) {
            fullText += chunkText;
            onChunk(chunkText);
          }
        }
        
        responseText = fullText;
        parsedResponse = responseText;
        
        console.log("<-- 📝 Received streaming raw text response from Gemini:");
        console.log(responseText);
      } else {
        console.log("📝 Using REGULAR text output (no schema)");
        const client = getGeminiClient();
        const response = await client.models.generateContent({
          model: 'gemini-2.5-flash',
          contents: fullHistory,
          config: {
            thinkingConfig: {
              thinkingBudget: 0
            }
          }
        });
        
        responseText = response.text || '';
        parsedResponse = responseText;
        
        console.log("<-- 📝 Received raw text response from Gemini:");
        console.log(responseText);
      }
    }

    if (!responseText.trim()) {
      throw new Error('Empty response from Gemini API');
    }

    // 3. Add model response to history to maintain context
    session.history.push({
      role: 'model',
      parts: [{ text: responseText }],
    });

    console.log("✅ History updated successfully.");

    // 4. Return the appropriate response type
    return parsedResponse;

  } catch (error) {
    console.error("[sendStructuredChatMessage] Error:", error);

    if (error instanceof AIGenerationError) {
      throw error;
    }
    throw new AIGenerationError(
      'Failed to send chat message',
      'CHAT_MESSAGE_ERROR',
      error
    );
  } finally {
    console.groupEnd();
  }
};

/**
 * Clear chat session
 * @param sessionId Chat session ID
 */
export const clearChatSession = (sessionId: string): void => {
  chatSessions.delete(sessionId);
};

/**
 * Get chat session history
 * @param sessionId Chat session ID
 * @returns ChatMessage[] Chat history
 */
export const getChatHistory = (sessionId: string): ChatMessage[] => {
  const session = chatSessions.get(sessionId);
  return session ? [...session.history] : [];
};

/**
 * Reset the Gemini client instance (useful for testing)
 */
export const resetGeminiClient = (): void => {
  geminiInstance = null;
  chatSessions.clear();
};