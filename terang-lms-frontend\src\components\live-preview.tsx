'use client';

import React, { useEffect, useRef, useState, useCallback } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { ScrollArea } from '@/components/ui/scroll-area';
import { Badge } from '@/components/ui/badge';
import { Eye, Loader2 } from 'lucide-react';
import { motion, AnimatePresence } from 'framer-motion';

interface LivePreviewProps {
  isStreaming: boolean;
  streamingText: string;
  currentStep?: string;
  className?: string;
}

// Buffer untuk mengelompokkan baris per 5
interface TextBuffer {
  lines: string[];
  id: string;
}

export function LivePreview({ 
  isStreaming, 
  streamingText, 
  currentStep,
  className = '' 
}: LivePreviewProps) {
  const scrollAreaRef = useRef<HTMLDivElement>(null);
  const [displayBuffer, setDisplayBuffer] = useState<TextBuffer[]>([]);
  const [bufferedLines, setBufferedLines] = useState<string[]>([]);
  const bufferRef = useRef<string[]>([]);
  const maxLines = 5;

  // Buffer incoming lines
  const processBuffer = useCallback(() => {
    if (bufferRef.current.length === 0) return;

    const newBuffer: TextBuffer = {
      lines: [...bufferRef.current],
      id: `buffer-${Date.now()}-${Math.random()}`
    };

    setDisplayBuffer(prev => {
      const updated = [...prev, newBuffer];
      // Keep only last 2 buffers (10 lines max)
      if (updated.length > 2) {
        return updated.slice(-2);
      }
      return updated;
    });

    // Update lines for character counter
    setBufferedLines(prev => {
      const allLines = [...prev, ...bufferRef.current];
      return allLines.slice(-maxLines * 2); // Keep more for counting
    });

    bufferRef.current = [];
  }, []);

  // Throttle buffer processing
  useEffect(() => {
    const interval = setInterval(() => {
      processBuffer();
    }, 1000); // Process every 1.5 second

    return () => clearInterval(interval);
  }, [processBuffer]);

  // Collect incoming lines
  useEffect(() => {
    if (!streamingText.trim()) {
      setDisplayBuffer([]);
      setBufferedLines([]);
      bufferRef.current = [];
      return;
    }

    const lines = streamingText.split('\n').filter(line => line.trim());
    bufferRef.current = lines.slice(-maxLines); // Keep latest 5 lines
  }, [streamingText]);

  // Smooth scroll to latest buffer
  useEffect(() => {
    if (scrollAreaRef.current && displayBuffer.length > 0) {
      const scrollElement = scrollAreaRef.current.querySelector('[data-radix-scroll-area-viewport]');
      if (scrollElement) {
        setTimeout(() => {
          scrollElement.scrollTo({
            top: scrollElement.scrollHeight,
            behavior: 'smooth'
          });
        }, 100);
      }
    }
  }, [displayBuffer]);

  if (!isStreaming && !streamingText) {
    return null;
  }

  return (
    <Card className={`${className} border-blue-200 bg-blue-50/50`}>
      <CardHeader className="pb-3">
        <CardTitle className="flex items-center gap-2 text-sm">
          <Eye className="w-4 h-4 text-blue-600" />
          AI Live Preview
          {isStreaming && (
            <Badge variant="secondary" className="ml-auto">
              <Loader2 className="w-3 h-3 mr-1 animate-spin" />
              Generating...
            </Badge>
          )}
        </CardTitle>
        {currentStep && (
          <p className="text-xs text-gray-600 mt-1">
            {currentStep}
          </p>
        )}
      </CardHeader>
      <CardContent>
        <ScrollArea 
          ref={scrollAreaRef}
          className="h-32 w-full rounded border bg-white p-3 overflow-hidden"
        >
          <div className="space-y-2 text-sm font-mono">
            <AnimatePresence mode="popLayout">
              {displayBuffer.length === 0 ? (
                <motion.div
                  key="placeholder"
                  initial={{ opacity: 0 }}
                  animate={{ opacity: 1 }}
                  exit={{ opacity: 0 }}
                  className="text-gray-400 italic"
                >
                  {isStreaming ? 'Waiting for AI response...' : 'No content'}
                </motion.div>
              ) : (
                displayBuffer.map((buffer) => (
                  <motion.div
                    key={buffer.id}
                    initial={{ 
                      opacity: 0, 
                      y: 20,
                      height: 0
                    }}
                    animate={{ 
                      opacity: 1, 
                      y: 0,
                      height: 'auto'
                    }}
                    exit={{ 
                      opacity: 0, 
                      y: -20,
                      height: 0,
                      transition: { duration: 0.3 }
                    }}
                    transition={{ 
                      duration: 0.5,
                      ease: [0.4, 0, 0.2, 1]
                    }}
                    className="space-y-1"
                  >
                    {buffer.lines.map((line, lineIndex) => (
                      <motion.div
                        key={`${buffer.id}-${lineIndex}`}
                        initial={{ opacity: 0, x: -10 }}
                        animate={{ opacity: 1, x: 0 }}
                        transition={{ 
                          duration: 0.3,
                          delay: lineIndex * 0.1
                        }}
                        className="text-gray-700"
                      >
                        {line}
                      </motion.div>
                    ))}
                  </motion.div>
                ))
              )}
            </AnimatePresence>
          </div>
        </ScrollArea>
        
        {streamingText && (
          <div className="mt-2 text-xs text-gray-500">
            Characters: {streamingText.length}
            {bufferedLines.length > 0 && (
              <span className="ml-2">
                Lines: {bufferedLines.length} (buffered)
              </span>
            )}
          </div>
        )}
      </CardContent>
    </Card>
  );
}

export default LivePreview;