import React, { useState } from 'react';
import { Button } from '@/components/ui/button';
import { BookMarked, Target, Award, Maximize, Minimize, X, ChevronLeft, ChevronRight, FileText } from 'lucide-react';
import { Course } from '@/types/lms';
import { TableOfContents } from '../table-of-contents';
import { ContentViewer } from '../content-viewer';
import { QuizIntroduction } from '../quiz-introduction';

interface CourseTabProps {
  courseData: Course;
  expandedModules: { [key: string]: boolean };
  expandedChapters: { [key: string]: boolean };
  expandedContents: { [key: string]: boolean };
  onToggleModule: (moduleId: string) => void;
  onToggleChapter: (chapterId: string) => void;
  onToggleContent: (contentId: string) => void;
  onToggleContentComplete: (contentId: string) => void;
  onStartQuiz: (quizId: string) => void;
  onNavigateToSection: (moduleId: string, chapterId?: string, contentId?: string) => void;
  onExpandAllModules: () => void;
  onCollapseAllModules: () => void;
  onExpandAllChaptersInModule: (moduleId: string) => void;
  onCollapseAllChaptersInModule: (moduleId: string) => void;
  onNavigateToFinalExam?: () => void;
  isLearningMode?: boolean;
  currentModuleId?: string;
  currentChapterId?: string;
  currentContentId?: string;
}

export const CourseTab: React.FC<CourseTabProps> = ({
  courseData,
  expandedModules,
  expandedChapters,
  expandedContents,
  onToggleModule,
  onToggleChapter,
  onToggleContent,
  onToggleContentComplete,
  onStartQuiz,
  onNavigateToSection,
  onExpandAllModules,
  onCollapseAllModules,
  onExpandAllChaptersInModule,
  onCollapseAllChaptersInModule,
  onNavigateToFinalExam,
  isLearningMode,
  currentModuleId,
  currentChapterId,
  currentContentId
}) => {
  const [showCourseStructure, setShowCourseStructure] = useState(true);
  const [currentModuleIndex, setCurrentModuleIndex] = useState(0);
  const [showQuizIntro, setShowQuizIntro] = useState(false);
  const [currentQuiz, setCurrentQuiz] = useState<any>(null);
  const [quizType, setQuizType] = useState<'chapter' | 'module' | 'final'>('chapter');

  const allModulesExpanded = courseData.modules
    .filter((m) => m.isUnlocked)
    .every((m) => expandedModules[m.id]);

  const currentModule = courseData.modules[currentModuleIndex];
  const canGoPrevious = currentModuleIndex > 0;
  const canGoNext = currentModuleIndex < courseData.modules.length - 1;
  const isLastModule = currentModuleIndex === courseData.modules.length - 1;

  const handlePreviousModule = () => {
    if (canGoPrevious) {
      setCurrentModuleIndex(currentModuleIndex - 1);
    }
  };

  const handleNextModule = () => {
    if (canGoNext) {
      setCurrentModuleIndex(currentModuleIndex + 1);
    }
  };

  const handleNextAction = () => {
    if (isLastModule && onNavigateToFinalExam) {
      onNavigateToFinalExam();
    } else {
      handleNextModule();
    }
  };

  const handleModuleNavigation = (moduleId: string) => {
    const moduleIndex = courseData.modules.findIndex(m => m.id === moduleId);
    if (moduleIndex !== -1) {
      setCurrentModuleIndex(moduleIndex);
    }
  };

  const findQuizById = (quizId: string, type: 'chapter' | 'module' | 'final') => {
    if (type === 'final') {
      return courseData.finalExam;
    }
    
    for (const module of courseData.modules) {
      if (type === 'module' && module.moduleQuiz.id === quizId) {
        return module.moduleQuiz;
      }
      
      for (const chapter of module.chapters) {
        if (type === 'chapter' && chapter.quiz.id === quizId) {
          return chapter.quiz;
        }
      }
    }
    
    return null;
  };

  return (
    <div className="w-full">
      <div className='grid grid-cols-1 gap-6 lg:grid-cols-5 min-w-0'>
        {/* Sidebar */}
        {!isLearningMode && (
          <div className='lg:col-span-2 min-w-0 overflow-y-auto'>
            <div className='w-full'>
              <TableOfContents
                course={courseData}
                onNavigate={(moduleId, chapterId, contentId) => {
                  handleModuleNavigation(moduleId);
                  onNavigateToSection(moduleId, chapterId, contentId);
                }}
                expandedModules={expandedModules}
                expandedChapters={expandedChapters}
                onToggleModule={onToggleModule}
                onToggleChapter={onToggleChapter}
                currentModuleIndex={currentModuleIndex}
              />
            </div>
          </div>
        )}

        {/* Main Content */}
        <div
          className={`${
            isLearningMode ? 'lg:col-span-5' : 'lg:col-span-3'
          } min-w-0`}
        >
          {showQuizIntro && currentQuiz ? (
            <QuizIntroduction
              quiz={currentQuiz}
              quizType={quizType}
              onStartQuiz={() => {
                setShowQuizIntro(false);
                onStartQuiz(currentQuiz.id);
              }}
              onCancel={() => {
                setShowQuizIntro(false);
                setCurrentQuiz(null);
              }}
            />
          ) : currentModuleId && currentChapterId && currentContentId ? (
            <ContentViewer
              course={courseData}
              currentModuleId={currentModuleId}
              currentChapterId={currentChapterId}
              currentContentId={currentContentId}
              onNavigate={onNavigateToSection}
              onStartQuiz={(quizId, type) => {
                const quiz = findQuizById(quizId, type);
                if (quiz) {
                  setCurrentQuiz(quiz);
                  setQuizType(type);
                  setShowQuizIntro(true);
                }
              }}
              onContentComplete={onToggleContentComplete}
            />
          ) : (
            <div className="flex items-center justify-center h-96">
              <div className="text-center">
                <BookMarked className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                <h3 className="text-lg font-semibold text-gray-900 mb-2">Pilih Konten</h3>
                <p className="text-gray-600">Silakan pilih konten dari navigasi di sebelah kiri untuk memulai pembelajaran.</p>
              </div>
            </div>
          )}
        </div>
      </div>
    </div>
  );
};
